"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Bell, BellOff, Smartphone, Check, X, Settings, Volume2 } from "lucide-react"

export default function PushNotifications() {
  const [permission, setPermission] = useState("default")
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [settings, setSettings] = useState({
    tickets: true,
    payroll: true,
    projects: false,
    analytics: true,
    system: true,
    marketing: false,
  })
  const [testNotification, setTestNotification] = useState(false)

  useEffect(() => {
    // Check current notification permission
    if ("Notification" in window) {
      setPermission(Notification.permission)
    }

    // Check if user is subscribed to push notifications
    if ("serviceWorker" in navigator && "PushManager" in window) {
      navigator.serviceWorker.ready.then((registration) => {
        registration.pushManager.getSubscription().then((subscription) => {
          setIsSubscribed(!!subscription)
        })
      })
    }
  }, [])

  const requestPermission = async () => {
    if ("Notification" in window) {
      const result = await Notification.requestPermission()
      setPermission(result)

      if (result === "granted") {
        await subscribeToPush()
      }
    }
  }

  const subscribeToPush = async () => {
    if ("serviceWorker" in navigator && "PushManager" in window) {
      try {
        const registration = await navigator.serviceWorker.ready
        const subscription = await registration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: urlBase64ToUint8Array("YOUR_VAPID_PUBLIC_KEY"), // Replace with actual VAPID key
        })

        // Send subscription to server
        await fetch("/api/push/subscribe", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(subscription),
        })

        setIsSubscribed(true)
      } catch (error) {
        console.error("Error subscribing to push notifications:", error)
      }
    }
  }

  const unsubscribeFromPush = async () => {
    if ("serviceWorker" in navigator) {
      try {
        const registration = await navigator.serviceWorker.ready
        const subscription = await registration.pushManager.getSubscription()

        if (subscription) {
          await subscription.unsubscribe()

          // Remove subscription from server
          await fetch("/api/push/unsubscribe", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ endpoint: subscription.endpoint }),
          })
        }

        setIsSubscribed(false)
      } catch (error) {
        console.error("Error unsubscribing from push notifications:", error)
      }
    }
  }

  const sendTestNotification = async () => {
    setTestNotification(true)

    if (permission === "granted") {
      // Send test notification via service worker
      if ("serviceWorker" in navigator) {
        const registration = await navigator.serviceWorker.ready
        registration.showNotification("SaaS Platform Test", {
          body: "This is a test notification from your mobile dashboard!",
          icon: "/icon-192x192.png",
          badge: "/badge-72x72.png",
          vibrate: [100, 50, 100],
          data: {
            dateOfArrival: Date.now(),
            primaryKey: "test-notification",
          },
          actions: [
            {
              action: "explore",
              title: "View Dashboard",
              icon: "/action-explore.png",
            },
            {
              action: "close",
              title: "Close",
              icon: "/action-close.png",
            },
          ],
        })
      }
    }

    setTimeout(() => setTestNotification(false), 2000)
  }

  const updateNotificationSetting = (key, value) => {
    setSettings((prev) => ({
      ...prev,
      [key]: value,
    }))

    // Send updated settings to server
    fetch("/api/notifications/settings", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ [key]: value }),
    })
  }

  // Helper function to convert VAPID key
  function urlBase64ToUint8Array(base64String) {
    const padding = "=".repeat((4 - (base64String.length % 4)) % 4)
    const base64 = (base64String + padding).replace(/-/g, "+").replace(/_/g, "/")

    const rawData = window.atob(base64)
    const outputArray = new Uint8Array(rawData.length)

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    return outputArray
  }

  const getPermissionStatus = () => {
    switch (permission) {
      case "granted":
        return { color: "text-green-600", bg: "bg-green-100", text: "Enabled" }
      case "denied":
        return { color: "text-red-600", bg: "bg-red-100", text: "Blocked" }
      default:
        return { color: "text-yellow-600", bg: "bg-yellow-100", text: "Not Set" }
    }
  }

  const status = getPermissionStatus()

  return (
    <div className="space-y-6">
      {/* Permission Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Smartphone className="h-5 w-5 mr-2 text-blue-600" />
            Push Notifications
          </CardTitle>
          <CardDescription>Stay updated with real-time notifications on your mobile device</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${status.bg}`}>
                {permission === "granted" ? (
                  <Bell className={`h-5 w-5 ${status.color}`} />
                ) : (
                  <BellOff className={`h-5 w-5 ${status.color}`} />
                )}
              </div>
              <div>
                <p className="font-medium text-gray-900">Notification Status</p>
                <p className="text-sm text-gray-600">
                  {permission === "granted" ? "Notifications are enabled" : "Notifications need permission"}
                </p>
              </div>
            </div>
            <Badge className={`${status.bg} ${status.color} border-0`}>{status.text}</Badge>
          </div>

          {permission !== "granted" && (
            <Alert className="mb-4">
              <Bell className="h-4 w-4" />
              <AlertDescription>
                Enable push notifications to receive real-time updates about your business activities.
              </AlertDescription>
            </Alert>
          )}

          <div className="flex flex-col space-y-3">
            {permission !== "granted" ? (
              <Button onClick={requestPermission} className="w-full">
                <Bell className="h-4 w-4 mr-2" />
                Enable Push Notifications
              </Button>
            ) : (
              <div className="flex space-x-2">
                <Button
                  onClick={sendTestNotification}
                  disabled={testNotification}
                  className="flex-1 bg-transparent"
                  variant="outline"
                >
                  {testNotification ? (
                    <>
                      <Volume2 className="h-4 w-4 mr-2 animate-pulse" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Bell className="h-4 w-4 mr-2" />
                      Test Notification
                    </>
                  )}
                </Button>
                {isSubscribed ? (
                  <Button onClick={unsubscribeFromPush} variant="outline" className="flex-1 bg-transparent">
                    <BellOff className="h-4 w-4 mr-2" />
                    Unsubscribe
                  </Button>
                ) : (
                  <Button onClick={subscribeToPush} className="flex-1">
                    <Bell className="h-4 w-4 mr-2" />
                    Subscribe
                  </Button>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Notification Settings */}
      {permission === "granted" && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2 text-gray-600" />
              Notification Preferences
            </CardTitle>
            <CardDescription>Choose which types of notifications you want to receive</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(settings).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between py-2">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${value ? "bg-blue-100" : "bg-gray-100"}`}>
                      <Bell className={`h-4 w-4 ${value ? "text-blue-600" : "text-gray-400"}`} />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 capitalize">{key} Notifications</p>
                      <p className="text-sm text-gray-600">
                        {key === "tickets" && "New tickets and status updates"}
                        {key === "payroll" && "Payroll processing and payments"}
                        {key === "projects" && "Project milestones and deadlines"}
                        {key === "analytics" && "Performance insights and reports"}
                        {key === "system" && "System maintenance and updates"}
                        {key === "marketing" && "Marketing campaigns and promotions"}
                      </p>
                    </div>
                  </div>
                  <Switch checked={value} onCheckedChange={(checked) => updateNotificationSetting(key, checked)} />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Browser Support Info */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm text-gray-600">Browser Support</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              {"Notification" in window ? (
                <Check className="h-4 w-4 text-green-600" />
              ) : (
                <X className="h-4 w-4 text-red-600" />
              )}
              <span>Notifications API</span>
            </div>
            <div className="flex items-center space-x-2">
              {"serviceWorker" in navigator ? (
                <Check className="h-4 w-4 text-green-600" />
              ) : (
                <X className="h-4 w-4 text-red-600" />
              )}
              <span>Service Worker</span>
            </div>
            <div className="flex items-center space-x-2">
              {"PushManager" in window ? (
                <Check className="h-4 w-4 text-green-600" />
              ) : (
                <X className="h-4 w-4 text-red-600" />
              )}
              <span>Push Manager</span>
            </div>
            <div className="flex items-center space-x-2">
              {navigator.onLine ? <Check className="h-4 w-4 text-green-600" /> : <X className="h-4 w-4 text-red-600" />}
              <span>Online Status</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
