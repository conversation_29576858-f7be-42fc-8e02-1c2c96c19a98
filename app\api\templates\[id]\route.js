import { NextResponse } from "next/server"

// Mock applied templates storage
const appliedTemplates = [
  {
    id: "1",
    projectId: "proj-1",
    templateType: "project_type",
    templateKey: "software_development",
    templateName: "Software Development Project",
    appliedBy: "admin",
    appliedAt: "2024-01-15T10:30:00Z",
    configuration: {
      phases: ["CREATE", "SELECT", "PLAN", "MANAGE"],
      customSettings: {
        allowDeveloperApproval: false,
        requireTechnicalReview: true,
        enableAgileWorkflow: true,
        requireCodeReview: true,
      },
    },
  },
]

export async function GET(request, { params }) {
  try {
    const template = appliedTemplates.find((t) => t.id === params.id)

    if (!template) {
      return NextResponse.json({ success: false, error: "Template not found" }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: template,
    })
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to fetch template" }, { status: 500 })
  }
}

export async function PUT(request, { params }) {
  try {
    const body = await request.json()
    const templateIndex = appliedTemplates.findIndex((t) => t.id === params.id)

    if (templateIndex === -1) {
      return NextResponse.json({ success: false, error: "Template not found" }, { status: 404 })
    }

    const updatedTemplate = {
      ...appliedTemplates[templateIndex],
      ...body,
      updatedAt: new Date().toISOString(),
    }

    appliedTemplates[templateIndex] = updatedTemplate

    return NextResponse.json({
      success: true,
      data: updatedTemplate,
      message: "Template updated successfully",
    })
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to update template" }, { status: 500 })
  }
}

export async function DELETE(request, { params }) {
  try {
    const templateIndex = appliedTemplates.findIndex((t) => t.id === params.id)

    if (templateIndex === -1) {
      return NextResponse.json({ success: false, error: "Template not found" }, { status: 404 })
    }

    const deletedTemplate = appliedTemplates.splice(templateIndex, 1)[0]

    return NextResponse.json({
      success: true,
      data: deletedTemplate,
      message: "Template removed successfully",
    })
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to remove template" }, { status: 500 })
  }
}
