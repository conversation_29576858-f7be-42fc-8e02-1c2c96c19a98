"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  PROJECT_TYPE_TEMPLATES,
  ROLE_COMBINATION_TEMPLATES,
  getRecommendedTemplates,
  applyProjectTypeTemplate,
  applyRoleCombinationTemplate,
} from "@/lib/permission-templates"
import { getRoleDisplayName } from "@/lib/permissions"
import {
  LayoutTemplateIcon as Template,
  Zap,
  Users,
  Crown,
  Code,
  Briefcase,
  CheckCircle,
  Play,
  Eye,
  Shield,
  HardHat,
  Megaphone,
  Search,
  Star,
  Plus,
  Settings,
} from "lucide-react"

const iconMap = {
  Template,
  Users,
  Crown,
  Code,
  Briefcase,
  CheckCircle,
  Play,
  Eye,
  Shield,
  HardHat,
  Megaphone,
  Search,
}

export default function TemplateSelector({
  projectId,
  projectType,
  currentPhase = "CREATE",
  teamSize = 5,
  onTemplateApply,
  availableUsers = [],
}) {
  const [selectedProjectType, setSelectedProjectType] = useState(projectType || "")
  const [selectedUsers, setSelectedUsers] = useState([])
  const [recommendations, setRecommendations] = useState([])
  const [isApplyDialogOpen, setIsApplyDialogOpen] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [templateType, setTemplateType] = useState("")

  useEffect(() => {
    if (selectedProjectType) {
      const recs = getRecommendedTemplates(selectedProjectType, currentPhase, teamSize)
      setRecommendations(recs)
    }
  }, [selectedProjectType, currentPhase, teamSize])

  const handleApplyTemplate = async (template, type) => {
    try {
      let result
      if (type === "project_type") {
        result = applyProjectTypeTemplate(selectedProjectType, projectId)
      } else if (type === "role_combination") {
        result = applyRoleCombinationTemplate(template.key || template.name, selectedUsers, projectId)
      }

      onTemplateApply?.(result, type)
      setIsApplyDialogOpen(false)
      setSelectedUsers([])
    } catch (error) {
      console.error("Failed to apply template:", error)
    }
  }

  const getColorClasses = (color) => {
    const colorMap = {
      blue: "bg-blue-100 text-blue-800 border-blue-200",
      purple: "bg-purple-100 text-purple-800 border-purple-200",
      green: "bg-green-100 text-green-800 border-green-200",
      orange: "bg-orange-100 text-orange-800 border-orange-200",
      red: "bg-red-100 text-red-800 border-red-200",
      teal: "bg-teal-100 text-teal-800 border-teal-200",
      gray: "bg-gray-100 text-gray-800 border-gray-200",
      yellow: "bg-yellow-100 text-yellow-800 border-yellow-200",
      pink: "bg-pink-100 text-pink-800 border-pink-200",
    }
    return colorMap[color] || colorMap.gray
  }

  const renderIcon = (iconName, className = "h-5 w-5") => {
    const IconComponent = iconMap[iconName] || Template
    return <IconComponent className={className} />
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Template className="h-5 w-5 mr-2 text-purple-600" />
                Permission Templates
              </CardTitle>
              <CardDescription>Apply pre-configured permission templates for common scenarios</CardDescription>
            </div>
            <div className="flex items-center space-x-3">
              <Select value={selectedProjectType} onValueChange={setSelectedProjectType}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Select project type" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(PROJECT_TYPE_TEMPLATES).map(([key, template]) => (
                    <SelectItem key={key} value={key}>
                      {template.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Badge variant="outline" className="bg-blue-50 text-blue-700">
                {currentPhase} Phase
              </Badge>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Recommendations */}
      {recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Zap className="h-5 w-5 mr-2 text-yellow-600" />
              Recommended Templates
            </CardTitle>
            <CardDescription>Templates optimized for your project type and current phase</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {recommendations.slice(0, 6).map((rec, index) => (
                <Card key={index} className="hover:shadow-md transition-shadow border-l-4 border-l-yellow-400">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        {renderIcon(rec.template.icon, "h-5 w-5 text-gray-600")}
                        <h3 className="font-semibold text-sm">{rec.template.name}</h3>
                      </div>
                      <Badge
                        className={`text-xs ${
                          rec.priority === "high"
                            ? "bg-red-100 text-red-800"
                            : rec.priority === "medium"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {rec.priority}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-600 mb-3">{rec.reason}</p>
                    <Button
                      size="sm"
                      className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600"
                      onClick={() => {
                        setSelectedTemplate(rec.template)
                        setTemplateType(rec.type)
                        setIsApplyDialogOpen(true)
                      }}
                    >
                      <Star className="h-3 w-3 mr-1" />
                      Apply Template
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Template Categories */}
      <Tabs defaultValue="project-types" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="project-types">Project Type Templates</TabsTrigger>
          <TabsTrigger value="role-combinations">Role Combination Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="project-types" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Object.entries(PROJECT_TYPE_TEMPLATES).map(([key, template]) => (
              <Card key={key} className={`hover:shadow-lg transition-all border-2 ${getColorClasses(template.color)}`}>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    {renderIcon(template.icon, "h-8 w-8")}
                    <div>
                      <h3 className="font-bold text-lg">{template.name}</h3>
                      <p className="text-sm opacity-80">{template.description}</p>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <Label className="text-sm font-medium">Phases Covered</Label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {Object.keys(template.phases).map((phase) => (
                          <Badge key={phase} variant="outline" className="text-xs">
                            {phase}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Key Features</Label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {Object.keys(template.customSettings).map((setting) => (
                          <Badge key={setting} variant="secondary" className="text-xs">
                            {setting.replace(/([A-Z])/g, " $1").toLowerCase()}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <Button
                      className="w-full mt-4"
                      onClick={() => {
                        setSelectedTemplate({ ...template, key })
                        setTemplateType("project_type")
                        setIsApplyDialogOpen(true)
                      }}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Apply Template
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="role-combinations" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Object.entries(ROLE_COMBINATION_TEMPLATES).map(([key, template]) => (
              <Card key={key} className={`hover:shadow-lg transition-all border-2 ${getColorClasses(template.color)}`}>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    {renderIcon(template.icon, "h-8 w-8")}
                    <div>
                      <h3 className="font-bold text-lg">{template.name}</h3>
                      <p className="text-sm opacity-80">{template.description}</p>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <Label className="text-sm font-medium">Included Roles</Label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {template.roles.map((role) => (
                          <Badge key={role} variant="outline" className="text-xs">
                            {getRoleDisplayName(role)}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Applicable Phases</Label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {template.applicablePhases.map((phase) => (
                          <Badge key={phase} variant="secondary" className="text-xs">
                            {phase}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Permissions</Label>
                      <p className="text-xs text-gray-600 mt-1">{template.permissions.length} permissions included</p>
                    </div>

                    <Button
                      className="w-full mt-4"
                      onClick={() => {
                        setSelectedTemplate({ ...template, key })
                        setTemplateType("role_combination")
                        setIsApplyDialogOpen(true)
                      }}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Apply Template
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Apply Template Dialog */}
      <Dialog open={isApplyDialogOpen} onOpenChange={setIsApplyDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              {selectedTemplate && renderIcon(selectedTemplate.icon, "h-5 w-5 mr-2")}
              Apply Template: {selectedTemplate?.name}
            </DialogTitle>
            <DialogDescription>{selectedTemplate?.description}</DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {templateType === "role_combination" && (
              <div>
                <Label className="text-sm font-medium mb-3 block">Select Users to Assign Roles</Label>
                <div className="grid grid-cols-2 gap-3 max-h-60 overflow-y-auto">
                  {availableUsers.map((user) => (
                    <div key={user.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={user.id}
                        checked={selectedUsers.includes(user.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedUsers([...selectedUsers, user.id])
                          } else {
                            setSelectedUsers(selectedUsers.filter((id) => id !== user.id))
                          }
                        }}
                      />
                      <Label htmlFor={user.id} className="text-sm flex items-center space-x-2">
                        <span>{user.name}</span>
                        <Badge variant="outline" className="text-xs">
                          {getRoleDisplayName(user.role)}
                        </Badge>
                      </Label>
                    </div>
                  ))}
                </div>
                {selectedUsers.length === 0 && (
                  <p className="text-sm text-red-600 mt-2">Please select at least one user to apply the template.</p>
                )}
              </div>
            )}

            {selectedTemplate && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Template Preview</h4>
                <div className="space-y-2 text-sm">
                  {templateType === "project_type" && (
                    <>
                      <div>
                        <span className="font-medium">Phases: </span>
                        {Object.keys(selectedTemplate.phases || {}).join(", ")}
                      </div>
                      <div>
                        <span className="font-medium">Custom Settings: </span>
                        {Object.keys(selectedTemplate.customSettings || {}).length} configurations
                      </div>
                    </>
                  )}
                  {templateType === "role_combination" && (
                    <>
                      <div>
                        <span className="font-medium">Roles: </span>
                        {selectedTemplate.roles?.map((role) => getRoleDisplayName(role)).join(", ")}
                      </div>
                      <div>
                        <span className="font-medium">Permissions: </span>
                        {selectedTemplate.permissions?.length} permissions
                      </div>
                      <div>
                        <span className="font-medium">Applicable Phases: </span>
                        {selectedTemplate.applicablePhases?.join(", ")}
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsApplyDialogOpen(false)}>
              Cancel
            </Button>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  disabled={templateType === "role_combination" && selectedUsers.length === 0}
                  className="bg-gradient-to-r from-purple-600 to-blue-600"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Apply Template
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Confirm Template Application</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will apply the selected template and may modify existing permissions. This action cannot be
                    undone. Are you sure you want to continue?
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={() => handleApplyTemplate(selectedTemplate, templateType)}>
                    Apply Template
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
