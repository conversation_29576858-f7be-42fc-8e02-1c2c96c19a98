"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Users,
  UserCheck,
  TrendingUp,
  ShoppingCart,
  Package,
  DollarSign,
  FolderOpen,
  Settings,
  BarChart3,
  Shield,
  Clock,
} from "lucide-react"
import EmployeeManagement from "@/components/modules/hrms/employee-management"
import LeadManagement from "@/components/modules/crm/lead-management"

const erpModules = [
  {
    id: "hrms",
    name: "HRMS",
    description: "Human Resource Management System",
    icon: Users,
    subModules: ["Employee Master", "Attendance", "Payroll", "Leave", "Appraisal"],
    count: 245,
    color: "bg-blue-500",
    status: "active",
  },
  {
    id: "crm",
    name: "CRM",
    description: "Customer Relationship Management",
    icon: UserCheck,
    subModules: ["Leads", "Opportunities", "Contacts", "Activities"],
    count: 1250,
    color: "bg-purple-500",
    status: "active",
  },
  {
    id: "sales",
    name: "Sales & Distribution",
    description: "Sales order and distribution management",
    icon: TrendingUp,
    subModules: ["Sales Orders", "Quotations", "Delivery Challan", "Invoices"],
    count: 890,
    color: "bg-green-500",
    status: "active",
  },
  {
    id: "procurement",
    name: "Procurement",
    description: "Purchase and vendor management",
    icon: ShoppingCart,
    subModules: ["Purchase Orders", "Vendors", "GRN", "Invoices"],
    count: 456,
    color: "bg-orange-500",
    status: "active",
  },
  {
    id: "inventory",
    name: "Inventory Management",
    description: "Stock and warehouse management",
    icon: Package,
    subModules: ["Items", "Bins", "Stock Levels", "Warehouses", "Stock Transfers"],
    count: 2340,
    color: "bg-teal-500",
    status: "active",
  },
  {
    id: "finance",
    name: "Finance & Accounting",
    description: "Financial management and accounting",
    icon: DollarSign,
    subModules: ["Journal Entries", "Payments", "Receipts", "Chart of Accounts"],
    count: 1890,
    color: "bg-emerald-500",
    status: "active",
  },
  {
    id: "projects",
    name: "Project Management",
    description: "Project planning and execution",
    icon: FolderOpen,
    subModules: ["Projects", "Tasks", "Milestones", "Timesheets"],
    count: 78,
    color: "bg-indigo-500",
    status: "active",
  },
  {
    id: "production",
    name: "Production",
    description: "Manufacturing and production planning",
    icon: Settings,
    subModules: ["BOM", "Work Orders", "Production Planning"],
    count: 234,
    color: "bg-red-500",
    status: "active",
  },
  {
    id: "assets",
    name: "Asset Management",
    description: "Asset tracking and depreciation",
    icon: BarChart3,
    subModules: ["Asset Register", "Depreciation", "Transfers"],
    count: 156,
    color: "bg-yellow-500",
    status: "active",
  },
  {
    id: "quality",
    name: "Quality Management",
    description: "Quality control and inspections",
    icon: Shield,
    subModules: ["Inspections", "Quality Plans", "Defects"],
    count: 89,
    color: "bg-pink-500",
    status: "active",
  },
]

export default function ModulesPage() {
  const [selectedModule, setSelectedModule] = useState(null)

  if (selectedModule === "hrms") {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => setSelectedModule(null)}>
            ← Back to Modules
          </Button>
        </div>
        <EmployeeManagement />
      </div>
    )
  }

  if (selectedModule === "crm") {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => setSelectedModule(null)}>
            ← Back to Modules
          </Button>
        </div>
        <LeadManagement />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">ERP Modules</h1>
          <p className="text-gray-600">Comprehensive business management modules</p>
        </div>
        <div className="flex items-center space-x-4">
          <Badge variant="outline" className="text-sm">
            {erpModules.length} Active Modules
          </Badge>
          <Badge className="bg-green-100 text-green-800">
            <Clock className="h-3 w-3 mr-1" />
            All Systems Operational
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {erpModules.map((module) => {
          const IconComponent = module.icon
          return (
            <Card
              key={module.id}
              className="cursor-pointer hover:shadow-lg transition-all duration-200 border-l-4 hover:scale-105"
              style={{ borderLeftColor: module.color.replace("bg-", "#") }}
              onClick={() => setSelectedModule(module.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className={`p-3 rounded-lg ${module.color} text-white`}>
                    <IconComponent className="h-6 w-6" />
                  </div>
                  <div className="text-right">
                    <Badge variant="secondary" className="text-xs">
                      {module.count.toLocaleString()}
                    </Badge>
                    <div className="text-xs text-gray-500 mt-1">Records</div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <CardTitle className="text-xl mb-2">{module.name}</CardTitle>
                <p className="text-sm text-gray-600 mb-4">{module.description}</p>
                <div className="space-y-2">
                  <div className="text-xs font-medium text-gray-700">Sub-modules:</div>
                  <div className="flex flex-wrap gap-1">
                    {module.subModules.slice(0, 3).map((subModule, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {subModule}
                      </Badge>
                    ))}
                    {module.subModules.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{module.subModules.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Module Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total Records</span>
                <span className="font-bold text-lg">
                  {erpModules.reduce((sum, module) => sum + module.count, 0).toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Active Modules</span>
                <span className="font-bold text-lg text-green-600">{erpModules.length}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">System Health</span>
                <Badge className="bg-green-100 text-green-800">Excellent</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div className="text-sm">
                  <div className="font-medium">New employee added</div>
                  <div className="text-gray-500">2 minutes ago</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <div className="text-sm">
                  <div className="font-medium">Lead converted</div>
                  <div className="text-gray-500">15 minutes ago</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div className="text-sm">
                  <div className="font-medium">Sales order created</div>
                  <div className="text-gray-500">1 hour ago</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Button variant="outline" className="w-full justify-start bg-transparent">
                <Users className="h-4 w-4 mr-2" />
                Add Employee
              </Button>
              <Button variant="outline" className="w-full justify-start bg-transparent">
                <UserCheck className="h-4 w-4 mr-2" />
                Create Lead
              </Button>
              <Button variant="outline" className="w-full justify-start bg-transparent">
                <TrendingUp className="h-4 w-4 mr-2" />
                New Sales Order
              </Button>
              <Button variant="outline" className="w-full justify-start bg-transparent">
                <Package className="h-4 w-4 mr-2" />
                Stock Transfer
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
