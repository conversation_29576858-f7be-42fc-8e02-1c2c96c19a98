"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Bell,
  Mail,
  MessageSquare,
  Smartphone,
  Clock,
  CheckCircle,
  XCircle,
  ArrowRightLeft,
  AlertTriangle,
  Settings,
} from "lucide-react"

export default function AssetTransferNotifications() {
  const [notifications, setNotifications] = useState([
    {
      id: "notif-001",
      type: "approval_required",
      title: "Transfer Approval Required",
      message: "MacBook Pro 16-inch transfer from <PERSON> to <PERSON> requires your approval",
      transferId: "TRF-001",
      priority: "normal",
      timestamp: "2024-01-16 2:16 PM",
      isRead: false,
      channels: ["email", "in_app"],
    },
    {
      id: "notif-002",
      type: "transfer_approved",
      title: "Transfer Approved",
      message: "Your transfer request for Office Printer HP LaserJet has been approved",
      transferId: "TRF-002",
      priority: "normal",
      timestamp: "2024-01-16 1:45 PM",
      isRead: true,
      channels: ["email", "in_app", "sms"],
    },
    {
      id: "notif-003",
      type: "transfer_overdue",
      title: "Transfer Overdue",
      message: "Conference Room Table transfer is overdue for completion",
      transferId: "TRF-003",
      priority: "high",
      timestamp: "2024-01-16 9:00 AM",
      isRead: false,
      channels: ["email", "in_app", "push"],
    },
    {
      id: "notif-004",
      type: "transfer_completed",
      title: "Transfer Completed",
      message: "Company Vehicle - Toyota Camry has been successfully transferred",
      transferId: "TRF-004",
      priority: "low",
      timestamp: "2024-01-15 4:30 PM",
      isRead: true,
      channels: ["email", "in_app"],
    },
    {
      id: "notif-005",
      type: "transfer_rejected",
      title: "Transfer Rejected",
      message: "Industrial 3D Printer transfer has been rejected by Production Manager",
      transferId: "TRF-005",
      priority: "high",
      timestamp: "2024-01-15 11:45 AM",
      isRead: false,
      channels: ["email", "in_app", "sms"],
    },
  ])

  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    inAppNotifications: true,
    approvalReminders: true,
    overdueAlerts: true,
    completionUpdates: true,
    rejectionNotifications: true,
    reminderInterval: "24", // hours
    quietHours: {
      enabled: false,
      start: "22:00",
      end: "08:00",
    },
  })

  const getNotificationIcon = (type) => {
    switch (type) {
      case "approval_required":
        return <Clock className="h-5 w-5 text-orange-600" />
      case "transfer_approved":
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case "transfer_rejected":
        return <XCircle className="h-5 w-5 text-red-600" />
      case "transfer_completed":
        return <CheckCircle className="h-5 w-5 text-blue-600" />
      case "transfer_overdue":
        return <AlertTriangle className="h-5 w-5 text-red-600" />
      default:
        return <ArrowRightLeft className="h-5 w-5 text-gray-600" />
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800"
      case "normal":
        return "bg-blue-100 text-blue-800"
      case "low":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getChannelIcon = (channel) => {
    switch (channel) {
      case "email":
        return <Mail className="h-4 w-4" />
      case "sms":
        return <MessageSquare className="h-4 w-4" />
      case "push":
        return <Smartphone className="h-4 w-4" />
      case "in_app":
        return <Bell className="h-4 w-4" />
      default:
        return <Bell className="h-4 w-4" />
    }
  }

  const markAsRead = (notificationId) => {
    setNotifications((prev) => prev.map((notif) => (notif.id === notificationId ? { ...notif, isRead: true } : notif)))
  }

  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((notif) => ({ ...notif, isRead: true })))
  }

  const updateNotificationSettings = (key, value) => {
    setNotificationSettings((prev) => ({
      ...prev,
      [key]: value,
    }))
  }

  const unreadCount = notifications.filter((n) => !n.isRead).length

  return (
    <div className="space-y-6">
      {/* Notification Center */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Bell className="h-5 w-5 mr-2 text-indigo-600" />
                Transfer Notifications
                {unreadCount > 0 && <Badge className="ml-2 bg-red-500 hover:bg-red-500">{unreadCount}</Badge>}
              </CardTitle>
              <CardDescription>Stay updated on asset transfer activities and approvals</CardDescription>
            </div>
            <Button variant="outline" onClick={markAllAsRead} disabled={unreadCount === 0}>
              Mark All Read
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 border rounded-lg transition-colors ${
                  notification.isRead ? "bg-white" : "bg-blue-50 border-blue-200"
                }`}
              >
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 mt-1">{getNotificationIcon(notification.type)}</div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium text-gray-900">{notification.title}</h4>
                      <div className="flex items-center space-x-2">
                        <Badge className={getPriorityColor(notification.priority)} variant="outline">
                          {notification.priority.toUpperCase()}
                        </Badge>
                        {!notification.isRead && <div className="w-2 h-2 bg-blue-600 rounded-full"></div>}
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{notification.message}</p>
                    <div className="flex items-center justify-between mt-3">
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500">Transfer ID: {notification.transferId}</span>
                        <span className="text-xs text-gray-400">•</span>
                        <span className="text-xs text-gray-500">{notification.timestamp}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        {notification.channels.map((channel) => (
                          <div
                            key={channel}
                            className="p-1 bg-gray-100 rounded text-gray-600"
                            title={`Sent via ${channel}`}
                          >
                            {getChannelIcon(channel)}
                          </div>
                        ))}
                      </div>
                    </div>
                    {!notification.isRead && (
                      <div className="mt-3">
                        <Button variant="outline" size="sm" onClick={() => markAsRead(notification.id)}>
                          Mark as Read
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Notification Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2 text-indigo-600" />
            Notification Settings
          </CardTitle>
          <CardDescription>Configure how and when you receive transfer notifications</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Notification Channels */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-4">Notification Channels</h4>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-gray-600" />
                  <div>
                    <Label htmlFor="email-notifications">Email Notifications</Label>
                    <p className="text-sm text-gray-500">Receive notifications via email</p>
                  </div>
                </div>
                <Switch
                  id="email-notifications"
                  checked={notificationSettings.emailNotifications}
                  onCheckedChange={(checked) => updateNotificationSettings("emailNotifications", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <MessageSquare className="h-4 w-4 text-gray-600" />
                  <div>
                    <Label htmlFor="sms-notifications">SMS Notifications</Label>
                    <p className="text-sm text-gray-500">Receive urgent notifications via SMS</p>
                  </div>
                </div>
                <Switch
                  id="sms-notifications"
                  checked={notificationSettings.smsNotifications}
                  onCheckedChange={(checked) => updateNotificationSettings("smsNotifications", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Smartphone className="h-4 w-4 text-gray-600" />
                  <div>
                    <Label htmlFor="push-notifications">Push Notifications</Label>
                    <p className="text-sm text-gray-500">Receive push notifications on mobile devices</p>
                  </div>
                </div>
                <Switch
                  id="push-notifications"
                  checked={notificationSettings.pushNotifications}
                  onCheckedChange={(checked) => updateNotificationSettings("pushNotifications", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Bell className="h-4 w-4 text-gray-600" />
                  <div>
                    <Label htmlFor="in-app-notifications">In-App Notifications</Label>
                    <p className="text-sm text-gray-500">Show notifications within the application</p>
                  </div>
                </div>
                <Switch
                  id="in-app-notifications"
                  checked={notificationSettings.inAppNotifications}
                  onCheckedChange={(checked) => updateNotificationSettings("inAppNotifications", checked)}
                />
              </div>
            </div>
          </div>

          {/* Notification Types */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-4">Notification Types</h4>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="approval-reminders">Approval Reminders</Label>
                  <p className="text-sm text-gray-500">Reminders for pending approvals</p>
                </div>
                <Switch
                  id="approval-reminders"
                  checked={notificationSettings.approvalReminders}
                  onCheckedChange={(checked) => updateNotificationSettings("approvalReminders", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="overdue-alerts">Overdue Alerts</Label>
                  <p className="text-sm text-gray-500">Alerts for overdue transfers</p>
                </div>
                <Switch
                  id="overdue-alerts"
                  checked={notificationSettings.overdueAlerts}
                  onCheckedChange={(checked) => updateNotificationSettings("overdueAlerts", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="completion-updates">Completion Updates</Label>
                  <p className="text-sm text-gray-500">Updates when transfers are completed</p>
                </div>
                <Switch
                  id="completion-updates"
                  checked={notificationSettings.completionUpdates}
                  onCheckedChange={(checked) => updateNotificationSettings("completionUpdates", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="rejection-notifications">Rejection Notifications</Label>
                  <p className="text-sm text-gray-500">Notifications when transfers are rejected</p>
                </div>
                <Switch
                  id="rejection-notifications"
                  checked={notificationSettings.rejectionNotifications}
                  onCheckedChange={(checked) => updateNotificationSettings("rejectionNotifications", checked)}
                />
              </div>
            </div>
          </div>

          {/* Reminder Settings */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-4">Reminder Settings</h4>
            <div className="space-y-4">
              <div>
                <Label htmlFor="reminder-interval">Reminder Interval</Label>
                <Select
                  value={notificationSettings.reminderInterval}
                  onValueChange={(value) => updateNotificationSettings("reminderInterval", value)}
                >
                  <SelectTrigger className="w-full mt-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="2">Every 2 hours</SelectItem>
                    <SelectItem value="6">Every 6 hours</SelectItem>
                    <SelectItem value="12">Every 12 hours</SelectItem>
                    <SelectItem value="24">Daily</SelectItem>
                    <SelectItem value="48">Every 2 days</SelectItem>
                    <SelectItem value="72">Every 3 days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="quiet-hours">Quiet Hours</Label>
                  <p className="text-sm text-gray-500">Disable notifications during specified hours</p>
                </div>
                <Switch
                  id="quiet-hours"
                  checked={notificationSettings.quietHours.enabled}
                  onCheckedChange={(checked) =>
                    updateNotificationSettings("quietHours", {
                      ...notificationSettings.quietHours,
                      enabled: checked,
                    })
                  }
                />
              </div>
              {notificationSettings.quietHours.enabled && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="quiet-start">Start Time</Label>
                    <input
                      type="time"
                      id="quiet-start"
                      value={notificationSettings.quietHours.start}
                      onChange={(e) =>
                        updateNotificationSettings("quietHours", {
                          ...notificationSettings.quietHours,
                          start: e.target.value,
                        })
                      }
                      className="mt-2 w-full px-3 py-2 border border-gray-300 rounded-md"
                    />
                  </div>
                  <div>
                    <Label htmlFor="quiet-end">End Time</Label>
                    <input
                      type="time"
                      id="quiet-end"
                      value={notificationSettings.quietHours.end}
                      onChange={(e) =>
                        updateNotificationSettings("quietHours", {
                          ...notificationSettings.quietHours,
                          end: e.target.value,
                        })
                      }
                      className="mt-2 w-full px-3 py-2 border border-gray-300 rounded-md"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
