"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
} from "recharts"
import {
  Download,
  Share,
  Edit,
  Calendar,
  Clock,
  Users,
  DollarSign,
  TrendingUp,
  BarChart3,
  FileText,
  Mail,
  Printer,
  RefreshCw,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import { useParams } from "next/navigation"

export default function ReportDetailPage() {
  const params = useParams()
  const [reportData, setReportData] = useState(null)
  const [timeRange, setTimeRange] = useState("30d")
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Mock report data based on ID
    const mockReportData = {
      1: {
        id: "1",
        name: "Executive Summary",
        description: "High-level overview of all modules for leadership",
        type: "executive",
        frequency: "weekly",
        lastRun: "2024-01-15",
        nextRun: "2024-01-22",
        modules: ["all"],
        data: {
          summary: {
            totalRevenue: 485230,
            totalUsers: 2847,
            activeProjects: 24,
            completionRate: 94.2,
          },
          trends: [
            { month: "Oct", revenue: 420000, users: 2650, projects: 22 },
            { month: "Nov", revenue: 445000, users: 2720, projects: 23 },
            { month: "Dec", revenue: 465000, users: 2780, projects: 24 },
            { month: "Jan", revenue: 485230, users: 2847, projects: 24 },
          ],
          moduleBreakdown: [
            { module: "Payroll", revenue: 180000, efficiency: 99.2 },
            { module: "Consultations", revenue: 85000, efficiency: 94.2 },
            { module: "Projects", revenue: 120000, efficiency: 87.0 },
            { module: "CRM", revenue: 100230, efficiency: 91.5 },
          ],
        },
      },
      2: {
        id: "2",
        name: "HR Analytics",
        description: "Comprehensive HR metrics including payroll and user management",
        type: "hr",
        frequency: "monthly",
        lastRun: "2024-01-10",
        nextRun: "2024-02-10",
        modules: ["users", "payroll"],
        data: {
          summary: {
            totalEmployees: 247,
            payrollCost: 485230,
            avgSalary: 65000,
            turnoverRate: 8.5,
          },
          departmentData: [
            { name: "Engineering", employees: 85, cost: 170000 },
            { name: "Sales", employees: 62, cost: 124000 },
            { name: "Marketing", employees: 45, cost: 90000 },
            { name: "HR", employees: 30, cost: 60000 },
            { name: "Operations", employees: 25, cost: 41230 },
          ],
          payrollTrends: [
            { month: "Oct", cost: 465000, employees: 235 },
            { month: "Nov", cost: 472000, employees: 240 },
            { month: "Dec", cost: 478000, employees: 245 },
            { month: "Jan", cost: 485230, employees: 247 },
          ],
        },
      },
      3: {
        id: "3",
        name: "Customer Success Report",
        description: "CRM and consultation performance analysis",
        type: "customer",
        frequency: "weekly",
        lastRun: "2024-01-12",
        nextRun: "2024-01-19",
        modules: ["crm", "consultations"],
        data: {
          summary: {
            totalLeads: 1247,
            convertedLeads: 342,
            conversionRate: 27.4,
            consultationRevenue: 85000,
          },
          leadSources: [
            { source: "Website", leads: 435, converted: 120 },
            { source: "Referrals", leads: 312, converted: 95 },
            { source: "Social Media", leads: 280, converted: 78 },
            { source: "Email", leads: 220, converted: 49 },
          ],
          consultationMetrics: [
            { month: "Oct", appointments: 145, revenue: 72000 },
            { month: "Nov", appointments: 152, revenue: 76000 },
            { month: "Dec", appointments: 148, revenue: 78000 },
            { month: "Jan", appointments: 156, revenue: 85000 },
          ],
        },
      },
    }

    setTimeout(() => {
      setReportData(mockReportData[params.id] || null)
      setIsLoading(false)
    }, 1000)
  }, [params.id])

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const getReportIcon = (type) => {
    switch (type) {
      case "executive":
        return <BarChart3 className="h-5 w-5 text-blue-600" />
      case "hr":
        return <Users className="h-5 w-5 text-green-600" />
      case "customer":
        return <TrendingUp className="h-5 w-5 text-purple-600" />
      default:
        return <FileText className="h-5 w-5 text-gray-600" />
    }
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="h-24 bg-gray-200 rounded"></div>
              ))}
            </div>
            <div className="h-96 bg-gray-200 rounded"></div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!reportData) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Report Not Found</h2>
            <p className="text-gray-600">The requested report could not be found.</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getReportIcon(reportData.type)}
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{reportData.name}</h1>
              <p className="text-gray-600 mt-1">{reportData.description}</p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline">
              <Share className="h-4 w-4 mr-2" />
              Share
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Report Metadata */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Last Run: {reportData.lastRun}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Next Run: {reportData.nextRun}</span>
                </div>
                <Badge variant="outline">{reportData.frequency}</Badge>
                <Badge variant="outline">
                  {reportData.modules.includes("all") ? "All Modules" : `${reportData.modules.length} Modules`}
                </Badge>
              </div>
              <div className="flex space-x-2">
                <Button size="sm" variant="ghost">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
                <Button size="sm" variant="ghost">
                  <Mail className="h-4 w-4 mr-2" />
                  Email
                </Button>
                <Button size="sm" variant="ghost">
                  <Printer className="h-4 w-4 mr-2" />
                  Print
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Report Content */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="details">Detailed Analysis</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="export">Export Options</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {reportData.type === "executive" && (
                <>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                          <p className="text-2xl font-bold text-gray-900">
                            {formatCurrency(reportData.data.summary.totalRevenue)}
                          </p>
                        </div>
                        <DollarSign className="h-8 w-8 text-green-600" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Total Users</p>
                          <p className="text-2xl font-bold text-gray-900">
                            {reportData.data.summary.totalUsers.toLocaleString()}
                          </p>
                        </div>
                        <Users className="h-8 w-8 text-blue-600" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Active Projects</p>
                          <p className="text-2xl font-bold text-gray-900">{reportData.data.summary.activeProjects}</p>
                        </div>
                        <BarChart3 className="h-8 w-8 text-purple-600" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Completion Rate</p>
                          <p className="text-2xl font-bold text-gray-900">{reportData.data.summary.completionRate}%</p>
                        </div>
                        <TrendingUp className="h-8 w-8 text-emerald-600" />
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}

              {reportData.type === "hr" && (
                <>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Total Employees</p>
                          <p className="text-2xl font-bold text-gray-900">{reportData.data.summary.totalEmployees}</p>
                        </div>
                        <Users className="h-8 w-8 text-blue-600" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Payroll Cost</p>
                          <p className="text-2xl font-bold text-gray-900">
                            {formatCurrency(reportData.data.summary.payrollCost)}
                          </p>
                        </div>
                        <DollarSign className="h-8 w-8 text-green-600" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Avg Salary</p>
                          <p className="text-2xl font-bold text-gray-900">
                            {formatCurrency(reportData.data.summary.avgSalary)}
                          </p>
                        </div>
                        <BarChart3 className="h-8 w-8 text-purple-600" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Turnover Rate</p>
                          <p className="text-2xl font-bold text-gray-900">{reportData.data.summary.turnoverRate}%</p>
                        </div>
                        <TrendingUp className="h-8 w-8 text-orange-600" />
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}

              {reportData.type === "customer" && (
                <>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Total Leads</p>
                          <p className="text-2xl font-bold text-gray-900">{reportData.data.summary.totalLeads}</p>
                        </div>
                        <Users className="h-8 w-8 text-blue-600" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Converted Leads</p>
                          <p className="text-2xl font-bold text-gray-900">{reportData.data.summary.convertedLeads}</p>
                        </div>
                        <TrendingUp className="h-8 w-8 text-green-600" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                          <p className="text-2xl font-bold text-gray-900">{reportData.data.summary.conversionRate}%</p>
                        </div>
                        <BarChart3 className="h-8 w-8 text-purple-600" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Consultation Revenue</p>
                          <p className="text-2xl font-bold text-gray-900">
                            {formatCurrency(reportData.data.summary.consultationRevenue)}
                          </p>
                        </div>
                        <DollarSign className="h-8 w-8 text-emerald-600" />
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {reportData.type === "executive" && (
                <>
                  <Card>
                    <CardHeader>
                      <CardTitle>Revenue Trends</CardTitle>
                      <CardDescription>Monthly revenue growth</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <AreaChart data={reportData.data.trends}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip formatter={(value) => formatCurrency(value)} />
                          <Area type="monotone" dataKey="revenue" stroke="#3B82F6" fill="#3B82F6" fillOpacity={0.6} />
                        </AreaChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader>
                      <CardTitle>Module Performance</CardTitle>
                      <CardDescription>Revenue by module</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={reportData.data.moduleBreakdown}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="module" />
                          <YAxis />
                          <Tooltip formatter={(value) => formatCurrency(value)} />
                          <Bar dataKey="revenue" fill="#10B981" />
                        </BarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </>
              )}

              {reportData.type === "hr" && (
                <>
                  <Card>
                    <CardHeader>
                      <CardTitle>Department Distribution</CardTitle>
                      <CardDescription>Employees by department</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <PieChart>
                          <Pie
                            data={reportData.data.departmentData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, value }) => `${name}: ${value}`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="employees"
                          >
                            {reportData.data.departmentData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={`hsl(${index * 72}, 70%, 50%)`} />
                            ))}
                          </Pie>
                          <Tooltip />
                        </PieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader>
                      <CardTitle>Payroll Trends</CardTitle>
                      <CardDescription>Monthly payroll costs</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <LineChart data={reportData.data.payrollTrends}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip formatter={(value) => formatCurrency(value)} />
                          <Line type="monotone" dataKey="cost" stroke="#F59E0B" strokeWidth={2} />
                        </LineChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </>
              )}

              {reportData.type === "customer" && (
                <>
                  <Card>
                    <CardHeader>
                      <CardTitle>Lead Sources</CardTitle>
                      <CardDescription>Lead generation by source</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={reportData.data.leadSources}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="source" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="leads" fill="#3B82F6" name="Total Leads" />
                          <Bar dataKey="converted" fill="#10B981" name="Converted" />
                        </BarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader>
                      <CardTitle>Consultation Revenue</CardTitle>
                      <CardDescription>Monthly consultation performance</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <AreaChart data={reportData.data.consultationMetrics}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip formatter={(value) => formatCurrency(value)} />
                          <Area type="monotone" dataKey="revenue" stroke="#8B5CF6" fill="#8B5CF6" fillOpacity={0.6} />
                        </AreaChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </>
              )}
            </div>
          </TabsContent>

          <TabsContent value="details" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Detailed Analysis</CardTitle>
                <CardDescription>In-depth breakdown of report metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {reportData.type === "executive" && (
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Module Performance Breakdown</h3>
                      <div className="space-y-3">
                        {reportData.data.moduleBreakdown.map((module, index) => (
                          <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                              <h4 className="font-medium">{module.module}</h4>
                              <p className="text-sm text-gray-600">Revenue: {formatCurrency(module.revenue)}</p>
                            </div>
                            <div className="text-right">
                              <div className="font-semibold">{module.efficiency}%</div>
                              <div className="text-sm text-gray-600">Efficiency</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {reportData.type === "hr" && (
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Department Cost Analysis</h3>
                      <div className="space-y-3">
                        {reportData.data.departmentData.map((dept, index) => (
                          <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                              <h4 className="font-medium">{dept.name}</h4>
                              <p className="text-sm text-gray-600">{dept.employees} employees</p>
                            </div>
                            <div className="text-right">
                              <div className="font-semibold">{formatCurrency(dept.cost)}</div>
                              <div className="text-sm text-gray-600">Monthly Cost</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {reportData.type === "customer" && (
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Lead Conversion Analysis</h3>
                      <div className="space-y-3">
                        {reportData.data.leadSources.map((source, index) => (
                          <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                              <h4 className="font-medium">{source.source}</h4>
                              <p className="text-sm text-gray-600">{source.leads} total leads</p>
                            </div>
                            <div className="text-right">
                              <div className="font-semibold">{source.converted} converted</div>
                              <div className="text-sm text-gray-600">
                                {Math.round((source.converted / source.leads) * 100)}% rate
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="trends" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Trend Analysis</CardTitle>
                <CardDescription>Historical trends and projections</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  {reportData.type === "executive" && (
                    <LineChart data={reportData.data.trends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line type="monotone" dataKey="revenue" stroke="#3B82F6" strokeWidth={2} name="Revenue" />
                      <Line type="monotone" dataKey="users" stroke="#10B981" strokeWidth={2} name="Users" />
                      <Line type="monotone" dataKey="projects" stroke="#F59E0B" strokeWidth={2} name="Projects" />
                    </LineChart>
                  )}
                  {reportData.type === "hr" && (
                    <LineChart data={reportData.data.payrollTrends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line type="monotone" dataKey="cost" stroke="#F59E0B" strokeWidth={2} name="Payroll Cost" />
                      <Line type="monotone" dataKey="employees" stroke="#10B981" strokeWidth={2} name="Employees" />
                    </LineChart>
                  )}
                  {reportData.type === "customer" && (
                    <LineChart data={reportData.data.consultationMetrics}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="appointments"
                        stroke="#8B5CF6"
                        strokeWidth={2}
                        name="Appointments"
                      />
                      <Line type="monotone" dataKey="revenue" stroke="#10B981" strokeWidth={2} name="Revenue" />
                    </LineChart>
                  )}
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="export" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Export Options</CardTitle>
                <CardDescription>Download or share this report in various formats</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <Button
                    className="h-20 flex flex-col items-center justify-center space-y-2 bg-transparent"
                    variant="outline"
                  >
                    <FileText className="h-6 w-6" />
                    <span>Export as PDF</span>
                  </Button>
                  <Button
                    className="h-20 flex flex-col items-center justify-center space-y-2 bg-transparent"
                    variant="outline"
                  >
                    <BarChart3 className="h-6 w-6" />
                    <span>Export as Excel</span>
                  </Button>
                  <Button
                    className="h-20 flex flex-col items-center justify-center space-y-2 bg-transparent"
                    variant="outline"
                  >
                    <Mail className="h-6 w-6" />
                    <span>Email Report</span>
                  </Button>
                  <Button
                    className="h-20 flex flex-col items-center justify-center space-y-2 bg-transparent"
                    variant="outline"
                  >
                    <Share className="h-6 w-6" />
                    <span>Share Link</span>
                  </Button>
                  <Button
                    className="h-20 flex flex-col items-center justify-center space-y-2 bg-transparent"
                    variant="outline"
                  >
                    <Printer className="h-6 w-6" />
                    <span>Print Report</span>
                  </Button>
                  <Button
                    className="h-20 flex flex-col items-center justify-center space-y-2 bg-transparent"
                    variant="outline"
                  >
                    <Calendar className="h-6 w-6" />
                    <span>Schedule Delivery</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
