"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Truck,
  Wrench,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Plus,
  Search,
  MoreHorizontal,
  Calendar,
  MapPin,
  Clock,
  DollarSign,
  Settings,
  Download,
  BarChart3,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"

export default function MachineryVehiclePage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [isAddMachineOpen, setIsAddMachineOpen] = useState(false)
  const [isScheduleMaintenanceOpen, setIsScheduleMaintenanceOpen] = useState(false)

  const machineryStats = [
    {
      title: "Total Equipment",
      value: "156",
      change: "+8 this month",
      icon: Truck,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Active Equipment",
      value: "142",
      change: "91% utilization",
      icon: TrendingUp,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Maintenance Due",
      value: "12",
      change: "This week",
      icon: Wrench,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
    {
      title: "Downtime Cost",
      value: "$24K",
      change: "-18% vs last month",
      icon: TrendingDown,
      color: "text-red-600",
      bgColor: "bg-red-100",
    },
  ]

  const equipment = [
    {
      id: "EQ001",
      name: "CAT 320 Excavator",
      type: "Heavy Machinery",
      model: "320GC",
      serialNumber: "CAT320001",
      status: "Active",
      condition: "Good",
      location: "Metro Tower Complex",
      operator: "John Smith",
      project: "Metro Tower Complex",
      purchaseDate: "2022-03-15",
      purchasePrice: 285000,
      currentValue: 220000,
      hoursWorked: 2847,
      fuelConsumption: 12.5,
      lastMaintenance: "2024-01-15",
      nextMaintenance: "2024-02-15",
      maintenanceCost: 15000,
      downtime: 24,
      efficiency: 92,
      utilization: 85,
      specifications: {
        enginePower: "122 HP",
        operatingWeight: "20.2 tons",
        bucketCapacity: "1.2 cubic meters",
        maxDigDepth: "6.5 meters",
      },
      maintenanceHistory: [
        {
          date: "2024-01-15",
          type: "Preventive",
          description: "Engine oil change and filter replacement",
          cost: 850,
          technician: "Mike Mechanic",
        },
        {
          date: "2023-12-10",
          type: "Repair",
          description: "Hydraulic pump replacement",
          cost: 3200,
          technician: "Sarah Tech",
        },
      ],
    },
    {
      id: "EQ002",
      name: "Liebherr Tower Crane",
      type: "Lifting Equipment",
      model: "280 EC-H 12",
      serialNumber: "LIE280001",
      status: "Active",
      condition: "Excellent",
      location: "Metro Tower Complex",
      operator: "David Crane",
      project: "Metro Tower Complex",
      purchaseDate: "2023-01-20",
      purchasePrice: 450000,
      currentValue: 380000,
      hoursWorked: 1245,
      fuelConsumption: 0,
      lastMaintenance: "2024-01-20",
      nextMaintenance: "2024-03-20",
      maintenanceCost: 8500,
      downtime: 12,
      efficiency: 96,
      utilization: 78,
      specifications: {
        maxLoad: "12 tons",
        jibLength: "60 meters",
        maxHeight: "200 meters",
        hoistSpeed: "120 m/min",
      },
      maintenanceHistory: [
        {
          date: "2024-01-20",
          type: "Preventive",
          description: "Wire rope inspection and lubrication",
          cost: 1200,
          technician: "Tom Crane",
        },
      ],
    },
    {
      id: "EQ003",
      name: "Volvo Dump Truck",
      type: "Transport Vehicle",
      model: "A40G",
      serialNumber: "VOL40001",
      status: "Maintenance",
      condition: "Fair",
      location: "Service Center",
      operator: "Robert Driver",
      project: "Green Valley Residential",
      purchaseDate: "2021-08-10",
      purchasePrice: 320000,
      currentValue: 185000,
      hoursWorked: 4250,
      fuelConsumption: 28.5,
      lastMaintenance: "2024-01-22",
      nextMaintenance: "2024-01-25",
      maintenanceCost: 22000,
      downtime: 48,
      efficiency: 78,
      utilization: 65,
      specifications: {
        payloadCapacity: "40 tons",
        enginePower: "402 HP",
        maxSpeed: "55 km/h",
        fuelCapacity: "400 liters",
      },
      maintenanceHistory: [
        {
          date: "2024-01-22",
          type: "Repair",
          description: "Transmission overhaul",
          cost: 8500,
          technician: "Alex Mechanic",
        },
        {
          date: "2024-01-10",
          type: "Preventive",
          description: "Brake system inspection",
          cost: 650,
          technician: "Mike Mechanic",
        },
      ],
    },
  ]

  const maintenanceSchedule = [
    {
      id: "MAINT001",
      equipment: "CAT 320 Excavator",
      type: "Preventive",
      scheduledDate: "2024-02-15",
      description: "500-hour service - Engine oil, filters, hydraulic fluid check",
      estimatedCost: 1200,
      estimatedDuration: 4,
      technician: "Mike Mechanic",
      status: "Scheduled",
      priority: "Medium",
    },
    {
      id: "MAINT002",
      equipment: "Volvo Dump Truck",
      type: "Repair",
      scheduledDate: "2024-01-25",
      description: "Complete transmission repair and testing",
      estimatedCost: 8500,
      estimatedDuration: 24,
      technician: "Alex Mechanic",
      status: "In Progress",
      priority: "High",
    },
    {
      id: "MAINT003",
      equipment: "Liebherr Tower Crane",
      type: "Inspection",
      scheduledDate: "2024-03-20",
      description: "Annual safety inspection and certification",
      estimatedCost: 2500,
      estimatedDuration: 8,
      technician: "Tom Crane",
      status: "Scheduled",
      priority: "Critical",
    },
  ]

  const usageAnalytics = [
    {
      equipment: "CAT 320 Excavator",
      dailyHours: 8.5,
      weeklyHours: 42.5,
      monthlyHours: 180,
      efficiency: 92,
      fuelConsumption: 12.5,
      costPerHour: 45,
      productivity: 95,
    },
    {
      equipment: "Liebherr Tower Crane",
      dailyHours: 6.2,
      weeklyHours: 31,
      monthlyHours: 124,
      efficiency: 96,
      fuelConsumption: 0,
      costPerHour: 85,
      productivity: 88,
    },
    {
      equipment: "Volvo Dump Truck",
      dailyHours: 5.8,
      weeklyHours: 29,
      monthlyHours: 116,
      efficiency: 78,
      fuelConsumption: 28.5,
      costPerHour: 35,
      productivity: 72,
    },
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case "Active":
      case "Scheduled":
        return "bg-green-100 text-green-800"
      case "Maintenance":
      case "In Progress":
        return "bg-yellow-100 text-yellow-800"
      case "Inactive":
      case "Breakdown":
        return "bg-red-100 text-red-800"
      case "Completed":
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getConditionColor = (condition) => {
    switch (condition) {
      case "Excellent":
        return "bg-green-100 text-green-800"
      case "Good":
        return "bg-blue-100 text-blue-800"
      case "Fair":
        return "bg-yellow-100 text-yellow-800"
      case "Poor":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "Critical":
        return "bg-red-100 text-red-800"
      case "High":
        return "bg-orange-100 text-orange-800"
      case "Medium":
        return "bg-yellow-100 text-yellow-800"
      case "Low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Machinery & Vehicle Management</h1>
            <p className="text-gray-600 mt-1">
              Preventive maintenance, usage tracking, and equipment lifecycle monitoring
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Reports
            </Button>
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Dialog open={isAddMachineOpen} onOpenChange={setIsAddMachineOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Equipment
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Add New Equipment</DialogTitle>
                  <DialogDescription>Register new machinery or vehicle in the system.</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="equipmentName">Equipment Name</Label>
                      <Input id="equipmentName" placeholder="Enter equipment name" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="equipmentType">Type</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="heavy-machinery">Heavy Machinery</SelectItem>
                          <SelectItem value="lifting-equipment">Lifting Equipment</SelectItem>
                          <SelectItem value="transport-vehicle">Transport Vehicle</SelectItem>
                          <SelectItem value="power-tools">Power Tools</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="model">Model</Label>
                      <Input id="model" placeholder="Enter model" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="serialNumber">Serial Number</Label>
                      <Input id="serialNumber" placeholder="Enter serial number" />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="purchasePrice">Purchase Price</Label>
                      <Input id="purchasePrice" type="number" placeholder="Enter purchase price" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="purchaseDate">Purchase Date</Label>
                      <Input id="purchaseDate" type="date" />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="location">Current Location</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select location" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="metro">Metro Tower Complex</SelectItem>
                          <SelectItem value="green">Green Valley Residential</SelectItem>
                          <SelectItem value="industrial">Industrial Park Phase 1</SelectItem>
                          <SelectItem value="yard">Equipment Yard</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="operator">Assigned Operator</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select operator" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="john">John Smith</SelectItem>
                          <SelectItem value="david">David Crane</SelectItem>
                          <SelectItem value="robert">Robert Driver</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="specifications">Specifications</Label>
                    <Textarea id="specifications" placeholder="Enter technical specifications..." />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" className="bg-gradient-to-r from-orange-600 to-red-600">
                    Add Equipment
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {machineryStats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-green-600">{stat.change}</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="equipment" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="equipment">Equipment</TabsTrigger>
            <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
            <TabsTrigger value="usage">Usage Analytics</TabsTrigger>
            <TabsTrigger value="downtime">Downtime</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>

          {/* Equipment Tab */}
          <TabsContent value="equipment" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Equipment Inventory</CardTitle>
                  <div className="flex space-x-2">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search equipment..."
                        className="pl-10 w-64"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <Select value={selectedType} onValueChange={setSelectedType}>
                      <SelectTrigger className="w-40">
                        <SelectValue placeholder="Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Types</SelectItem>
                        <SelectItem value="heavy-machinery">Heavy Machinery</SelectItem>
                        <SelectItem value="lifting-equipment">Lifting Equipment</SelectItem>
                        <SelectItem value="transport-vehicle">Transport Vehicle</SelectItem>
                        <SelectItem value="power-tools">Power Tools</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="maintenance">Maintenance</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {equipment.map((item) => (
                    <Card key={item.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center space-x-4">
                            <div className="p-3 bg-orange-100 rounded-lg">
                              <Truck className="h-6 w-6 text-orange-600" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-3 mb-2">
                                <h3 className="font-semibold text-gray-900">{item.name}</h3>
                                <Badge className={getStatusColor(item.status)}>{item.status}</Badge>
                                <Badge className={getConditionColor(item.condition)}>{item.condition}</Badge>
                                <Badge variant="outline">{item.type}</Badge>
                              </div>
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500">
                                <div className="flex items-center space-x-1">
                                  <MapPin className="h-3 w-3" />
                                  <span>{item.location}</span>
                                </div>
                                <span>Operator: {item.operator}</span>
                                <span>Hours: {item.hoursWorked.toLocaleString()}h</span>
                                <span>Model: {item.model}</span>
                              </div>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>View Details</DropdownMenuItem>
                              <DropdownMenuItem>Edit Equipment</DropdownMenuItem>
                              <DropdownMenuItem>Schedule Maintenance</DropdownMenuItem>
                              <DropdownMenuItem>View History</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Transfer Equipment</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        {/* Equipment Specifications */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 text-sm">
                          <div>
                            <span className="text-gray-600">Serial Number:</span>
                            <div className="font-medium">{item.serialNumber}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Purchase Date:</span>
                            <div className="font-medium">{formatDate(item.purchaseDate)}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Purchase Price:</span>
                            <div className="font-medium">{formatCurrency(item.purchasePrice)}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Current Value:</span>
                            <div className="font-medium text-green-600">{formatCurrency(item.currentValue)}</div>
                          </div>
                        </div>

                        {/* Performance Metrics */}
                        <div className="bg-gray-50 p-4 rounded-lg mb-4">
                          <h4 className="text-sm font-medium text-gray-900 mb-3">Performance Metrics</h4>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-sm text-gray-600">Efficiency</span>
                                <span className="text-sm font-medium">{item.efficiency}%</span>
                              </div>
                              <Progress value={item.efficiency} className="h-2" />
                            </div>
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-sm text-gray-600">Utilization</span>
                                <span className="text-sm font-medium">{item.utilization}%</span>
                              </div>
                              <Progress value={item.utilization} className="h-2" />
                            </div>
                            <div>
                              <span className="text-sm text-gray-600">Fuel Consumption</span>
                              <div className="font-semibold text-orange-600">{item.fuelConsumption} L/hr</div>
                            </div>
                            <div>
                              <span className="text-sm text-gray-600">Downtime</span>
                              <div className="font-semibold text-red-600">{item.downtime} hrs</div>
                            </div>
                          </div>
                        </div>

                        {/* Technical Specifications */}
                        <div className="bg-blue-50 p-4 rounded-lg mb-4">
                          <h4 className="text-sm font-medium text-gray-900 mb-2">Technical Specifications</h4>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            {Object.entries(item.specifications).map(([key, value], index) => (
                              <div key={index}>
                                <span className="text-gray-600 capitalize">{key.replace(/([A-Z])/g, " $1")}:</span>
                                <div className="font-medium">{value}</div>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Maintenance Information */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Last Maintenance:</span>
                            <div className="font-medium">{formatDate(item.lastMaintenance)}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Next Maintenance:</span>
                            <div className="font-medium text-orange-600">{formatDate(item.nextMaintenance)}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Maintenance Cost (YTD):</span>
                            <div className="font-medium text-red-600">{formatCurrency(item.maintenanceCost)}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Project Assignment:</span>
                            <div className="font-medium text-blue-600">{item.project}</div>
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex items-center justify-between mt-4">
                          <div className="flex space-x-2">
                            {item.status === "Active" && (
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-green-600 border-green-200 bg-transparent"
                              >
                                <TrendingUp className="h-3 w-3 mr-1" />
                                Active
                              </Button>
                            )}
                            {new Date(item.nextMaintenance) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) && (
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-orange-600 border-orange-200 bg-transparent"
                              >
                                <Wrench className="h-3 w-3 mr-1" />
                                Maintenance Due
                              </Button>
                            )}
                          </div>
                          <div className="flex space-x-2">
                            <Button size="sm" variant="outline">
                              <Calendar className="h-3 w-3 mr-1" />
                              Schedule
                            </Button>
                            <Button size="sm" variant="outline">
                              <BarChart3 className="h-3 w-3 mr-1" />
                              Analytics
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Maintenance Tab */}
          <TabsContent value="maintenance" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <Wrench className="h-5 w-5 mr-2 text-orange-600" />
                    Maintenance Schedule
                  </CardTitle>
                  <Dialog open={isScheduleMaintenanceOpen} onOpenChange={setIsScheduleMaintenanceOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline">
                        <Plus className="h-4 w-4 mr-2" />
                        Schedule Maintenance
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[525px]">
                      <DialogHeader>
                        <DialogTitle>Schedule Maintenance</DialogTitle>
                        <DialogDescription>Schedule preventive or repair maintenance for equipment.</DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="maintenanceEquipment">Equipment</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select equipment" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="cat320">CAT 320 Excavator</SelectItem>
                                <SelectItem value="liebherr">Liebherr Tower Crane</SelectItem>
                                <SelectItem value="volvo">Volvo Dump Truck</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="maintenanceType">Type</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="preventive">Preventive</SelectItem>
                                <SelectItem value="repair">Repair</SelectItem>
                                <SelectItem value="inspection">Inspection</SelectItem>
                                <SelectItem value="overhaul">Overhaul</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="scheduledDate">Scheduled Date</Label>
                            <Input id="scheduledDate" type="date" />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="estimatedDuration">Duration (hours)</Label>
                            <Input id="estimatedDuration" type="number" placeholder="Enter duration" />
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="technician">Technician</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select technician" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="mike">Mike Mechanic</SelectItem>
                                <SelectItem value="alex">Alex Mechanic</SelectItem>
                                <SelectItem value="tom">Tom Crane</SelectItem>
                                <SelectItem value="sarah">Sarah Tech</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="estimatedCost">Estimated Cost</Label>
                            <Input id="estimatedCost" type="number" placeholder="Enter estimated cost" />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="maintenanceDescription">Description</Label>
                          <Textarea id="maintenanceDescription" placeholder="Describe the maintenance work..." />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button type="submit" className="bg-gradient-to-r from-orange-600 to-red-600">
                          Schedule Maintenance
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {maintenanceSchedule.map((maintenance) => (
                    <Card key={maintenance.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h3 className="font-semibold text-gray-900">{maintenance.equipment}</h3>
                              <Badge className={getStatusColor(maintenance.status)}>{maintenance.status}</Badge>
                              <Badge className={getPriorityColor(maintenance.priority)}>{maintenance.priority}</Badge>
                              <Badge variant="outline">{maintenance.type}</Badge>
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500 mb-3">
                              <div className="flex items-center space-x-1">
                                <Calendar className="h-3 w-3" />
                                <span>Scheduled: {formatDate(maintenance.scheduledDate)}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="h-3 w-3" />
                                <span>Duration: {maintenance.estimatedDuration}h</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <DollarSign className="h-3 w-3" />
                                <span>Cost: {formatCurrency(maintenance.estimatedCost)}</span>
                              </div>
                              <span>Technician: {maintenance.technician}</span>
                            </div>
                            <p className="text-sm text-gray-600">{maintenance.description}</p>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>View Details</DropdownMenuItem>
                              <DropdownMenuItem>Edit Schedule</DropdownMenuItem>
                              <DropdownMenuItem>Start Maintenance</DropdownMenuItem>
                              <DropdownMenuItem>Complete Work</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Cancel Schedule</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Usage Analytics Tab */}
          <TabsContent value="usage" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <BarChart3 className="h-5 w-5 mr-2 text-blue-600" />
                    Usage Analytics
                  </CardTitle>
                  <Button variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Export Data
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {usageAnalytics.map((usage, index) => (
                    <Card key={index} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <h3 className="font-semibold text-gray-900 mb-3">{usage.equipment}</h3>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                              <div>
                                <span className="text-gray-600">Daily Hours</span>
                                <div className="font-semibold text-blue-600">{usage.dailyHours}h</div>
                              </div>
                              <div>
                                <span className="text-gray-600">Weekly Hours</span>
                                <div className="font-semibold text-green-600">{usage.weeklyHours}h</div>
                              </div>
                              <div>
                                <span className="text-gray-600">Monthly Hours</span>
                                <div className="font-semibold text-purple-600">{usage.monthlyHours}h</div>
                              </div>
                              <div>
                                <span className="text-gray-600">Cost/Hour</span>
                                <div className="font-semibold text-orange-600">{formatCurrency(usage.costPerHour)}</div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Performance Indicators */}
                        <div className="grid grid-cols-3 gap-4 mb-4">
                          <div>
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-sm text-gray-600">Efficiency</span>
                              <span className="text-sm font-medium">{usage.efficiency}%</span>
                            </div>
                            <Progress value={usage.efficiency} className="h-2" />
                          </div>
                          <div>
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-sm text-gray-600">Productivity</span>
                              <span className="text-sm font-medium">{usage.productivity}%</span>
                            </div>
                            <Progress value={usage.productivity} className="h-2" />
                          </div>
                          <div>
                            <span className="text-sm text-gray-600">Fuel Consumption</span>
                            <div className="font-semibold text-red-600">{usage.fuelConsumption} L/hr</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Downtime Tab */}
          <TabsContent value="downtime" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <AlertTriangle className="h-5 w-5 mr-2 text-red-600" />
                    Downtime Analysis
                  </CardTitle>
                  <Button variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Report Downtime
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-gray-500">
                  <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Downtime analysis features coming soon</p>
                  <p className="text-sm">Track equipment breakdowns and maintenance downtime</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Reports Tab */}
          <TabsContent value="reports" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <Truck className="h-5 w-5 mr-2 text-blue-600" />
                    Equipment Report
                  </CardTitle>
                  <CardDescription>Comprehensive equipment inventory and status</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Equipment</span>
                      <span className="font-semibold">156</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Active Equipment</span>
                      <span className="font-semibold text-green-600">142</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Utilization Rate</span>
                      <span className="font-semibold text-blue-600">85%</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <Wrench className="h-5 w-5 mr-2 text-orange-600" />
                    Maintenance Report
                  </CardTitle>
                  <CardDescription>Maintenance schedules and cost analysis</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Scheduled Maintenance</span>
                      <span className="font-semibold">12</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Maintenance Cost</span>
                      <span className="font-semibold text-red-600">{formatCurrency(45500)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Preventive vs Repair</span>
                      <span className="font-semibold text-green-600">75% / 25%</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <TrendingUp className="h-5 w-5 mr-2 text-green-600" />
                    Performance Report
                  </CardTitle>
                  <CardDescription>Equipment efficiency and productivity metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Avg. Efficiency</span>
                      <span className="font-semibold text-green-600">89%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Downtime Hours</span>
                      <span className="font-semibold text-red-600">84 hrs</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Cost Savings</span>
                      <span className="font-semibold text-blue-600">{formatCurrency(28000)}</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
