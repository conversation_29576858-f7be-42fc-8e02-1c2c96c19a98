"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  HardHat,
  Building,
  Users,
  DollarSign,
  AlertTriangle,
  Search,
  MoreHorizontal,
  Plus,
  MapPin,
  Truck,
  Wrench,
  FileText,
  Camera,
  Shield,
  Settings,
  Download,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"

export default function ConstructionManagementPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [selectedPriority, setSelectedPriority] = useState("all")
  const [isCreateProjectOpen, setIsCreateProjectOpen] = useState(false)

  const constructionStats = [
    {
      title: "Active Projects",
      value: "18",
      change: "+2 this month",
      icon: Building,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Total Budget",
      value: "$2.4M",
      change: "+15% allocated",
      icon: DollarSign,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Safety Incidents",
      value: "3",
      change: "-40% this month",
      icon: AlertTriangle,
      color: "text-red-600",
      bgColor: "bg-red-100",
    },
    {
      title: "Workforce",
      value: "247",
      change: "+12 workers",
      icon: Users,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
  ]

  const projects = [
    {
      id: "CONST001",
      name: "Downtown Office Complex",
      description: "25-story commercial building with underground parking and retail space",
      status: "In Progress",
      priority: "High",
      progress: 68,
      startDate: "2024-01-15",
      endDate: "2024-12-30",
      budget: 850000,
      spent: 578000,
      location: "Downtown District",
      contractor: "BuildCorp Construction",
      projectManager: "Sarah Wilson",
      workforce: 45,
      safetyScore: 92,
      phases: [
        { name: "Foundation", status: "Completed", progress: 100 },
        { name: "Structure", status: "In Progress", progress: 75 },
        { name: "MEP Systems", status: "Planning", progress: 15 },
        { name: "Finishing", status: "Not Started", progress: 0 },
      ],
      team: [
        { name: "John Smith", role: "Site Supervisor", avatar: "/placeholder.svg?height=32&width=32" },
        { name: "Maria Garcia", role: "Safety Officer", avatar: "/placeholder.svg?height=32&width=32" },
        { name: "David Chen", role: "Quality Inspector", avatar: "/placeholder.svg?height=32&width=32" },
        { name: "Lisa Johnson", role: "Architect", avatar: "/placeholder.svg?height=32&width=32" },
      ],
    },
    {
      id: "CONST002",
      name: "Residential Complex Phase 2",
      description: "120-unit residential development with amenities and landscaping",
      status: "Planning",
      priority: "Medium",
      progress: 25,
      startDate: "2024-03-01",
      endDate: "2024-11-15",
      budget: 650000,
      spent: 162500,
      location: "Westside Development",
      contractor: "HomeBuild Solutions",
      projectManager: "Mike Anderson",
      workforce: 32,
      safetyScore: 88,
      phases: [
        { name: "Site Preparation", status: "In Progress", progress: 80 },
        { name: "Foundation", status: "Planning", progress: 20 },
        { name: "Construction", status: "Not Started", progress: 0 },
        { name: "Landscaping", status: "Not Started", progress: 0 },
      ],
      team: [
        { name: "Robert Brown", role: "Site Supervisor", avatar: "/placeholder.svg?height=32&width=32" },
        { name: "Jennifer Lee", role: "Safety Officer", avatar: "/placeholder.svg?height=32&width=32" },
        { name: "Tom Wilson", role: "Foreman", avatar: "/placeholder.svg?height=32&width=32" },
      ],
    },
    {
      id: "CONST003",
      name: "Infrastructure Upgrade",
      description: "Road widening and utility infrastructure improvements",
      status: "In Progress",
      priority: "Critical",
      progress: 45,
      startDate: "2023-11-01",
      endDate: "2024-04-30",
      budget: 420000,
      spent: 189000,
      location: "Main Street Corridor",
      contractor: "CityWorks Engineering",
      projectManager: "Emma Davis",
      workforce: 28,
      safetyScore: 95,
      phases: [
        { name: "Planning & Permits", status: "Completed", progress: 100 },
        { name: "Excavation", status: "Completed", progress: 100 },
        { name: "Utility Installation", status: "In Progress", progress: 60 },
        { name: "Road Construction", status: "Planning", progress: 10 },
      ],
      team: [
        { name: "Carlos Rodriguez", role: "Civil Engineer", avatar: "/placeholder.svg?height=32&width=32" },
        { name: "Amy Thompson", role: "Safety Coordinator", avatar: "/placeholder.svg?height=32&width=32" },
        { name: "Kevin Park", role: "Equipment Operator", avatar: "/placeholder.svg?height=32&width=32" },
      ],
    },
  ]

  const equipment = [
    {
      id: "EQ001",
      name: "Excavator CAT 320",
      type: "Heavy Machinery",
      status: "Active",
      location: "Downtown Office Complex",
      operator: "Kevin Park",
      lastMaintenance: "2024-01-10",
      nextMaintenance: "2024-02-10",
      hoursUsed: 1247,
      condition: "Good",
    },
    {
      id: "EQ002",
      name: "Crane Tower 150T",
      type: "Lifting Equipment",
      status: "Active",
      location: "Downtown Office Complex",
      operator: "Mike Johnson",
      lastMaintenance: "2024-01-05",
      nextMaintenance: "2024-02-05",
      hoursUsed: 892,
      condition: "Excellent",
    },
    {
      id: "EQ003",
      name: "Concrete Mixer Truck",
      type: "Transport",
      status: "Maintenance",
      location: "Equipment Yard",
      operator: "David Brown",
      lastMaintenance: "2024-01-20",
      nextMaintenance: "2024-01-25",
      hoursUsed: 2156,
      condition: "Fair",
    },
  ]

  const safetyIncidents = [
    {
      id: "INC001",
      type: "Near Miss",
      description: "Worker almost struck by falling debris",
      project: "Downtown Office Complex",
      date: "2024-01-22",
      severity: "Medium",
      status: "Investigated",
      reportedBy: "Maria Garcia",
      actions: "Additional safety barriers installed",
    },
    {
      id: "INC002",
      type: "Minor Injury",
      description: "Cut on hand from sharp metal edge",
      project: "Residential Complex Phase 2",
      date: "2024-01-20",
      severity: "Low",
      status: "Closed",
      reportedBy: "Jennifer Lee",
      actions: "First aid provided, safety briefing conducted",
    },
    {
      id: "INC003",
      type: "Equipment Damage",
      description: "Hydraulic leak in excavator",
      project: "Infrastructure Upgrade",
      date: "2024-01-18",
      severity: "Medium",
      status: "Resolved",
      reportedBy: "Carlos Rodriguez",
      actions: "Equipment repaired, maintenance schedule updated",
    },
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case "Planning":
        return "bg-gray-100 text-gray-800"
      case "In Progress":
        return "bg-blue-100 text-blue-800"
      case "On Hold":
        return "bg-yellow-100 text-yellow-800"
      case "Completed":
        return "bg-green-100 text-green-800"
      case "Delayed":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "Critical":
        return "bg-red-100 text-red-800"
      case "High":
        return "bg-orange-100 text-orange-800"
      case "Medium":
        return "bg-yellow-100 text-yellow-800"
      case "Low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getSeverityColor = (severity) => {
    switch (severity) {
      case "Critical":
        return "bg-red-100 text-red-800"
      case "High":
        return "bg-orange-100 text-orange-800"
      case "Medium":
        return "bg-yellow-100 text-yellow-800"
      case "Low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getConditionColor = (condition) => {
    switch (condition) {
      case "Excellent":
        return "bg-green-100 text-green-800"
      case "Good":
        return "bg-blue-100 text-blue-800"
      case "Fair":
        return "bg-yellow-100 text-yellow-800"
      case "Poor":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Construction Management</h1>
            <p className="text-gray-600 mt-1">Manage construction projects, resources, safety, and compliance</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Reports
            </Button>
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Dialog open={isCreateProjectOpen} onOpenChange={setIsCreateProjectOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700">
                  <Plus className="h-4 w-4 mr-2" />
                  New Project
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[525px]">
                <DialogHeader>
                  <DialogTitle>Create New Construction Project</DialogTitle>
                  <DialogDescription>Set up a new construction project with timeline and resources.</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="projectName" className="text-right">
                      Name
                    </Label>
                    <Input id="projectName" placeholder="Project name" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-start gap-4">
                    <Label htmlFor="description" className="text-right mt-2">
                      Description
                    </Label>
                    <Textarea id="description" placeholder="Project description..." className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="location" className="text-right">
                      Location
                    </Label>
                    <Input id="location" placeholder="Project location" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="contractor" className="text-right">
                      Contractor
                    </Label>
                    <Select>
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select contractor" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="buildcorp">BuildCorp Construction</SelectItem>
                        <SelectItem value="homebuild">HomeBuild Solutions</SelectItem>
                        <SelectItem value="cityworks">CityWorks Engineering</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="budget" className="text-right">
                      Budget
                    </Label>
                    <Input id="budget" type="number" placeholder="Project budget" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="startDate">Start Date</Label>
                      <Input id="startDate" type="date" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="endDate">End Date</Label>
                      <Input id="endDate" type="date" />
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" className="bg-gradient-to-r from-orange-600 to-red-600">
                    Create Project
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {constructionStats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-green-600">{stat.change}</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="projects" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="projects">Projects</TabsTrigger>
            <TabsTrigger value="equipment">Equipment</TabsTrigger>
            <TabsTrigger value="safety">Safety</TabsTrigger>
            <TabsTrigger value="workforce">Workforce</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>

          {/* Projects Tab */}
          <TabsContent value="projects" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Construction Projects</CardTitle>
                  <div className="flex space-x-2">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search projects..."
                        className="pl-10 w-64"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="planning">Planning</SelectItem>
                        <SelectItem value="in-progress">In Progress</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="on-hold">On Hold</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {projects.map((project) => (
                    <Card key={project.id} className="hover:shadow-md transition-shadow cursor-pointer">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h3 className="font-semibold text-gray-900">{project.name}</h3>
                              <Badge className={getStatusColor(project.status)}>{project.status}</Badge>
                              <Badge className={getPriorityColor(project.priority)}>{project.priority}</Badge>
                            </div>
                            <p className="text-sm text-gray-600 mb-3">{project.description}</p>
                            <div className="flex items-center space-x-6 text-sm text-gray-500">
                              <div className="flex items-center space-x-1">
                                <MapPin className="h-3 w-3" />
                                <span>{project.location}</span>
                              </div>
                              <span>PM: {project.projectManager}</span>
                              <span>Contractor: {project.contractor}</span>
                              <div className="flex items-center space-x-1">
                                <Users className="h-3 w-3" />
                                <span>{project.workforce} workers</span>
                              </div>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>View Details</DropdownMenuItem>
                              <DropdownMenuItem>Edit Project</DropdownMenuItem>
                              <DropdownMenuItem>View Timeline</DropdownMenuItem>
                              <DropdownMenuItem>Safety Report</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Archive Project</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        <div className="space-y-4">
                          <div>
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm text-gray-600">Overall Progress</span>
                              <span className="text-sm font-medium">{project.progress}%</span>
                            </div>
                            <Progress value={project.progress} className="h-2" />
                          </div>

                          <div className="grid grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-gray-600">Budget</span>
                              <div className="font-semibold">{formatCurrency(project.budget)}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Spent</span>
                              <div className="font-semibold text-orange-600">{formatCurrency(project.spent)}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Timeline</span>
                              <div className="font-semibold">
                                {formatDate(project.startDate)} - {formatDate(project.endDate)}
                              </div>
                            </div>
                            <div>
                              <span className="text-gray-600">Safety Score</span>
                              <div className="font-semibold text-green-600">{project.safetyScore}%</div>
                            </div>
                          </div>

                          <div>
                            <h4 className="text-sm font-medium text-gray-900 mb-2">Project Phases</h4>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                              {project.phases.map((phase, index) => (
                                <div key={index} className="text-xs">
                                  <div className="flex items-center justify-between mb-1">
                                    <span className="text-gray-600">{phase.name}</span>
                                    <span className="font-medium">{phase.progress}%</span>
                                  </div>
                                  <Progress value={phase.progress} className="h-1" />
                                  <Badge className={`mt-1 text-xs ${getStatusColor(phase.status)}`}>
                                    {phase.status}
                                  </Badge>
                                </div>
                              ))}
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex -space-x-2">
                              {project.team.slice(0, 4).map((member, index) => (
                                <Avatar key={index} className="h-6 w-6 border-2 border-white">
                                  <AvatarImage src={member.avatar || "/placeholder.svg"} alt={member.name} />
                                  <AvatarFallback className="text-xs">
                                    {member.name
                                      .split(" ")
                                      .map((n) => n[0])
                                      .join("")}
                                  </AvatarFallback>
                                </Avatar>
                              ))}
                              {project.team.length > 4 && (
                                <div className="h-6 w-6 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center">
                                  <span className="text-xs text-gray-600">+{project.team.length - 4}</span>
                                </div>
                              )}
                            </div>
                            <div className="flex space-x-2">
                              <Button size="sm" variant="outline">
                                <Camera className="h-3 w-3 mr-1" />
                                Photos
                              </Button>
                              <Button size="sm" variant="outline">
                                <FileText className="h-3 w-3 mr-1" />
                                Documents
                              </Button>
                              <Button size="sm" variant="outline">
                                View Details
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Equipment Tab */}
          <TabsContent value="equipment" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <Truck className="h-5 w-5 mr-2 text-orange-600" />
                    Equipment Management
                  </CardTitle>
                  <Button variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Equipment
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {equipment.map((item) => (
                    <Card key={item.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className="p-3 bg-orange-100 rounded-lg">
                              <Truck className="h-6 w-6 text-orange-600" />
                            </div>
                            <div>
                              <h4 className="font-semibold text-gray-900">{item.name}</h4>
                              <p className="text-sm text-gray-600">{item.type}</p>
                              <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                                <span>Operator: {item.operator}</span>
                                <span>Hours: {item.hoursUsed}h</span>
                                <div className="flex items-center space-x-1">
                                  <MapPin className="h-3 w-3" />
                                  <span>{item.location}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <Badge className={getStatusColor(item.status)}>{item.status}</Badge>
                            <Badge className={`ml-2 ${getConditionColor(item.condition)}`}>{item.condition}</Badge>
                            <div className="text-sm text-gray-500 mt-2">
                              <div>Last: {formatDate(item.lastMaintenance)}</div>
                              <div>Next: {formatDate(item.nextMaintenance)}</div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Safety Tab */}
          <TabsContent value="safety" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Shield className="h-5 w-5 mr-2 text-red-600" />
                      Safety Incidents
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {safetyIncidents.map((incident) => (
                        <Card key={incident.id} className="hover:shadow-md transition-shadow">
                          <CardContent className="p-4">
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex-1">
                                <div className="flex items-center space-x-3 mb-2">
                                  <h4 className="font-semibold text-gray-900">{incident.type}</h4>
                                  <Badge className={getSeverityColor(incident.severity)}>{incident.severity}</Badge>
                                  <Badge className={getStatusColor(incident.status)}>{incident.status}</Badge>
                                </div>
                                <p className="text-sm text-gray-600 mb-2">{incident.description}</p>
                                <div className="flex items-center space-x-4 text-sm text-gray-500">
                                  <span>Project: {incident.project}</span>
                                  <span>Date: {formatDate(incident.date)}</span>
                                  <span>Reported by: {incident.reportedBy}</span>
                                </div>
                              </div>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" className="h-8 w-8 p-0">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem>View Details</DropdownMenuItem>
                                  <DropdownMenuItem>Update Status</DropdownMenuItem>
                                  <DropdownMenuItem>Add Photos</DropdownMenuItem>
                                  <DropdownMenuItem>Generate Report</DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                            <div className="bg-gray-50 p-3 rounded-lg">
                              <h5 className="text-sm font-medium text-gray-900 mb-1">Actions Taken:</h5>
                              <p className="text-sm text-gray-600">{incident.actions}</p>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <AlertTriangle className="h-5 w-5 mr-2 text-yellow-600" />
                      Safety Metrics
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-gray-600">Overall Safety Score</span>
                          <span className="text-sm font-medium">92%</span>
                        </div>
                        <Progress value={92} className="h-2" />
                      </div>
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-gray-600">Days Without Incident</span>
                          <span className="text-2xl font-bold text-green-600">45</span>
                        </div>
                      </div>
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-gray-600">Safety Training Completion</span>
                          <span className="text-sm font-medium">87%</span>
                        </div>
                        <Progress value={87} className="h-2" />
                      </div>
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-gray-600">Equipment Inspections</span>
                          <span className="text-sm font-medium">94%</span>
                        </div>
                        <Progress value={94} className="h-2" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="mt-6">
                  <CardHeader>
                    <CardTitle className="text-lg">Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <Button className="w-full justify-start bg-transparent" variant="outline">
                        <AlertTriangle className="h-4 w-4 mr-2" />
                        Report Incident
                      </Button>
                      <Button className="w-full justify-start bg-transparent" variant="outline">
                        <Shield className="h-4 w-4 mr-2" />
                        Safety Inspection
                      </Button>
                      <Button className="w-full justify-start bg-transparent" variant="outline">
                        <FileText className="h-4 w-4 mr-2" />
                        Safety Training
                      </Button>
                      <Button className="w-full justify-start bg-transparent" variant="outline">
                        <Wrench className="h-4 w-4 mr-2" />
                        Equipment Check
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Workforce Tab */}
          <TabsContent value="workforce" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <HardHat className="h-5 w-5 mr-2 text-blue-600" />
                  Workforce Management
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-gray-500">
                  <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Workforce management features coming soon</p>
                  <p className="text-sm">Track worker assignments, certifications, and attendance</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Reports Tab */}
          <TabsContent value="reports" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <Building className="h-5 w-5 mr-2 text-blue-600" />
                    Project Reports
                  </CardTitle>
                  <CardDescription>Progress, budget, and timeline analysis</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">On Schedule</span>
                      <span className="font-semibold text-green-600">83%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Budget Utilization</span>
                      <span className="font-semibold">68%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Quality Score</span>
                      <span className="font-semibold text-blue-600">4.3/5</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <Shield className="h-5 w-5 mr-2 text-red-600" />
                    Safety Reports
                  </CardTitle>
                  <CardDescription>Safety incidents and compliance metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Safety Score</span>
                      <span className="font-semibold text-green-600">92%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Incidents This Month</span>
                      <span className="font-semibold text-orange-600">3</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Training Compliance</span>
                      <span className="font-semibold">87%</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <Truck className="h-5 w-5 mr-2 text-orange-600" />
                    Equipment Reports
                  </CardTitle>
                  <CardDescription>Equipment utilization and maintenance</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Equipment Uptime</span>
                      <span className="font-semibold text-green-600">94%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Maintenance Due</span>
                      <span className="font-semibold text-yellow-600">5 items</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Utilization Rate</span>
                      <span className="font-semibold">78%</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
