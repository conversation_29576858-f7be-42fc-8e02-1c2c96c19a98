"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Package,
  Warehouse,
  TrendingDown,
  TrendingUp,
  AlertTriangle,
  Plus,
  Search,
  MoreHorizontal,
  MapPin,
  Clock,
  CheckCircle,
  Settings,
  Download,
  BarChart3,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"

export default function InventoryManagementPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedLocation, setSelectedLocation] = useState("all")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [isAddStockOpen, setIsAddStockOpen] = useState(false)
  const [isIssueStockOpen, setIsIssueStockOpen] = useState(false)

  const inventoryStats = [
    {
      title: "Total Items",
      value: "1,247",
      change: "+23 this month",
      icon: Package,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Total Value",
      value: "$1.8M",
      change: "+12% vs last month",
      icon: TrendingUp,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Low Stock Items",
      value: "18",
      change: "Requires attention",
      icon: AlertTriangle,
      color: "text-red-600",
      bgColor: "bg-red-100",
    },
    {
      title: "Storage Locations",
      value: "8",
      change: "Across 3 sites",
      icon: Warehouse,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
  ]

  const inventoryItems = [
    {
      id: "INV001",
      name: "Steel Beams (Grade A)",
      category: "Steel & Metal",
      unit: "tons",
      currentStock: 45,
      minStock: 20,
      maxStock: 100,
      unitPrice: 1200,
      totalValue: 54000,
      location: "Main Warehouse",
      supplier: "Steel Dynamics Corp",
      lastReceived: "2024-01-20",
      lastIssued: "2024-01-22",
      status: "In Stock",
      reorderLevel: 25,
      movements: [
        { type: "Receipt", quantity: 25, date: "2024-01-20", reference: "PO001", project: "Metro Tower Complex" },
        { type: "Issue", quantity: -15, date: "2024-01-22", reference: "ISS001", project: "Metro Tower Complex" },
      ],
    },
    {
      id: "INV002",
      name: "Cement (OPC 53)",
      category: "Cement & Concrete",
      unit: "bags",
      currentStock: 150,
      minStock: 200,
      maxStock: 500,
      unitPrice: 45,
      totalValue: 6750,
      location: "Site Storage A",
      supplier: "Concrete Solutions Ltd",
      lastReceived: "2024-01-18",
      lastIssued: "2024-01-23",
      status: "Low Stock",
      reorderLevel: 200,
      movements: [
        { type: "Receipt", quantity: 200, date: "2024-01-18", reference: "PO002", project: "Green Valley Residential" },
        { type: "Issue", quantity: -50, date: "2024-01-23", reference: "ISS002", project: "Green Valley Residential" },
      ],
    },
    {
      id: "INV003",
      name: "Reinforcement Bars (12mm)",
      category: "Steel & Metal",
      unit: "tons",
      currentStock: 8,
      minStock: 15,
      maxStock: 50,
      unitPrice: 350,
      totalValue: 2800,
      location: "Site Storage B",
      supplier: "Steel Dynamics Corp",
      lastReceived: "2024-01-15",
      lastIssued: "2024-01-21",
      status: "Critical",
      reorderLevel: 15,
      movements: [
        { type: "Receipt", quantity: 20, date: "2024-01-15", reference: "PO003", project: "Industrial Park Phase 1" },
        { type: "Issue", quantity: -12, date: "2024-01-21", reference: "ISS003", project: "Industrial Park Phase 1" },
      ],
    },
    {
      id: "INV004",
      name: "Bricks (Class A)",
      category: "Masonry",
      unit: "pieces",
      currentStock: 25000,
      minStock: 10000,
      maxStock: 50000,
      unitPrice: 0.8,
      totalValue: 20000,
      location: "Main Warehouse",
      supplier: "BuildMart Supplies",
      lastReceived: "2024-01-19",
      lastIssued: "2024-01-24",
      status: "In Stock",
      reorderLevel: 12000,
      movements: [
        {
          type: "Receipt",
          quantity: 30000,
          date: "2024-01-19",
          reference: "PO004",
          project: "Green Valley Residential",
        },
        {
          type: "Issue",
          quantity: -5000,
          date: "2024-01-24",
          reference: "ISS004",
          project: "Green Valley Residential",
        },
      ],
    },
  ]

  const storageLocations = [
    {
      id: "LOC001",
      name: "Main Warehouse",
      address: "123 Industrial Ave, Construction City",
      capacity: "10,000 sq ft",
      utilization: 75,
      manager: "John Storage",
      phone: "(*************",
      itemCount: 485,
      totalValue: 850000,
      categories: ["Steel & Metal", "Cement & Concrete", "Masonry", "Electrical"],
    },
    {
      id: "LOC002",
      name: "Site Storage A",
      address: "Metro Tower Complex Site",
      capacity: "2,500 sq ft",
      utilization: 60,
      manager: "Maria Site",
      phone: "(*************",
      itemCount: 125,
      totalValue: 320000,
      categories: ["Steel & Metal", "Cement & Concrete"],
    },
    {
      id: "LOC003",
      name: "Site Storage B",
      address: "Green Valley Residential Site",
      capacity: "1,800 sq ft",
      utilization: 45,
      manager: "David Field",
      phone: "(*************",
      itemCount: 89,
      totalValue: 180000,
      categories: ["Masonry", "Electrical", "Plumbing"],
    },
  ]

  const stockMovements = [
    {
      id: "MOV001",
      type: "Receipt",
      item: "Steel Beams (Grade A)",
      quantity: 25,
      unit: "tons",
      date: "2024-01-20",
      time: "10:30 AM",
      reference: "PO001",
      project: "Metro Tower Complex",
      location: "Main Warehouse",
      operator: "John Storage",
      notes: "Quality inspection passed",
    },
    {
      id: "MOV002",
      type: "Issue",
      item: "Cement (OPC 53)",
      quantity: -50,
      unit: "bags",
      date: "2024-01-23",
      time: "2:15 PM",
      reference: "ISS002",
      project: "Green Valley Residential",
      location: "Site Storage A",
      operator: "Maria Site",
      notes: "Issued for foundation work",
    },
    {
      id: "MOV003",
      type: "Transfer",
      item: "Reinforcement Bars (12mm)",
      quantity: 5,
      unit: "tons",
      date: "2024-01-21",
      time: "11:45 AM",
      reference: "TRF001",
      project: "Industrial Park Phase 1",
      location: "Main Warehouse → Site Storage B",
      operator: "David Field",
      notes: "Inter-location transfer",
    },
    {
      id: "MOV004",
      type: "Adjustment",
      item: "Bricks (Class A)",
      quantity: -200,
      unit: "pieces",
      date: "2024-01-22",
      time: "4:00 PM",
      reference: "ADJ001",
      project: "N/A",
      location: "Main Warehouse",
      operator: "John Storage",
      notes: "Damaged during handling",
    },
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case "In Stock":
        return "bg-green-100 text-green-800"
      case "Low Stock":
        return "bg-yellow-100 text-yellow-800"
      case "Critical":
        return "bg-red-100 text-red-800"
      case "Out of Stock":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getMovementTypeColor = (type) => {
    switch (type) {
      case "Receipt":
        return "bg-green-100 text-green-800"
      case "Issue":
        return "bg-blue-100 text-blue-800"
      case "Transfer":
        return "bg-purple-100 text-purple-800"
      case "Adjustment":
        return "bg-orange-100 text-orange-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStockLevel = (current, min, max) => {
    if (current <= min) return "Critical"
    if (current <= min * 1.5) return "Low Stock"
    return "In Stock"
  }

  const getStockLevelColor = (current, min) => {
    if (current <= min) return "text-red-600"
    if (current <= min * 1.5) return "text-yellow-600"
    return "text-green-600"
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Inventory & Store Management</h1>
            <p className="text-gray-600 mt-1">
              Real-time stock monitoring, location-wise tracking, and automated reorder management
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Reports
            </Button>
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Dialog open={isAddStockOpen} onOpenChange={setIsAddStockOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Stock
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[525px]">
                <DialogHeader>
                  <DialogTitle>Add Stock Receipt</DialogTitle>
                  <DialogDescription>Record new stock receipt from purchase orders or transfers.</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="item">Item</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select item" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="steel-beams">Steel Beams (Grade A)</SelectItem>
                          <SelectItem value="cement">Cement (OPC 53)</SelectItem>
                          <SelectItem value="rebar">Reinforcement Bars (12mm)</SelectItem>
                          <SelectItem value="bricks">Bricks (Class A)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="location">Location</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select location" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="main">Main Warehouse</SelectItem>
                          <SelectItem value="site-a">Site Storage A</SelectItem>
                          <SelectItem value="site-b">Site Storage B</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="quantity">Quantity</Label>
                      <Input id="quantity" type="number" placeholder="Enter quantity" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="unitPrice">Unit Price</Label>
                      <Input id="unitPrice" type="number" placeholder="Enter unit price" />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="reference">Reference</Label>
                      <Input id="reference" placeholder="PO number or reference" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="supplier">Supplier</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select supplier" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="steel-dynamics">Steel Dynamics Corp</SelectItem>
                          <SelectItem value="concrete-solutions">Concrete Solutions Ltd</SelectItem>
                          <SelectItem value="buildmart">BuildMart Supplies</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="notes">Notes</Label>
                    <Textarea id="notes" placeholder="Additional notes..." />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" className="bg-gradient-to-r from-green-600 to-teal-600">
                    Add Stock
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {inventoryStats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-green-600">{stat.change}</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="inventory" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="inventory">Inventory</TabsTrigger>
            <TabsTrigger value="locations">Locations</TabsTrigger>
            <TabsTrigger value="movements">Movements</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          {/* Inventory Tab */}
          <TabsContent value="inventory" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Inventory Items</CardTitle>
                  <div className="flex space-x-2">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search items..."
                        className="pl-10 w-64"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                      <SelectTrigger className="w-40">
                        <SelectValue placeholder="Location" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Locations</SelectItem>
                        <SelectItem value="main">Main Warehouse</SelectItem>
                        <SelectItem value="site-a">Site Storage A</SelectItem>
                        <SelectItem value="site-b">Site Storage B</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                      <SelectTrigger className="w-40">
                        <SelectValue placeholder="Category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Categories</SelectItem>
                        <SelectItem value="steel">Steel & Metal</SelectItem>
                        <SelectItem value="concrete">Cement & Concrete</SelectItem>
                        <SelectItem value="masonry">Masonry</SelectItem>
                        <SelectItem value="electrical">Electrical</SelectItem>
                      </SelectContent>
                    </Select>
                    <Dialog open={isIssueStockOpen} onOpenChange={setIsIssueStockOpen}>
                      <DialogTrigger asChild>
                        <Button variant="outline">
                          <TrendingDown className="h-4 w-4 mr-2" />
                          Issue Stock
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[525px]">
                        <DialogHeader>
                          <DialogTitle>Issue Stock</DialogTitle>
                          <DialogDescription>
                            Issue materials to projects or transfer between locations.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="issueItem">Item</Label>
                              <Select>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select item" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="steel-beams">Steel Beams (Grade A)</SelectItem>
                                  <SelectItem value="cement">Cement (OPC 53)</SelectItem>
                                  <SelectItem value="rebar">Reinforcement Bars (12mm)</SelectItem>
                                  <SelectItem value="bricks">Bricks (Class A)</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="issueLocation">From Location</Label>
                              <Select>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select location" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="main">Main Warehouse</SelectItem>
                                  <SelectItem value="site-a">Site Storage A</SelectItem>
                                  <SelectItem value="site-b">Site Storage B</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="issueQuantity">Quantity</Label>
                              <Input id="issueQuantity" type="number" placeholder="Enter quantity" />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="issueProject">Project</Label>
                              <Select>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select project" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="metro">Metro Tower Complex</SelectItem>
                                  <SelectItem value="green">Green Valley Residential</SelectItem>
                                  <SelectItem value="industrial">Industrial Park Phase 1</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="issueNotes">Purpose/Notes</Label>
                            <Textarea id="issueNotes" placeholder="Purpose of issue..." />
                          </div>
                        </div>
                        <DialogFooter>
                          <Button type="submit" className="bg-gradient-to-r from-blue-600 to-purple-600">
                            Issue Stock
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {inventoryItems.map((item) => (
                    <Card key={item.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h3 className="font-semibold text-gray-900">{item.name}</h3>
                              <Badge className={getStatusColor(item.status)}>{item.status}</Badge>
                              <Badge variant="outline">{item.category}</Badge>
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500 mb-4">
                              <div className="flex items-center space-x-1">
                                <MapPin className="h-3 w-3" />
                                <span>{item.location}</span>
                              </div>
                              <span>Supplier: {item.supplier}</span>
                              <span>Last Received: {formatDate(item.lastReceived)}</span>
                              <span>Last Issued: {formatDate(item.lastIssued)}</span>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>View Details</DropdownMenuItem>
                              <DropdownMenuItem>Issue Stock</DropdownMenuItem>
                              <DropdownMenuItem>Transfer Stock</DropdownMenuItem>
                              <DropdownMenuItem>Adjust Stock</DropdownMenuItem>
                              <DropdownMenuItem>Set Reorder Level</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Movement History</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        {/* Stock Level Indicator */}
                        <div className="mb-4">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-gray-600">Stock Level</span>
                            <span
                              className={`text-sm font-medium ${getStockLevelColor(item.currentStock, item.minStock)}`}
                            >
                              {item.currentStock} {item.unit}
                            </span>
                          </div>
                          <div className="relative">
                            <Progress value={(item.currentStock / item.maxStock) * 100} className="h-2" />
                            <div
                              className="absolute top-0 h-2 w-0.5 bg-red-500"
                              style={{ left: `${(item.minStock / item.maxStock) * 100}%` }}
                            />
                            <div
                              className="absolute top-0 h-2 w-0.5 bg-yellow-500"
                              style={{ left: `${(item.reorderLevel / item.maxStock) * 100}%` }}
                            />
                          </div>
                          <div className="flex items-center justify-between text-xs text-gray-500 mt-1">
                            <span>Min: {item.minStock}</span>
                            <span>Reorder: {item.reorderLevel}</span>
                            <span>Max: {item.maxStock}</span>
                          </div>
                        </div>

                        {/* Financial Information */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Unit Price</span>
                            <div className="font-semibold">{formatCurrency(item.unitPrice)}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Total Value</span>
                            <div className="font-semibold text-green-600">{formatCurrency(item.totalValue)}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Current Stock</span>
                            <div className="font-semibold">
                              {item.currentStock} {item.unit}
                            </div>
                          </div>
                          <div>
                            <span className="text-gray-600">Status</span>
                            <div className={`font-semibold ${getStockLevelColor(item.currentStock, item.minStock)}`}>
                              {getStockLevel(item.currentStock, item.minStock, item.maxStock)}
                            </div>
                          </div>
                        </div>

                        {/* Recent Movements */}
                        <div className="mt-4">
                          <h4 className="text-sm font-medium text-gray-900 mb-2">Recent Movements</h4>
                          <div className="space-y-1">
                            {item.movements.slice(0, 2).map((movement, index) => (
                              <div
                                key={index}
                                className="flex items-center justify-between text-xs p-2 bg-gray-50 rounded"
                              >
                                <div className="flex items-center space-x-2">
                                  <Badge className={getMovementTypeColor(movement.type)} variant="outline">
                                    {movement.type}
                                  </Badge>
                                  <span>
                                    {movement.quantity > 0 ? "+" : ""}
                                    {movement.quantity} {item.unit}
                                  </span>
                                  <span className="text-gray-500">{formatDate(movement.date)}</span>
                                </div>
                                <span className="text-gray-500">{movement.reference}</span>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex items-center justify-between mt-4">
                          <div className="flex space-x-2">
                            {item.currentStock <= item.minStock && (
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-red-600 border-red-200 bg-transparent"
                              >
                                <AlertTriangle className="h-3 w-3 mr-1" />
                                Reorder Now
                              </Button>
                            )}
                          </div>
                          <div className="flex space-x-2">
                            <Button size="sm" variant="outline">
                              <TrendingDown className="h-3 w-3 mr-1" />
                              Issue
                            </Button>
                            <Button size="sm" variant="outline">
                              <BarChart3 className="h-3 w-3 mr-1" />
                              History
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Storage Locations Tab */}
          <TabsContent value="locations" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <Warehouse className="h-5 w-5 mr-2 text-purple-600" />
                    Storage Locations
                  </CardTitle>
                  <Button variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Location
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {storageLocations.map((location) => (
                    <Card key={location.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <h3 className="font-semibold text-gray-900 mb-1">{location.name}</h3>
                            <p className="text-sm text-gray-600 mb-2">{location.address}</p>
                            <div className="text-sm text-gray-500">
                              <div>Manager: {location.manager}</div>
                              <div>{location.phone}</div>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>View Details</DropdownMenuItem>
                              <DropdownMenuItem>Edit Location</DropdownMenuItem>
                              <DropdownMenuItem>View Inventory</DropdownMenuItem>
                              <DropdownMenuItem>Transfer Stock</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Deactivate</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        {/* Utilization */}
                        <div className="mb-4">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-gray-600">Space Utilization</span>
                            <span className="text-sm font-medium">{location.utilization}%</span>
                          </div>
                          <Progress value={location.utilization} className="h-2" />
                          <div className="text-xs text-gray-500 mt-1">Capacity: {location.capacity}</div>
                        </div>

                        {/* Statistics */}
                        <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                          <div>
                            <span className="text-gray-600">Items</span>
                            <div className="font-semibold">{location.itemCount}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Total Value</span>
                            <div className="font-semibold text-green-600">{formatCurrency(location.totalValue)}</div>
                          </div>
                        </div>

                        {/* Categories */}
                        <div>
                          <span className="text-sm text-gray-600 mb-2 block">Categories</span>
                          <div className="flex flex-wrap gap-1">
                            {location.categories.map((category, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {category}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Stock Movements Tab */}
          <TabsContent value="movements" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <Clock className="h-5 w-5 mr-2 text-blue-600" />
                    Stock Movements
                  </CardTitle>
                  <div className="flex space-x-2">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search movements..."
                        className="pl-10 w-64"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <Select>
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Types</SelectItem>
                        <SelectItem value="receipt">Receipt</SelectItem>
                        <SelectItem value="issue">Issue</SelectItem>
                        <SelectItem value="transfer">Transfer</SelectItem>
                        <SelectItem value="adjustment">Adjustment</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {stockMovements.map((movement) => (
                    <Card key={movement.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <Badge className={getMovementTypeColor(movement.type)}>{movement.type}</Badge>
                              <h4 className="font-semibold text-gray-900">{movement.item}</h4>
                              <span
                                className={`font-semibold ${movement.quantity > 0 ? "text-green-600" : "text-red-600"}`}
                              >
                                {movement.quantity > 0 ? "+" : ""}
                                {movement.quantity} {movement.unit}
                              </span>
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500 mb-2">
                              <span>
                                Date: {formatDate(movement.date)} {movement.time}
                              </span>
                              <span>Reference: {movement.reference}</span>
                              <span>Project: {movement.project}</span>
                              <span>Operator: {movement.operator}</span>
                            </div>
                            <div className="flex items-center space-x-4 text-sm">
                              <div className="flex items-center space-x-1">
                                <MapPin className="h-3 w-3 text-gray-400" />
                                <span className="text-gray-600">{movement.location}</span>
                              </div>
                              {movement.notes && <span className="text-gray-500 italic">"{movement.notes}"</span>}
                            </div>
                          </div>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <TrendingUp className="h-5 w-5 mr-2 text-green-600" />
                    Stock Turnover
                  </CardTitle>
                  <CardDescription>Inventory turnover analysis and optimization</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Average Turnover</span>
                      <span className="font-semibold text-green-600">4.2x/year</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Fast Moving Items</span>
                      <span className="font-semibold">67%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Slow Moving Items</span>
                      <span className="font-semibold text-orange-600">18%</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    View Analysis
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <AlertTriangle className="h-5 w-5 mr-2 text-red-600" />
                    Wastage Tracking
                  </CardTitle>
                  <CardDescription>Material wastage and loss analysis</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">This Month</span>
                      <span className="font-semibold text-red-600">$12,450</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Wastage Rate</span>
                      <span className="font-semibold">2.3%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">vs Last Month</span>
                      <span className="font-semibold text-green-600">-15%</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <TrendingDown className="h-4 w-4 mr-2" />
                    View Report
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <Package className="h-5 w-5 mr-2 text-blue-600" />
                    Reorder Analysis
                  </CardTitle>
                  <CardDescription>Automated reorder recommendations</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Items to Reorder</span>
                      <span className="font-semibold text-orange-600">18</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Estimated Cost</span>
                      <span className="font-semibold">$285,000</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Lead Time Avg</span>
                      <span className="font-semibold text-blue-600">12 days</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Generate Orders
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
