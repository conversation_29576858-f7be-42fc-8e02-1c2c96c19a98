"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Mic, Volume2, Brain, Zap, Settings, HelpCircle, Activity, Users, MessageSquare, BarChart3 } from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import VoiceRecognition from "@/components/voice-recognition"
import VoiceDictationField from "@/components/voice-dictation-field"

export default function VoicePage() {
  const [demoText, setDemoText] = useState("")
  const [demoTextarea, setDemoTextarea] = useState("")

  const voiceFeatures = [
    {
      title: "Navigation Commands",
      description: "Navigate through the platform using voice commands",
      icon: Zap,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      examples: ["Go to dashboard", "Open users", "Go to tickets", "Go back"],
    },
    {
      title: "Action Commands",
      description: "Perform actions and create new items with voice",
      icon: Activity,
      color: "text-green-600",
      bgColor: "bg-green-100",
      examples: ["Create new user", "New ticket", "Add asset", "Schedule appointment"],
    },
    {
      title: "Search Commands",
      description: "Search for content using natural language",
      icon: Users,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
      examples: ["Search for John", "Find tickets", "Look for projects", "Show analytics"],
    },
    {
      title: "Voice Dictation",
      description: "Convert speech to text for forms and fields",
      icon: MessageSquare,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
      examples: ["Dictate notes", "Fill forms", "Write descriptions", "Add comments"],
    },
  ]

  const useCases = [
    {
      title: "Accessibility",
      description: "Hands-free operation for users with mobility limitations",
      icon: HelpCircle,
      benefits: ["Improved accessibility", "Reduced physical strain", "Better user experience"],
    },
    {
      title: "Multitasking",
      description: "Navigate while working on other tasks",
      icon: Brain,
      benefits: ["Increased productivity", "Seamless workflow", "Time savings"],
    },
    {
      title: "Mobile Usage",
      description: "Perfect for mobile devices and on-the-go access",
      icon: Mic,
      benefits: ["Touch-free interaction", "Better mobile UX", "Faster data entry"],
    },
    {
      title: "Data Entry",
      description: "Quick form filling and note-taking",
      icon: BarChart3,
      benefits: ["Faster input", "Reduced typing errors", "Natural interaction"],
    },
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Voice Recognition</h1>
            <p className="text-gray-600 mt-1">Hands-free navigation and voice-to-text capabilities</p>
          </div>
          <Badge className="bg-green-100 text-green-800">
            <Volume2 className="h-3 w-3 mr-1" />
            Voice Enabled
          </Badge>
        </div>

        {/* Main Voice Recognition Component */}
        <VoiceRecognition />

        {/* Features Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {voiceFeatures.map((feature, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-3 rounded-lg ${feature.bgColor}`}>
                    <feature.icon className={`h-6 w-6 ${feature.color}`} />
                  </div>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-sm text-gray-600 mb-4">{feature.description}</p>
                <div className="space-y-1">
                  {feature.examples.map((example, idx) => (
                    <div key={idx} className="text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded">
                      "{example}"
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Demo Section */}
        <Tabs defaultValue="dictation" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="dictation">Voice Dictation Demo</TabsTrigger>
            <TabsTrigger value="use-cases">Use Cases</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="dictation" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageSquare className="h-5 w-5 mr-2 text-blue-600" />
                  Voice Dictation Demo
                </CardTitle>
                <CardDescription>Try voice-to-text functionality with these interactive fields</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Single Line Input</label>
                  <VoiceDictationField
                    placeholder="Try saying something..."
                    value={demoText}
                    onChange={(e) => setDemoText(e.target.value)}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Multi-line Textarea</label>
                  <VoiceDictationField
                    component="textarea"
                    placeholder="Dictate a longer message..."
                    value={demoTextarea}
                    onChange={(e) => setDemoTextarea(e.target.value)}
                    className="w-full min-h-[120px]"
                  />
                </div>

                <Alert>
                  <Mic className="h-4 w-4" />
                  <AlertDescription>
                    Click the microphone icon in any field to start voice dictation. Speak clearly and the text will
                    appear automatically.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="use-cases" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {useCases.map((useCase, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="flex items-center text-lg">
                      <useCase.icon className="h-5 w-5 mr-2 text-blue-600" />
                      {useCase.title}
                    </CardTitle>
                    <CardDescription>{useCase.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <h4 className="font-medium text-gray-900">Benefits:</h4>
                      <ul className="space-y-1">
                        {useCase.benefits.map((benefit, idx) => (
                          <li key={idx} className="text-sm text-gray-600 flex items-center">
                            <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-2"></div>
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="h-5 w-5 mr-2 text-gray-600" />
                  Voice Recognition Settings
                </CardTitle>
                <CardDescription>Configure voice recognition preferences and behavior</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-900">Audio Settings</h4>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-700">Audio Feedback</span>
                          <Badge className="bg-green-100 text-green-800">Enabled</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-700">Auto-stop Listening</span>
                          <Badge className="bg-green-100 text-green-800">3 seconds</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-700">Language</span>
                          <Badge className="bg-blue-100 text-blue-800">English (US)</Badge>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-900">Recognition Settings</h4>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-700">Continuous Mode</span>
                          <Badge className="bg-green-100 text-green-800">Enabled</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-700">Interim Results</span>
                          <Badge className="bg-green-100 text-green-800">Enabled</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-700">Confidence Threshold</span>
                          <Badge className="bg-blue-100 text-blue-800">70%</Badge>
                        </div>
                      </div>
                    </div>
                  </div>

                  <Alert>
                    <Settings className="h-4 w-4" />
                    <AlertDescription>
                      Voice recognition settings are automatically optimized for your browser and device. Advanced
                      settings can be configured in your browser's privacy settings.
                    </AlertDescription>
                  </Alert>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
