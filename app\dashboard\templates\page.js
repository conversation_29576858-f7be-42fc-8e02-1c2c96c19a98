"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  LayoutTemplateIcon as Template,
  Search,
  Filter,
  Plus,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Download,
  Upload,
  Clock,
  <PERSON>,
  Setting<PERSON>,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import TemplateSelector from "@/components/permissions/template-selector"

export default function TemplatesPage() {
  const [appliedTemplates, setAppliedTemplates] = useState([])
  const [availableUsers, setAvailableUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterType, setFilterType] = useState("all")
  const [stats, setStats] = useState({
    totalTemplates: 0,
    projectTypeTemplates: 0,
    roleCombinationTemplates: 0,
    recentlyApplied: 0,
  })

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockAppliedTemplates = [
      {
        id: "1",
        projectId: "proj-1",
        projectName: "E-commerce Platform",
        templateType: "project_type",
        templateKey: "software_development",
        templateName: "Software Development Project",
        appliedBy: "John Doe",
        appliedAt: "2024-01-15T10:30:00Z",
        status: "active",
        usersAffected: 8,
      },
      {
        id: "2",
        projectId: "proj-2",
        projectName: "Office Building Construction",
        templateType: "project_type",
        templateKey: "construction",
        templateName: "Construction Project",
        appliedBy: "Sarah Wilson",
        appliedAt: "2024-01-14T16:45:00Z",
        status: "active",
        usersAffected: 12,
      },
      {
        id: "3",
        projectId: "proj-3",
        projectName: "Marketing Campaign Q1",
        templateType: "role_combination",
        templateKey: "project_core_team",
        templateName: "Project Core Team",
        appliedBy: "Mike Johnson",
        appliedAt: "2024-01-13T09:20:00Z",
        status: "active",
        usersAffected: 5,
      },
      {
        id: "4",
        projectId: "proj-4",
        projectName: "Research Initiative",
        templateType: "role_combination",
        templateKey: "leadership_team",
        templateName: "Leadership Team",
        appliedBy: "Lisa Chen",
        appliedAt: "2024-01-12T14:15:00Z",
        status: "inactive",
        usersAffected: 3,
      },
    ]

    const mockUsers = [
      { id: "1", name: "John Doe", email: "<EMAIL>", role: "project_manager" },
      { id: "2", name: "Sarah Wilson", email: "<EMAIL>", role: "portfolio_manager" },
      { id: "3", name: "Mike Johnson", email: "<EMAIL>", role: "business_analyst" },
      { id: "4", name: "Lisa Chen", email: "<EMAIL>", role: "team_lead" },
      { id: "5", name: "Emma Davis", email: "<EMAIL>", role: "developer" },
    ]

    setAppliedTemplates(mockAppliedTemplates)
    setAvailableUsers(mockUsers)
    setStats({
      totalTemplates: mockAppliedTemplates.length,
      projectTypeTemplates: mockAppliedTemplates.filter((t) => t.templateType === "project_type").length,
      roleCombinationTemplates: mockAppliedTemplates.filter((t) => t.templateType === "role_combination").length,
      recentlyApplied: mockAppliedTemplates.filter(
        (t) => new Date(t.appliedAt) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      ).length,
    })
    setLoading(false)
  }, [])

  const handleTemplateApply = (templateResult, templateType) => {
    // Add the newly applied template to the list
    const newTemplate = {
      id: (appliedTemplates.length + 1).toString(),
      projectId: templateResult.projectId,
      projectName: `Project ${templateResult.projectId}`,
      templateType,
      templateKey: templateResult.templateName || templateResult.combination,
      templateName: templateResult.templateName,
      appliedBy: "Current User",
      appliedAt: new Date().toISOString(),
      status: "active",
      usersAffected: templateResult.userIds?.length || 1,
    }

    setAppliedTemplates([newTemplate, ...appliedTemplates])
    setStats((prev) => ({
      ...prev,
      totalTemplates: prev.totalTemplates + 1,
      [templateType === "project_type" ? "projectTypeTemplates" : "roleCombinationTemplates"]:
        prev[templateType === "project_type" ? "projectTypeTemplates" : "roleCombinationTemplates"] + 1,
      recentlyApplied: prev.recentlyApplied + 1,
    }))
  }

  const filteredTemplates = appliedTemplates.filter((template) => {
    const matchesSearch =
      template.templateName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.projectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.appliedBy.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesFilter = filterType === "all" || template.templateType === filterType || template.status === filterType

    return matchesSearch && matchesFilter
  })

  const getStatusColor = (status) => {
    return status === "active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
  }

  const getTypeColor = (type) => {
    return type === "project_type" ? "bg-blue-100 text-blue-800" : "bg-purple-100 text-purple-800"
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading templates...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Permission Templates</h1>
            <p className="text-gray-600 mt-1">Manage and apply permission templates for projects</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button className="bg-gradient-to-r from-purple-600 to-blue-600">
              <Plus className="h-4 w-4 mr-2" />
              Create Template
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Templates</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalTemplates}</p>
                </div>
                <Template className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Project Types</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.projectTypeTemplates}</p>
                </div>
                <Settings className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Role Combinations</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.roleCombinationTemplates}</p>
                </div>
                <Users className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Recently Applied</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.recentlyApplied}</p>
                </div>
                <Clock className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="browse" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="browse">Browse Templates</TabsTrigger>
            <TabsTrigger value="applied">Applied Templates</TabsTrigger>
          </TabsList>

          <TabsContent value="browse" className="space-y-6">
            <TemplateSelector
              projectId="new-project"
              projectType="software_development"
              currentPhase="CREATE"
              teamSize={5}
              onTemplateApply={handleTemplateApply}
              availableUsers={availableUsers}
            />
          </TabsContent>

          <TabsContent value="applied" className="space-y-6">
            {/* Filters */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search templates..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Filter by type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Templates</SelectItem>
                      <SelectItem value="project_type">Project Type</SelectItem>
                      <SelectItem value="role_combination">Role Combination</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline">
                    <Filter className="h-4 w-4 mr-2" />
                    More Filters
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Applied Templates Table */}
            <Card>
              <CardHeader>
                <CardTitle>Applied Templates</CardTitle>
                <CardDescription>Templates currently applied to projects</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Template</TableHead>
                      <TableHead>Project</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Applied By</TableHead>
                      <TableHead>Applied Date</TableHead>
                      <TableHead>Users</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTemplates.map((template) => (
                      <TableRow key={template.id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Template className="h-4 w-4 text-gray-500" />
                            <span className="font-medium">{template.templateName}</span>
                          </div>
                        </TableCell>
                        <TableCell>{template.projectName}</TableCell>
                        <TableCell>
                          <Badge className={getTypeColor(template.templateType)}>
                            {template.templateType === "project_type" ? "Project Type" : "Role Combination"}
                          </Badge>
                        </TableCell>
                        <TableCell>{template.appliedBy}</TableCell>
                        <TableCell>{new Date(template.appliedAt).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <Users className="h-3 w-3 text-gray-500" />
                            <span>{template.usersAffected}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(template.status)}>{template.status}</Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Template
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-red-600">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Remove Template
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {filteredTemplates.length === 0 && (
                  <div className="text-center py-8">
                    <Template className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
                    <p className="text-gray-600">
                      {searchTerm || filterType !== "all"
                        ? "Try adjusting your search or filter criteria"
                        : "Start by applying a template to a project"}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
