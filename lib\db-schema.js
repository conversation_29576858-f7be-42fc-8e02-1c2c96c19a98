// Database schema definitions for all master tables and modules

export const masterTables = {
  countries: {
    id: "string",
    name: "string",
    code: "string",
    currency_code: "string",
    phone_code: "string",
    is_active: "boolean",
    created_at: "datetime",
    updated_at: "datetime",
  },

  states: {
    id: "string",
    country_id: "string",
    name: "string",
    code: "string",
    is_active: "boolean",
    created_at: "datetime",
    updated_at: "datetime",
  },

  cities: {
    id: "string",
    state_id: "string",
    name: "string",
    code: "string",
    is_active: "boolean",
    created_at: "datetime",
    updated_at: "datetime",
  },

  locations: {
    id: "string",
    city_id: "string",
    name: "string",
    address: "text",
    postal_code: "string",
    latitude: "decimal",
    longitude: "decimal",
    is_active: "boolean",
    created_at: "datetime",
    updated_at: "datetime",
  },

  zones: {
    id: "string",
    name: "string",
    description: "text",
    locations: "json",
    is_active: "boolean",
    created_at: "datetime",
    updated_at: "datetime",
  },

  floors: {
    id: "string",
    location_id: "string",
    name: "string",
    level: "integer",
    description: "text",
    is_active: "boolean",
    created_at: "datetime",
    updated_at: "datetime",
  },

  departments: {
    id: "string",
    name: "string",
    code: "string",
    parent_id: "string",
    manager_id: "string",
    description: "text",
    is_active: "boolean",
    created_at: "datetime",
    updated_at: "datetime",
  },

  designations: {
    id: "string",
    department_id: "string",
    name: "string",
    code: "string",
    level: "integer",
    description: "text",
    is_active: "boolean",
    created_at: "datetime",
    updated_at: "datetime",
  },

  shifts: {
    id: "string",
    name: "string",
    start_time: "time",
    end_time: "time",
    break_duration: "integer",
    is_active: "boolean",
    created_at: "datetime",
    updated_at: "datetime",
  },

  currencies: {
    id: "string",
    name: "string",
    code: "string",
    symbol: "string",
    exchange_rate: "decimal",
    is_base: "boolean",
    is_active: "boolean",
    created_at: "datetime",
    updated_at: "datetime",
  },

  timezones: {
    id: "string",
    name: "string",
    code: "string",
    offset: "string",
    is_active: "boolean",
    created_at: "datetime",
    updated_at: "datetime",
  },

  tax_categories: {
    id: "string",
    name: "string",
    rate: "decimal",
    type: "string",
    description: "text",
    is_active: "boolean",
    created_at: "datetime",
    updated_at: "datetime",
  },

  uoms: {
    id: "string",
    name: "string",
    symbol: "string",
    type: "string",
    conversion_factor: "decimal",
    base_uom_id: "string",
    is_active: "boolean",
    created_at: "datetime",
    updated_at: "datetime",
  },

  payment_terms: {
    id: "string",
    name: "string",
    days: "integer",
    discount_percent: "decimal",
    description: "text",
    is_active: "boolean",
    created_at: "datetime",
    updated_at: "datetime",
  },

  bank_details: {
    id: "string",
    bank_name: "string",
    account_number: "string",
    account_name: "string",
    branch: "string",
    ifsc_code: "string",
    swift_code: "string",
    is_active: "boolean",
    created_at: "datetime",
    updated_at: "datetime",
  },

  document_types: {
    id: "string",
    name: "string",
    code: "string",
    category: "string",
    required_fields: "json",
    is_active: "boolean",
    created_at: "datetime",
    updated_at: "datetime",
  },

  user_roles: {
    id: "string",
    name: "string",
    description: "text",
    permissions: "json",
    is_active: "boolean",
    created_at: "datetime",
    updated_at: "datetime",
  },
}

export const moduleSchemas = {
  // HRMS
  employees: {
    id: "string",
    employee_code: "string",
    first_name: "string",
    last_name: "string",
    email: "string",
    phone: "string",
    department_id: "string",
    designation_id: "string",
    shift_id: "string",
    manager_id: "string",
    hire_date: "date",
    salary: "decimal",
    status: "string",
    is_active: "boolean",
    created_at: "datetime",
    updated_at: "datetime",
  },

  attendance: {
    id: "string",
    employee_id: "string",
    date: "date",
    check_in: "datetime",
    check_out: "datetime",
    break_duration: "integer",
    overtime_hours: "decimal",
    status: "string",
    created_at: "datetime",
    updated_at: "datetime",
  },

  // CRM
  leads: {
    id: "string",
    name: "string",
    email: "string",
    phone: "string",
    company: "string",
    source: "string",
    status: "string",
    assigned_to: "string",
    created_at: "datetime",
    updated_at: "datetime",
  },

  // Sales
  sales_orders: {
    id: "string",
    order_number: "string",
    customer_id: "string",
    order_date: "date",
    delivery_date: "date",
    total_amount: "decimal",
    status: "string",
    created_at: "datetime",
    updated_at: "datetime",
  },

  // Inventory
  items: {
    id: "string",
    item_code: "string",
    name: "string",
    description: "text",
    category: "string",
    uom_id: "string",
    unit_price: "decimal",
    is_active: "boolean",
    created_at: "datetime",
    updated_at: "datetime",
  },

  // Projects
  projects: {
    id: "string",
    name: "string",
    description: "text",
    start_date: "date",
    end_date: "date",
    status: "string",
    manager_id: "string",
    budget: "decimal",
    created_at: "datetime",
    updated_at: "datetime",
  },
}
