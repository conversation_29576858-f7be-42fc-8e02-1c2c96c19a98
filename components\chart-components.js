"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>hart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
  RadialBarChart,
  RadialBar,
} from "recharts"
import { TrendingUp, TrendingDown, MoreHorizontal } from "lucide-react"

// Reusable Chart Card Component
export function ChartCard({ title, description, children, trend, value, change, actions = true }) {
  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">{title}</CardTitle>
            {description && <CardDescription>{description}</CardDescription>}
          </div>
          <div className="flex items-center space-x-2">
            {value && (
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900">{value}</div>
                {change && (
                  <div
                    className={`text-sm flex items-center ${change.startsWith("+") ? "text-green-600" : "text-red-600"}`}
                  >
                    {change.startsWith("+") ? (
                      <TrendingUp className="h-3 w-3 mr-1" />
                    ) : (
                      <TrendingDown className="h-3 w-3 mr-1" />
                    )}
                    {change}
                  </div>
                )}
              </div>
            )}
            {actions && (
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>{children}</CardContent>
    </Card>
  )
}

// Revenue Trend Chart
export function RevenueTrendChart({ data, height = 300 }) {
  return (
    <ChartCard title="Revenue Trends" description="Monthly revenue across all modules" value="$485K" change="+15.3%">
      <ResponsiveContainer width="100%" height={height}>
        <AreaChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" />
          <YAxis />
          <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, ""]} />
          <Legend />
          <Area type="monotone" dataKey="payroll" stackId="1" stroke="#3B82F6" fill="#3B82F6" fillOpacity={0.6} />
          <Area type="monotone" dataKey="consultations" stackId="1" stroke="#10B981" fill="#10B981" fillOpacity={0.6} />
          <Area type="monotone" dataKey="projects" stackId="1" stroke="#F59E0B" fill="#F59E0B" fillOpacity={0.6} />
        </AreaChart>
      </ResponsiveContainer>
    </ChartCard>
  )
}

// User Activity Chart
export function UserActivityChart({ data, height = 300 }) {
  return (
    <ChartCard title="User Activity" description="Daily user engagement metrics" value="2,847" change="+12%">
      <ResponsiveContainer width="100%" height={height}>
        <BarChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="day" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar dataKey="users" fill="#3B82F6" name="Active Users" />
          <Bar dataKey="sessions" fill="#10B981" name="Sessions" />
        </BarChart>
      </ResponsiveContainer>
    </ChartCard>
  )
}

// Department Distribution Chart
export function DepartmentChart({ data, height = 300 }) {
  return (
    <ChartCard title="Department Distribution" description="Employee distribution across departments">
      <ResponsiveContainer width="100%" height={height}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip />
        </PieChart>
      </ResponsiveContainer>
    </ChartCard>
  )
}

// Performance Metrics Chart
export function PerformanceChart({ data, height = 300 }) {
  return (
    <ChartCard title="Performance Metrics" description="Key performance indicators" value="94.2%" change="****%">
      <ResponsiveContainer width="100%" height={height}>
        <RadialBarChart cx="50%" cy="50%" innerRadius="10%" outerRadius="80%" data={data}>
          <RadialBar
            minAngle={15}
            label={{ position: "insideStart", fill: "#fff" }}
            background
            clockWise
            dataKey="value"
          />
          <Legend iconSize={18} layout="vertical" verticalAlign="middle" wrapperStyle={{ paddingLeft: "20px" }} />
          <Tooltip />
        </RadialBarChart>
      </ResponsiveContainer>
    </ChartCard>
  )
}

// Module Performance Comparison Chart
export function ModuleComparisonChart({ data, height = 400 }) {
  return (
    <ChartCard title="Module Performance" description="Efficiency comparison across modules">
      <ResponsiveContainer width="100%" height={height}>
        <BarChart data={data} layout="horizontal">
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis type="number" />
          <YAxis dataKey="module" type="category" width={120} />
          <Tooltip />
          <Legend />
          <Bar dataKey="efficiency" fill="#3B82F6" name="Efficiency %" />
        </BarChart>
      </ResponsiveContainer>
    </ChartCard>
  )
}

// Trend Line Chart
export function TrendChart({ data, height = 300, title, description, dataKey, color = "#3B82F6" }) {
  return (
    <ChartCard title={title} description={description}>
      <ResponsiveContainer width="100%" height={height}>
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="period" />
          <YAxis />
          <Tooltip />
          <Line type="monotone" dataKey={dataKey} stroke={color} strokeWidth={2} />
        </LineChart>
      </ResponsiveContainer>
    </ChartCard>
  )
}

// Custom Metric Card
export function MetricCard({ title, value, change, icon: Icon, color, bgColor, description }) {
  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            {change && (
              <p className={`text-sm ${change.startsWith("+") ? "text-green-600" : "text-red-600"}`}>{change}</p>
            )}
            {description && <p className="text-xs text-gray-500 mt-1">{description}</p>}
          </div>
          {Icon && (
            <div className={`p-3 rounded-full ${bgColor}`}>
              <Icon className={`h-6 w-6 ${color}`} />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
