"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  UserCheck,
  Users,
  TrendingUp,
  DollarSign,
  Search,
  MoreHorizontal,
  Plus,
  Mail,
  Phone,
  Calendar,
  Star,
  Target,
  Activity,
  BarChart3,
  PieChart,
  Download,
  Settings,
  Zap,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"

export default function CRMPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedStage, setSelectedStage] = useState("all")
  const [selectedSource, setSelectedSource] = useState("all")
  const [isAddLeadOpen, setIsAddLeadOpen] = useState(false)
  const [isAddDealOpen, setIsAddDealOpen] = useState(false)

  const crmStats = [
    {
      title: "Total Leads",
      value: "1,247",
      change: "+12% this month",
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Qualified Leads",
      value: "342",
      change: "****% conversion",
      icon: Target,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Pipeline Value",
      value: "$2.4M",
      change: "+15.3% growth",
      icon: DollarSign,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      title: "Win Rate",
      value: "68%",
      change: "+5% improvement",
      icon: TrendingUp,
      color: "text-emerald-600",
      bgColor: "bg-emerald-100",
    },
  ]

  const pipelineStages = [
    { name: "Lead", count: 45, value: 180000, color: "bg-gray-500" },
    { name: "Qualified", count: 32, value: 320000, color: "bg-blue-500" },
    { name: "Proposal", count: 18, value: 540000, color: "bg-yellow-500" },
    { name: "Negotiation", count: 12, value: 720000, color: "bg-orange-500" },
    { name: "Closed Won", count: 8, value: 640000, color: "bg-green-500" },
  ]

  const leads = [
    {
      id: "LD001",
      name: "Sarah Johnson",
      email: "<EMAIL>",
      company: "TechCorp Solutions",
      phone: "+****************",
      source: "Website",
      stage: "Qualified",
      score: 85,
      value: 25000,
      lastActivity: "2 hours ago",
      assignedTo: "John Smith",
      status: "Hot",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: "LD002",
      name: "Michael Chen",
      email: "<EMAIL>",
      company: "Innovate Labs",
      phone: "+****************",
      source: "LinkedIn",
      stage: "Proposal",
      score: 92,
      value: 45000,
      lastActivity: "1 day ago",
      assignedTo: "Lisa Wilson",
      status: "Hot",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: "LD003",
      name: "Emily Rodriguez",
      email: "<EMAIL>",
      company: "Startup Ventures",
      phone: "+****************",
      source: "Referral",
      stage: "Negotiation",
      score: 78,
      value: 35000,
      lastActivity: "3 hours ago",
      assignedTo: "Mike Johnson",
      status: "Warm",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: "LD004",
      name: "David Park",
      email: "<EMAIL>",
      company: "Enterprise Systems",
      phone: "+****************",
      source: "Cold Email",
      stage: "Lead",
      score: 45,
      value: 15000,
      lastActivity: "1 week ago",
      assignedTo: "Sarah Davis",
      status: "Cold",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: "LD005",
      name: "Jennifer Kim",
      email: "<EMAIL>",
      company: "Growth Partners",
      phone: "+****************",
      source: "Trade Show",
      stage: "Qualified",
      score: 88,
      value: 55000,
      lastActivity: "4 hours ago",
      assignedTo: "Tom Anderson",
      status: "Hot",
      avatar: "/placeholder.svg?height=40&width=40",
    },
  ]

  const recentActivities = [
    {
      id: 1,
      type: "email",
      description: "Sent proposal to TechCorp Solutions",
      contact: "Sarah Johnson",
      time: "2 hours ago",
      user: "John Smith",
    },
    {
      id: 2,
      type: "call",
      description: "Follow-up call with Innovate Labs",
      contact: "Michael Chen",
      time: "4 hours ago",
      user: "Lisa Wilson",
    },
    {
      id: 3,
      type: "meeting",
      description: "Demo scheduled with Enterprise Systems",
      contact: "David Park",
      time: "1 day ago",
      user: "Sarah Davis",
    },
    {
      id: 4,
      type: "note",
      description: "Added qualification notes for Growth Partners",
      contact: "Jennifer Kim",
      time: "2 days ago",
      user: "Tom Anderson",
    },
  ]

  const automatedWorkflows = [
    {
      id: 1,
      name: "New Lead Welcome Sequence",
      trigger: "Lead Created",
      status: "Active",
      leads: 156,
      conversion: "23%",
      description: "Automated email sequence for new leads with company introduction and value proposition",
    },
    {
      id: 2,
      name: "Proposal Follow-up",
      trigger: "Proposal Sent",
      status: "Active",
      leads: 89,
      conversion: "45%",
      description: "Follow-up sequence after proposal delivery with case studies and testimonials",
    },
    {
      id: 3,
      name: "Cold Lead Re-engagement",
      trigger: "No Activity 30 Days",
      status: "Active",
      leads: 234,
      conversion: "12%",
      description: "Re-engagement campaign for leads that have gone cold with special offers",
    },
    {
      id: 4,
      name: "Demo No-Show Recovery",
      trigger: "Missed Demo",
      status: "Paused",
      leads: 45,
      conversion: "18%",
      description: "Recovery sequence for prospects who missed scheduled demos",
    },
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case "Hot":
        return "bg-red-100 text-red-800"
      case "Warm":
        return "bg-orange-100 text-orange-800"
      case "Cold":
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStageColor = (stage) => {
    switch (stage) {
      case "Lead":
        return "bg-gray-100 text-gray-800"
      case "Qualified":
        return "bg-blue-100 text-blue-800"
      case "Proposal":
        return "bg-yellow-100 text-yellow-800"
      case "Negotiation":
        return "bg-orange-100 text-orange-800"
      case "Closed Won":
        return "bg-green-100 text-green-800"
      case "Closed Lost":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getScoreColor = (score) => {
    if (score >= 80) return "text-green-600"
    if (score >= 60) return "text-yellow-600"
    return "text-red-600"
  }

  const getActivityIcon = (type) => {
    switch (type) {
      case "email":
        return <Mail className="h-4 w-4 text-blue-600" />
      case "call":
        return <Phone className="h-4 w-4 text-green-600" />
      case "meeting":
        return <Calendar className="h-4 w-4 text-purple-600" />
      case "note":
        return <Activity className="h-4 w-4 text-orange-600" />
      default:
        return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Customer Relationship Management</h1>
            <p className="text-gray-600 mt-1">Manage leads, track sales pipeline, and automate customer workflows</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Data
            </Button>
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              CRM Settings
            </Button>
            <Dialog open={isAddLeadOpen} onOpenChange={setIsAddLeadOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Lead
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[525px]">
                <DialogHeader>
                  <DialogTitle>Add New Lead</DialogTitle>
                  <DialogDescription>Create a new lead and add them to your sales pipeline.</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="name" className="text-right">
                      Name
                    </Label>
                    <Input id="name" placeholder="Full name" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="email" className="text-right">
                      Email
                    </Label>
                    <Input id="email" type="email" placeholder="Email address" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="company" className="text-right">
                      Company
                    </Label>
                    <Input id="company" placeholder="Company name" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="phone" className="text-right">
                      Phone
                    </Label>
                    <Input id="phone" placeholder="Phone number" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="source" className="text-right">
                      Source
                    </Label>
                    <Select>
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Lead source" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="website">Website</SelectItem>
                        <SelectItem value="linkedin">LinkedIn</SelectItem>
                        <SelectItem value="referral">Referral</SelectItem>
                        <SelectItem value="cold-email">Cold Email</SelectItem>
                        <SelectItem value="trade-show">Trade Show</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-start gap-4">
                    <Label htmlFor="notes" className="text-right mt-2">
                      Notes
                    </Label>
                    <Textarea id="notes" placeholder="Initial notes about the lead..." className="col-span-3" />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" className="bg-gradient-to-r from-blue-600 to-purple-600">
                    Create Lead
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {crmStats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-green-600">{stat.change}</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="pipeline" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="pipeline">Sales Pipeline</TabsTrigger>
            <TabsTrigger value="leads">Leads</TabsTrigger>
            <TabsTrigger value="activities">Activities</TabsTrigger>
            <TabsTrigger value="workflows">Workflows</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>

          {/* Sales Pipeline Tab */}
          <TabsContent value="pipeline" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2 text-blue-600" />
                  Sales Pipeline Overview
                </CardTitle>
                <CardDescription>Track deals through your sales process</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                  {pipelineStages.map((stage, index) => (
                    <div key={index} className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold text-gray-900">{stage.name}</h3>
                        <Badge variant="outline">{stage.count}</Badge>
                      </div>
                      <div className="space-y-2">
                        <div className={`h-2 rounded-full ${stage.color}`}></div>
                        <div className="text-sm text-gray-600">
                          <div className="font-medium">{formatCurrency(stage.value)}</div>
                          <div className="text-xs">{stage.count} deals</div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        {/* Sample deals in each stage */}
                        {index === 0 && (
                          <div className="p-2 bg-gray-50 rounded border">
                            <div className="text-xs font-medium">Enterprise Systems</div>
                            <div className="text-xs text-gray-500">{formatCurrency(15000)}</div>
                          </div>
                        )}
                        {index === 1 && (
                          <>
                            <div className="p-2 bg-blue-50 rounded border">
                              <div className="text-xs font-medium">TechCorp Solutions</div>
                              <div className="text-xs text-gray-500">{formatCurrency(25000)}</div>
                            </div>
                            <div className="p-2 bg-blue-50 rounded border">
                              <div className="text-xs font-medium">Growth Partners</div>
                              <div className="text-xs text-gray-500">{formatCurrency(55000)}</div>
                            </div>
                          </>
                        )}
                        {index === 2 && (
                          <div className="p-2 bg-yellow-50 rounded border">
                            <div className="text-xs font-medium">Innovate Labs</div>
                            <div className="text-xs text-gray-500">{formatCurrency(45000)}</div>
                          </div>
                        )}
                        {index === 3 && (
                          <div className="p-2 bg-orange-50 rounded border">
                            <div className="text-xs font-medium">Startup Ventures</div>
                            <div className="text-xs text-gray-500">{formatCurrency(35000)}</div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <TrendingUp className="h-5 w-5 mr-2 text-green-600" />
                    Pipeline Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Total Pipeline Value</span>
                      <span className="font-semibold text-lg">{formatCurrency(2400000)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Average Deal Size</span>
                      <span className="font-semibold">{formatCurrency(21053)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Win Rate</span>
                      <span className="font-semibold text-green-600">68%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Average Sales Cycle</span>
                      <span className="font-semibold">45 days</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Target className="h-5 w-5 mr-2 text-purple-600" />
                    Monthly Targets
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-gray-600">Revenue Target</span>
                        <span className="font-semibold">{formatCurrency(500000)}</span>
                      </div>
                      <Progress value={72} className="h-2" />
                      <div className="text-xs text-gray-500 mt-1">72% complete - {formatCurrency(360000)} achieved</div>
                    </div>
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-gray-600">New Leads Target</span>
                        <span className="font-semibold">200</span>
                      </div>
                      <Progress value={85} className="h-2" />
                      <div className="text-xs text-gray-500 mt-1">85% complete - 170 leads generated</div>
                    </div>
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-gray-600">Deals Closed Target</span>
                        <span className="font-semibold">25</span>
                      </div>
                      <Progress value={64} className="h-2" />
                      <div className="text-xs text-gray-500 mt-1">64% complete - 16 deals closed</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Leads Tab */}
          <TabsContent value="leads" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Lead Management</CardTitle>
                <CardDescription>Track and manage your sales leads</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search leads by name, company, or email..."
                      className="pl-10"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <Select value={selectedStage} onValueChange={setSelectedStage}>
                    <SelectTrigger className="w-full sm:w-[180px]">
                      <SelectValue placeholder="Filter by stage" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Stages</SelectItem>
                      <SelectItem value="lead">Lead</SelectItem>
                      <SelectItem value="qualified">Qualified</SelectItem>
                      <SelectItem value="proposal">Proposal</SelectItem>
                      <SelectItem value="negotiation">Negotiation</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={selectedSource} onValueChange={setSelectedSource}>
                    <SelectTrigger className="w-full sm:w-[180px]">
                      <SelectValue placeholder="Filter by source" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Sources</SelectItem>
                      <SelectItem value="website">Website</SelectItem>
                      <SelectItem value="linkedin">LinkedIn</SelectItem>
                      <SelectItem value="referral">Referral</SelectItem>
                      <SelectItem value="cold-email">Cold Email</SelectItem>
                      <SelectItem value="trade-show">Trade Show</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Lead</TableHead>
                        <TableHead>Company</TableHead>
                        <TableHead>Source</TableHead>
                        <TableHead>Stage</TableHead>
                        <TableHead>Score</TableHead>
                        <TableHead>Value</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Assigned To</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {leads.map((lead) => (
                        <TableRow key={lead.id}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <Avatar>
                                <AvatarImage src={lead.avatar || "/placeholder.svg"} alt={lead.name} />
                                <AvatarFallback>
                                  {lead.name
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="font-medium text-gray-900">{lead.name}</div>
                                <div className="text-sm text-gray-500">{lead.email}</div>
                                <div className="text-xs text-gray-400">{lead.id}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">{lead.company}</div>
                            <div className="text-sm text-gray-500">{lead.phone}</div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{lead.source}</Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStageColor(lead.stage)}>{lead.stage}</Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <div className={`font-semibold ${getScoreColor(lead.score)}`}>{lead.score}</div>
                              <div className="flex">
                                {[...Array(5)].map((_, i) => (
                                  <Star
                                    key={i}
                                    className={`h-3 w-3 ${
                                      i < Math.floor(lead.score / 20) ? "text-yellow-400 fill-current" : "text-gray-300"
                                    }`}
                                  />
                                ))}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-semibold">{formatCurrency(lead.value)}</div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(lead.status)}>{lead.status}</Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">{lead.assignedTo}</div>
                            <div className="text-xs text-gray-500">{lead.lastActivity}</div>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem>
                                  <UserCheck className="mr-2 h-4 w-4" />
                                  View Profile
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Mail className="mr-2 h-4 w-4" />
                                  Send Email
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Phone className="mr-2 h-4 w-4" />
                                  Schedule Call
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Calendar className="mr-2 h-4 w-4" />
                                  Book Meeting
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Target className="mr-2 h-4 w-4" />
                                  Convert to Deal
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Activities Tab */}
          <TabsContent value="activities" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="h-5 w-5 mr-2 text-orange-600" />
                  Recent Activities
                </CardTitle>
                <CardDescription>Track all customer interactions and activities</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivities.map((activity) => (
                    <div
                      key={activity.id}
                      className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="p-2 bg-gray-100 rounded-lg">{getActivityIcon(activity.type)}</div>
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{activity.description}</div>
                        <div className="text-sm text-gray-600">
                          Contact: {activity.contact} • By: {activity.user}
                        </div>
                        <div className="text-xs text-gray-500">{activity.time}</div>
                      </div>
                      <Button variant="ghost" size="sm">
                        View Details
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Workflows Tab */}
          <TabsContent value="workflows" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center">
                      <Zap className="h-5 w-5 mr-2 text-yellow-600" />
                      Automated Workflows
                    </CardTitle>
                    <CardDescription>Automate your sales and marketing processes</CardDescription>
                  </div>
                  <Button className="bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-700 hover:to-orange-700">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Workflow
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {automatedWorkflows.map((workflow) => (
                    <Card key={workflow.id} className="hover:shadow-lg transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">{workflow.name}</CardTitle>
                          <Badge
                            className={
                              workflow.status === "Active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                            }
                          >
                            {workflow.status}
                          </Badge>
                        </div>
                        <CardDescription>{workflow.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">Trigger</span>
                            <Badge variant="outline">{workflow.trigger}</Badge>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">Active Leads</span>
                            <span className="font-semibold">{workflow.leads}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">Conversion Rate</span>
                            <span className="font-semibold text-green-600">{workflow.conversion}</span>
                          </div>
                        </div>
                        <div className="flex space-x-2 mt-4">
                          <Button variant="outline" size="sm" className="flex-1 bg-transparent">
                            <Settings className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                          <Button variant="outline" size="sm" className="flex-1 bg-transparent">
                            <BarChart3 className="h-3 w-3 mr-1" />
                            Analytics
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Reports Tab */}
          <TabsContent value="reports" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <BarChart3 className="h-5 w-5 mr-2 text-blue-600" />
                    Sales Performance
                  </CardTitle>
                  <CardDescription>Revenue, deals closed, and team performance</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">This Month Revenue</span>
                      <span className="font-semibold">{formatCurrency(360000)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Deals Closed</span>
                      <span className="font-semibold">16</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Win Rate</span>
                      <span className="font-semibold text-green-600">68%</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <PieChart className="h-5 w-5 mr-2 text-green-600" />
                    Lead Sources
                  </CardTitle>
                  <CardDescription>Lead generation and source analysis</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Website</span>
                      <span className="font-semibold">35%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">LinkedIn</span>
                      <span className="font-semibold">28%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Referrals</span>
                      <span className="font-semibold">22%</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <TrendingUp className="h-5 w-5 mr-2 text-purple-600" />
                    Pipeline Analysis
                  </CardTitle>
                  <CardDescription>Pipeline health and conversion metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Pipeline Value</span>
                      <span className="font-semibold">{formatCurrency(2400000)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Avg. Deal Size</span>
                      <span className="font-semibold">{formatCurrency(21053)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Sales Cycle</span>
                      <span className="font-semibold">45 days</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
