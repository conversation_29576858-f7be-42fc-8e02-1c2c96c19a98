"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Bell, X, CheckCircle, AlertTriangle, Info, TrendingUp, Eye, BarChart3, Brain, Target, Zap } from "lucide-react"

export default function AnalyticsNotifications() {
  const [notifications, setNotifications] = useState([])
  const [filter, setFilter] = useState("all")
  const [showAll, setShowAll] = useState(false)

  useEffect(() => {
    // Mock analytics notifications
    const mockNotifications = [
      {
        id: 1,
        type: "insight",
        priority: "high",
        title: "Revenue Spike Detected",
        message: "Consultation revenue increased 34% this week. Consider expanding availability.",
        module: "Analytics",
        time: "5 minutes ago",
        read: false,
        actionable: true,
        data: { metric: "revenue", change: "+34%", module: "consultations" },
      },
      {
        id: 2,
        type: "alert",
        priority: "medium",
        title: "Performance Threshold Exceeded",
        message: "System response time exceeded 2s threshold. Investigation recommended.",
        module: "Analytics",
        time: "15 minutes ago",
        read: false,
        actionable: true,
        data: { metric: "response_time", value: "2.3s", threshold: "2.0s" },
      },
      {
        id: 3,
        type: "report",
        priority: "low",
        title: "Weekly Report Generated",
        message: "Executive summary report for week ending Jan 15 is ready for review.",
        module: "Analytics",
        time: "2 hours ago",
        read: true,
        actionable: false,
        data: { report: "executive_summary", period: "week_ending_jan_15" },
      },
      {
        id: 4,
        type: "trend",
        priority: "medium",
        title: "User Engagement Trending Up",
        message: "Daily active users increased 18% over the past 7 days.",
        module: "Analytics",
        time: "4 hours ago",
        read: false,
        actionable: false,
        data: { metric: "dau", change: "+18%", period: "7_days" },
      },
      {
        id: 5,
        type: "anomaly",
        priority: "high",
        title: "Unusual Activity Pattern",
        message: "Detected 300% increase in API calls from IT Management module.",
        module: "Analytics",
        time: "6 hours ago",
        read: true,
        actionable: true,
        data: { metric: "api_calls", change: "+300%", module: "it_management" },
      },
    ]
    setNotifications(mockNotifications)
  }, [])

  const getNotificationIcon = (type) => {
    switch (type) {
      case "insight":
        return <Brain className="h-4 w-4 text-purple-600" />
      case "alert":
        return <AlertTriangle className="h-4 w-4 text-orange-600" />
      case "report":
        return <BarChart3 className="h-4 w-4 text-blue-600" />
      case "trend":
        return <TrendingUp className="h-4 w-4 text-green-600" />
      case "anomaly":
        return <Zap className="h-4 w-4 text-red-600" />
      default:
        return <Info className="h-4 w-4 text-gray-600" />
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800 border-red-200"
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "low":
        return "bg-green-100 text-green-800 border-green-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getNotificationBg = (type, read) => {
    if (read) return "bg-gray-50"
    switch (type) {
      case "insight":
        return "bg-purple-50 border-l-purple-500"
      case "alert":
        return "bg-orange-50 border-l-orange-500"
      case "report":
        return "bg-blue-50 border-l-blue-500"
      case "trend":
        return "bg-green-50 border-l-green-500"
      case "anomaly":
        return "bg-red-50 border-l-red-500"
      default:
        return "bg-gray-50 border-l-gray-500"
    }
  }

  const markAsRead = (id) => {
    setNotifications(notifications.map((n) => (n.id === id ? { ...n, read: true } : n)))
  }

  const markAllAsRead = () => {
    setNotifications(notifications.map((n) => ({ ...n, read: true })))
  }

  const dismissNotification = (id) => {
    setNotifications(notifications.filter((n) => n.id !== id))
  }

  const filteredNotifications = notifications.filter((notification) => {
    if (filter === "all") return true
    if (filter === "unread") return !notification.read
    if (filter === "actionable") return notification.actionable
    return notification.priority === filter
  })

  const displayedNotifications = showAll ? filteredNotifications : filteredNotifications.slice(0, 3)
  const unreadCount = notifications.filter((n) => !n.read).length

  if (notifications.length === 0) return null

  return (
    <Card className="border-l-4 border-l-purple-500">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bell className="h-5 w-5 text-purple-600" />
            <CardTitle className="text-lg">Analytics Insights</CardTitle>
            {unreadCount > 0 && (
              <Badge className="bg-purple-100 text-purple-800 border-purple-200">{unreadCount} new</Badge>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <Select value={filter} onValueChange={setFilter}>
              <SelectTrigger className="w-32 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="unread">Unread</SelectItem>
                <SelectItem value="actionable">Actionable</SelectItem>
                <SelectItem value="high">High Priority</SelectItem>
                <SelectItem value="medium">Medium Priority</SelectItem>
                <SelectItem value="low">Low Priority</SelectItem>
              </SelectContent>
            </Select>
            {unreadCount > 0 && (
              <Button size="sm" variant="ghost" onClick={markAllAsRead} className="h-8 text-xs">
                <CheckCircle className="h-3 w-3 mr-1" />
                Mark All Read
              </Button>
            )}
          </div>
        </div>
        <CardDescription>AI-powered insights and analytics notifications</CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          {displayedNotifications.map((notification) => (
            <div
              key={notification.id}
              className={`p-4 rounded-lg border-l-4 transition-all duration-200 ${getNotificationBg(
                notification.type,
                notification.read,
              )} ${!notification.read ? "shadow-sm" : ""}`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                  <div className="mt-0.5">{getNotificationIcon(notification.type)}</div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className={`text-sm font-medium ${notification.read ? "text-gray-700" : "text-gray-900"}`}>
                        {notification.title}
                      </h4>
                      <Badge className={`text-xs ${getPriorityColor(notification.priority)}`}>
                        {notification.priority}
                      </Badge>
                      {notification.actionable && (
                        <Badge variant="outline" className="text-xs border-blue-200 text-blue-700">
                          <Target className="h-3 w-3 mr-1" />
                          Actionable
                        </Badge>
                      )}
                    </div>
                    <p className={`text-sm ${notification.read ? "text-gray-600" : "text-gray-800"} mb-2`}>
                      {notification.message}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <span className="text-xs text-gray-500">{notification.time}</span>
                        <span className="text-xs text-gray-500">•</span>
                        <span className="text-xs text-gray-500">{notification.module}</span>
                        {notification.data && (
                          <>
                            <span className="text-xs text-gray-500">•</span>
                            <span className="text-xs font-medium text-purple-600">
                              {notification.data.metric}: {notification.data.change || notification.data.value}
                            </span>
                          </>
                        )}
                      </div>
                      <div className="flex items-center space-x-1">
                        {notification.actionable && (
                          <Button size="sm" variant="ghost" className="h-6 px-2 text-xs">
                            <Eye className="h-3 w-3 mr-1" />
                            View
                          </Button>
                        )}
                        {!notification.read && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => markAsRead(notification.id)}
                            className="h-6 px-2 text-xs"
                          >
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Read
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => dismissNotification(notification.id)}
                          className="h-6 px-2 text-xs text-gray-500 hover:text-red-600"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        {filteredNotifications.length > 3 && (
          <div className="mt-4 text-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAll(!showAll)}
              className="text-purple-600 hover:text-purple-700"
            >
              {showAll ? "Show Less" : `Show ${filteredNotifications.length - 3} More`}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
