"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Package,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  QrCode,
  DollarSign,
  TrendingUp,
  CheckCircle,
  MoreHorizontal,
  ArrowRightLeft,
  FileIcon as FileTemplate,
  Wrench,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import Link from "next/link"

export default function AssetsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [isNewAssetOpen, setIsNewAssetOpen] = useState(false)

  // Mock data for assets
  const [assets] = useState([
    {
      id: "AST-001",
      name: "MacBook Pro 16-inch",
      category: "Laptops",
      serialNumber: "MBP2024001",
      status: "active",
      condition: "excellent",
      assignedTo: "John Doe",
      location: "Office Floor 2",
      purchaseDate: "2024-01-01",
      purchasePrice: 2499.99,
      currentValue: 2000.0,
      warrantyExpiry: "2027-01-01",
      lastMaintenance: "2024-01-10",
      nextMaintenance: "2024-07-10",
      qrCode: "QR-AST-001",
    },
    {
      id: "AST-002",
      name: "Office Printer HP LaserJet",
      category: "Printers",
      serialNumber: "HP2024002",
      status: "active",
      condition: "good",
      assignedTo: "IT Department",
      location: "Office Floor 1",
      purchaseDate: "2023-06-15",
      purchasePrice: 899.99,
      currentValue: 650.0,
      warrantyExpiry: "2026-06-15",
      lastMaintenance: "2024-01-05",
      nextMaintenance: "2024-04-05",
      qrCode: "QR-AST-002",
    },
    {
      id: "AST-003",
      name: "Conference Room Table",
      category: "Furniture",
      serialNumber: "FUR2024003",
      status: "active",
      condition: "good",
      assignedTo: "Conference Room A",
      location: "Office Floor 3",
      purchaseDate: "2023-03-20",
      purchasePrice: 1299.99,
      currentValue: 900.0,
      warrantyExpiry: "2028-03-20",
      lastMaintenance: "2023-12-15",
      nextMaintenance: "2024-06-15",
      qrCode: "QR-AST-003",
    },
    {
      id: "AST-004",
      name: "Company Vehicle - Toyota Camry",
      category: "Vehicles",
      serialNumber: "TOY2024004",
      status: "active",
      condition: "excellent",
      assignedTo: "Sales Team",
      location: "Parking Lot",
      purchaseDate: "2023-08-10",
      purchasePrice: 28999.99,
      currentValue: 24000.0,
      warrantyExpiry: "2026-08-10",
      lastMaintenance: "2024-01-12",
      nextMaintenance: "2024-04-12",
      qrCode: "QR-AST-004",
    },
    {
      id: "AST-005",
      name: "Industrial 3D Printer",
      category: "Equipment",
      serialNumber: "3DP2024005",
      status: "maintenance",
      condition: "fair",
      assignedTo: "Production Floor",
      location: "Warehouse",
      purchaseDate: "2022-11-30",
      purchasePrice: 15999.99,
      currentValue: 10000.0,
      warrantyExpiry: "2025-11-30",
      lastMaintenance: "2024-01-08",
      nextMaintenance: "2024-02-08",
      qrCode: "QR-AST-005",
    },
    {
      id: "AST-006",
      name: "Dell XPS 13",
      category: "Laptops",
      serialNumber: "DELL2024006",
      status: "active",
      condition: "good",
      assignedTo: "Sarah Wilson",
      location: "Office Floor 3",
      purchaseDate: "2023-09-22",
      purchasePrice: 1899.99,
      currentValue: 1400.0,
      warrantyExpiry: "2026-09-22",
      lastMaintenance: "2024-01-14",
      nextMaintenance: "2024-07-14",
      qrCode: "QR-AST-006",
    },
  ])

  const stats = [
    {
      title: "Total Assets",
      value: "156",
      change: "+12",
      icon: Package,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Active Assets",
      value: "142",
      change: "+8",
      icon: CheckCircle,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Total Value",
      value: "$2.4M",
      change: "+5.2%",
      icon: DollarSign,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      title: "Maintenance Due",
      value: "8",
      change: "-2",
      icon: Wrench,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
  ]

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800"
      case "inactive":
        return "bg-gray-100 text-gray-800"
      case "maintenance":
        return "bg-orange-100 text-orange-800"
      case "retired":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getConditionColor = (condition) => {
    switch (condition.toLowerCase()) {
      case "excellent":
        return "bg-green-100 text-green-800"
      case "good":
        return "bg-blue-100 text-blue-800"
      case "fair":
        return "bg-yellow-100 text-yellow-800"
      case "poor":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const filteredAssets = assets.filter((asset) => {
    const matchesSearch =
      asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      asset.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      asset.assignedTo.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = categoryFilter === "all" || asset.category === categoryFilter
    const matchesStatus = statusFilter === "all" || asset.status === statusFilter
    return matchesSearch && matchesCategory && matchesStatus
  })

  const handleNewAsset = (e) => {
    e.preventDefault()
    // Handle asset creation logic here
    setIsNewAssetOpen(false)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Asset Management</h1>
            <p className="text-gray-600 mt-1">Track and manage your organization's assets</p>
          </div>
          <div className="flex space-x-3">
            <Link href="/dashboard/assets/transfers">
              <Button variant="outline" className="flex items-center bg-transparent">
                <ArrowRightLeft className="h-4 w-4 mr-2" />
                Asset Transfers
              </Button>
            </Link>
            <Link href="/dashboard/assets/templates">
              <Button variant="outline" className="flex items-center bg-transparent">
                <FileTemplate className="h-4 w-4 mr-2" />
                Transfer Templates
              </Button>
            </Link>
            <Dialog open={isNewAssetOpen} onOpenChange={setIsNewAssetOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Asset
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Add New Asset</DialogTitle>
                  <DialogDescription>Register a new asset in the system</DialogDescription>
                </DialogHeader>
                <form onSubmit={handleNewAsset}>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="asset-name">Asset Name</Label>
                        <Input id="asset-name" placeholder="Enter asset name" required />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="asset-category">Category</Label>
                        <Select required>
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="laptops">Laptops</SelectItem>
                            <SelectItem value="desktops">Desktops</SelectItem>
                            <SelectItem value="printers">Printers</SelectItem>
                            <SelectItem value="furniture">Furniture</SelectItem>
                            <SelectItem value="vehicles">Vehicles</SelectItem>
                            <SelectItem value="equipment">Equipment</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="serial-number">Serial Number</Label>
                        <Input id="serial-number" placeholder="Enter serial number" required />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="purchase-price">Purchase Price</Label>
                        <Input id="purchase-price" type="number" placeholder="0.00" required />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="assigned-to">Assigned To</Label>
                        <Input id="assigned-to" placeholder="User or department" required />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="location">Location</Label>
                        <Input id="location" placeholder="Asset location" required />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="purchase-date">Purchase Date</Label>
                        <Input id="purchase-date" type="date" required />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="warranty-expiry">Warranty Expiry</Label>
                        <Input id="warranty-expiry" type="date" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea id="description" placeholder="Additional asset details" />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setIsNewAssetOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" className="bg-gradient-to-r from-indigo-600 to-purple-600">
                      <Package className="h-4 w-4 mr-2" />
                      Add Asset
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className={`text-sm ${stat.change.startsWith("+") ? "text-green-600" : "text-red-600"}`}>
                      {stat.change} from last month
                    </p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content */}
        <Tabs defaultValue="assets" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="assets">All Assets</TabsTrigger>
            <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
            <TabsTrigger value="depreciation">Depreciation</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>

          <TabsContent value="assets" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Asset Inventory</CardTitle>
                    <CardDescription>Manage and track all organizational assets</CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                    <Button variant="outline" size="sm">
                      <Filter className="h-4 w-4 mr-2" />
                      Filter
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search assets..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Filter by category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      <SelectItem value="Laptops">Laptops</SelectItem>
                      <SelectItem value="Printers">Printers</SelectItem>
                      <SelectItem value="Furniture">Furniture</SelectItem>
                      <SelectItem value="Vehicles">Vehicles</SelectItem>
                      <SelectItem value="Equipment">Equipment</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="maintenance">Maintenance</SelectItem>
                      <SelectItem value="retired">Retired</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Asset</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Condition</TableHead>
                        <TableHead>Assigned To</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Value</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredAssets.map((asset) => (
                        <TableRow key={asset.id}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <div className="bg-gradient-to-r from-indigo-100 to-purple-100 p-2 rounded-lg">
                                <Package className="h-4 w-4 text-indigo-600" />
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">{asset.name}</div>
                                <div className="text-sm text-gray-500">{asset.id}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{asset.category}</Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(asset.status)}>{asset.status.toUpperCase()}</Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={getConditionColor(asset.condition)} variant="outline">
                              {asset.condition.toUpperCase()}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">{asset.assignedTo}</div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">{asset.location}</div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm font-medium">${asset.currentValue.toLocaleString()}</div>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem asChild>
                                  <Link href={`/dashboard/assets/asset/${asset.id}`}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                  </Link>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit Asset
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <QrCode className="mr-2 h-4 w-4" />
                                  Generate QR Code
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <ArrowRightLeft className="mr-2 h-4 w-4" />
                                  Transfer Asset
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-red-600">
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete Asset
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="maintenance" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Wrench className="h-5 w-5 mr-2 text-orange-600" />
                  Maintenance Schedule
                </CardTitle>
                <CardDescription>Track asset maintenance and service schedules</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {assets
                    .filter(
                      (asset) =>
                        asset.status === "maintenance" ||
                        new Date(asset.nextMaintenance) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
                    )
                    .map((asset) => (
                      <div key={asset.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="bg-orange-100 p-2 rounded-lg">
                              <Wrench className="h-5 w-5 text-orange-600" />
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900">{asset.name}</h3>
                              <p className="text-sm text-gray-600">
                                {asset.id} • {asset.location}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium">Next Maintenance</div>
                            <div className="text-sm text-gray-600">{asset.nextMaintenance}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="depreciation" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-purple-600" />
                  Asset Depreciation
                </CardTitle>
                <CardDescription>Monitor asset value depreciation over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {assets.map((asset) => {
                    const depreciationRate = (
                      ((asset.purchasePrice - asset.currentValue) / asset.purchasePrice) *
                      100
                    ).toFixed(1)
                    return (
                      <div key={asset.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="bg-purple-100 p-2 rounded-lg">
                              <DollarSign className="h-5 w-5 text-purple-600" />
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900">{asset.name}</h3>
                              <p className="text-sm text-gray-600">{asset.id}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium">
                              ${asset.purchasePrice.toLocaleString()} → ${asset.currentValue.toLocaleString()}
                            </div>
                            <div className="text-sm text-red-600">-{depreciationRate}% depreciation</div>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reports" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Asset Summary Report</CardTitle>
                  <CardDescription>Overview of all assets by category and status</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Total Assets</span>
                      <span className="font-medium">{assets.length}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Active Assets</span>
                      <span className="font-medium">{assets.filter((a) => a.status === "active").length}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">In Maintenance</span>
                      <span className="font-medium">{assets.filter((a) => a.status === "maintenance").length}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Total Value</span>
                      <span className="font-medium">
                        ${assets.reduce((sum, asset) => sum + asset.currentValue, 0).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Maintenance Report</CardTitle>
                  <CardDescription>Upcoming maintenance and service requirements</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Due This Week</span>
                      <span className="font-medium text-orange-600">3</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Due This Month</span>
                      <span className="font-medium text-yellow-600">8</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Overdue</span>
                      <span className="font-medium text-red-600">2</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Completed This Month</span>
                      <span className="font-medium text-green-600">12</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
