"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Package,
  Plus,
  Search,
  Filter,
  Download,
  QrCode,
  Calendar,
  DollarSign,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  MapPin,
  User,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Wrench,
  BarChart3,
  FileText,
  Scan,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import Link from "next/link"

export default function AssetManagementPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [isAddAssetOpen, setIsAddAssetOpen] = useState(false)
  const [selectedAssets, setSelectedAssets] = useState([])

  // Mock data for assets
  const [assets] = useState([
    {
      id: "AST-001",
      name: "MacBook Pro 16-inch",
      category: "IT Equipment",
      status: "Active",
      assignedTo: "John Doe",
      location: "Office Floor 2",
      purchaseDate: "2023-01-15",
      purchasePrice: 2499.0,
      currentValue: 1874.25,
      depreciation: 25.0,
      nextMaintenance: "2024-03-15",
      condition: "Excellent",
      qrCode: "QR001",
      warrantyExpiry: "2025-01-15",
    },
    {
      id: "AST-002",
      name: "Office Printer HP LaserJet",
      category: "Office Equipment",
      status: "Maintenance",
      assignedTo: "IT Department",
      location: "Office Floor 1",
      purchaseDate: "2022-06-10",
      purchasePrice: 899.0,
      currentValue: 539.4,
      depreciation: 40.0,
      nextMaintenance: "2024-02-20",
      condition: "Good",
      qrCode: "QR002",
      warrantyExpiry: "2024-06-10",
    },
    {
      id: "AST-003",
      name: "Conference Room Table",
      category: "Furniture",
      status: "Active",
      assignedTo: "Conference Room A",
      location: "Office Floor 3",
      purchaseDate: "2021-03-20",
      purchasePrice: 1200.0,
      currentValue: 720.0,
      depreciation: 40.0,
      nextMaintenance: "2024-06-01",
      condition: "Good",
      qrCode: "QR003",
      warrantyExpiry: "2024-03-20",
    },
    {
      id: "AST-004",
      name: "Company Vehicle - Toyota Camry",
      category: "Vehicle",
      status: "Active",
      assignedTo: "Sales Team",
      location: "Parking Lot",
      purchaseDate: "2022-08-15",
      purchasePrice: 28500.0,
      currentValue: 19950.0,
      depreciation: 30.0,
      nextMaintenance: "2024-02-15",
      condition: "Excellent",
      qrCode: "QR004",
      warrantyExpiry: "2025-08-15",
    },
    {
      id: "AST-005",
      name: "Industrial 3D Printer",
      category: "Manufacturing",
      status: "Retired",
      assignedTo: "Production Floor",
      location: "Warehouse",
      purchaseDate: "2020-11-05",
      purchasePrice: 15000.0,
      currentValue: 3000.0,
      depreciation: 80.0,
      nextMaintenance: "N/A",
      condition: "Poor",
      qrCode: "QR005",
      warrantyExpiry: "2023-11-05",
    },
  ])

  const stats = [
    {
      title: "Total Assets",
      value: "456",
      change: "+12",
      icon: Package,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Asset Value",
      value: "$2.4M",
      change: "****%",
      icon: DollarSign,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Depreciation",
      value: "$180K",
      change: "****%",
      icon: TrendingDown,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
    {
      title: "Maintenance Due",
      value: "23",
      change: "-5",
      icon: AlertTriangle,
      color: "text-red-600",
      bgColor: "bg-red-100",
    },
  ]

  const categories = ["All", "IT Equipment", "Office Equipment", "Furniture", "Vehicle", "Manufacturing"]
  const statuses = ["All", "Active", "Maintenance", "Retired", "Disposed"]

  const filteredAssets = assets.filter((asset) => {
    const matchesSearch =
      asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      asset.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || asset.status.toLowerCase() === statusFilter.toLowerCase()
    const matchesCategory = categoryFilter === "all" || asset.category.toLowerCase() === categoryFilter.toLowerCase()
    return matchesSearch && matchesStatus && matchesCategory
  })

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800"
      case "maintenance":
        return "bg-yellow-100 text-yellow-800"
      case "retired":
        return "bg-gray-100 text-gray-800"
      case "disposed":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getConditionColor = (condition) => {
    switch (condition.toLowerCase()) {
      case "excellent":
        return "bg-green-100 text-green-800"
      case "good":
        return "bg-blue-100 text-blue-800"
      case "fair":
        return "bg-yellow-100 text-yellow-800"
      case "poor":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const handleAddAsset = (e) => {
    e.preventDefault()
    // Handle asset creation logic here
    setIsAddAssetOpen(false)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Asset Management</h1>
            <p className="text-gray-600 mt-1">Track, manage, and maintain your company assets</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" className="flex items-center bg-transparent">
              <Scan className="h-4 w-4 mr-2" />
              Scan QR Code
            </Button>
            <Dialog open={isAddAssetOpen} onOpenChange={setIsAddAssetOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Asset
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Add New Asset</DialogTitle>
                  <DialogDescription>Register a new asset in the system with QR code generation</DialogDescription>
                </DialogHeader>
                <form onSubmit={handleAddAsset}>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="asset-name">Asset Name</Label>
                        <Input id="asset-name" placeholder="Enter asset name" required />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="asset-category">Category</Label>
                        <Select required>
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="it-equipment">IT Equipment</SelectItem>
                            <SelectItem value="office-equipment">Office Equipment</SelectItem>
                            <SelectItem value="furniture">Furniture</SelectItem>
                            <SelectItem value="vehicle">Vehicle</SelectItem>
                            <SelectItem value="manufacturing">Manufacturing</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="purchase-price">Purchase Price</Label>
                        <Input id="purchase-price" type="number" placeholder="0.00" required />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="purchase-date">Purchase Date</Label>
                        <Input id="purchase-date" type="date" required />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="location">Location</Label>
                        <Input id="location" placeholder="Asset location" required />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="assigned-to">Assigned To</Label>
                        <Input id="assigned-to" placeholder="Person or department" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea id="description" placeholder="Asset description and notes" />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setIsAddAssetOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" className="bg-gradient-to-r from-indigo-600 to-purple-600">
                      Create Asset & Generate QR
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className={`text-sm ${stat.change.startsWith("+") ? "text-green-600" : "text-red-600"}`}>
                      {stat.change} from last month
                    </p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content */}
        <Tabs defaultValue="assets" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="assets">Assets</TabsTrigger>
            <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
            <TabsTrigger value="depreciation">Depreciation</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>

          <TabsContent value="assets" className="space-y-6">
            {/* Filters and Search */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Asset Inventory</CardTitle>
                    <CardDescription>Manage and track all company assets</CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                    <Button variant="outline" size="sm">
                      <Filter className="h-4 w-4 mr-2" />
                      Filter
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search assets..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      {statuses.map((status) => (
                        <SelectItem key={status} value={status.toLowerCase()}>
                          {status}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category} value={category.toLowerCase()}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Asset</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Assigned To</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Value</TableHead>
                        <TableHead>Condition</TableHead>
                        <TableHead>Next Maintenance</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredAssets.map((asset) => (
                        <TableRow key={asset.id}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <div className="bg-gradient-to-r from-indigo-100 to-purple-100 p-2 rounded-lg">
                                <Package className="h-4 w-4 text-indigo-600" />
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">{asset.name}</div>
                                <div className="text-sm text-gray-500">{asset.id}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{asset.category}</Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(asset.status)}>{asset.status}</Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <User className="h-4 w-4 text-gray-400" />
                              <span className="text-sm">{asset.assignedTo}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <MapPin className="h-4 w-4 text-gray-400" />
                              <span className="text-sm">{asset.location}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">${asset.currentValue.toLocaleString()}</div>
                              <div className="text-xs text-gray-500">-{asset.depreciation}% depreciated</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getConditionColor(asset.condition)}>{asset.condition}</Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4 text-gray-400" />
                              <span className="text-sm">{asset.nextMaintenance}</span>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem asChild>
                                  <Link href={`/dashboard/assets/asset/${asset.id}`}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                  </Link>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <QrCode className="mr-2 h-4 w-4" />
                                  View QR Code
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Wrench className="mr-2 h-4 w-4" />
                                  Schedule Maintenance
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit Asset
                                </DropdownMenuItem>
                                <DropdownMenuItem className="text-red-600">
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete Asset
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="maintenance" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Wrench className="h-5 w-5 mr-2 text-indigo-600" />
                  Maintenance Schedule
                </CardTitle>
                <CardDescription>Manage maintenance schedules and track service history</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <div className="lg:col-span-2">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold">Upcoming Maintenance</h3>
                        <Button size="sm" className="bg-gradient-to-r from-indigo-600 to-purple-600">
                          <Plus className="h-4 w-4 mr-2" />
                          Schedule Maintenance
                        </Button>
                      </div>
                      <div className="space-y-3">
                        {assets
                          .filter((asset) => asset.nextMaintenance !== "N/A")
                          .map((asset) => (
                            <div key={asset.id} className="flex items-center justify-between p-4 border rounded-lg">
                              <div className="flex items-center space-x-3">
                                <div className="bg-gradient-to-r from-indigo-100 to-purple-100 p-2 rounded-lg">
                                  <Package className="h-4 w-4 text-indigo-600" />
                                </div>
                                <div>
                                  <div className="font-medium">{asset.name}</div>
                                  <div className="text-sm text-gray-500">
                                    {asset.id} • {asset.location}
                                  </div>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="font-medium">{asset.nextMaintenance}</div>
                                <div className="text-sm text-gray-500">Preventive Maintenance</div>
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Maintenance Stats</h3>
                      <div className="space-y-3">
                        <div className="p-4 bg-green-50 rounded-lg">
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="text-2xl font-bold text-green-600">12</div>
                              <div className="text-sm text-green-700">Completed This Month</div>
                            </div>
                            <CheckCircle className="h-8 w-8 text-green-600" />
                          </div>
                        </div>
                        <div className="p-4 bg-yellow-50 rounded-lg">
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="text-2xl font-bold text-yellow-600">8</div>
                              <div className="text-sm text-yellow-700">In Progress</div>
                            </div>
                            <Clock className="h-8 w-8 text-yellow-600" />
                          </div>
                        </div>
                        <div className="p-4 bg-red-50 rounded-lg">
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="text-2xl font-bold text-red-600">3</div>
                              <div className="text-sm text-red-700">Overdue</div>
                            </div>
                            <AlertTriangle className="h-8 w-8 text-red-600" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="depreciation" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingDown className="h-5 w-5 mr-2 text-orange-600" />
                  Depreciation Analysis
                </CardTitle>
                <CardDescription>Track asset depreciation and calculate current values</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Depreciation Methods</h3>
                    <div className="space-y-4">
                      <div className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">Straight Line</h4>
                          <Badge variant="outline">Default</Badge>
                        </div>
                        <p className="text-sm text-gray-600">Equal depreciation over the asset's useful life</p>
                      </div>
                      <div className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">Declining Balance</h4>
                          <Badge variant="outline">Available</Badge>
                        </div>
                        <p className="text-sm text-gray-600">Higher depreciation in early years</p>
                      </div>
                      <div className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">Units of Production</h4>
                          <Badge variant="outline">Available</Badge>
                        </div>
                        <p className="text-sm text-gray-600">Based on actual usage or production</p>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Depreciation Summary</h3>
                    <div className="space-y-4">
                      <div className="p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-gray-900">$2.4M</div>
                        <div className="text-sm text-gray-600">Total Asset Value</div>
                      </div>
                      <div className="p-4 bg-orange-50 rounded-lg">
                        <div className="text-2xl font-bold text-orange-600">$180K</div>
                        <div className="text-sm text-orange-700">Total Depreciation</div>
                      </div>
                      <div className="p-4 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">$2.22M</div>
                        <div className="text-sm text-green-700">Current Book Value</div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2 text-purple-600" />
                  Asset Analytics
                </CardTitle>
                <CardDescription>Insights and trends for your asset portfolio</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Asset Distribution</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <span className="text-sm font-medium">IT Equipment</span>
                        <span className="text-sm text-blue-600">45% (205 assets)</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                        <span className="text-sm font-medium">Office Equipment</span>
                        <span className="text-sm text-green-600">25% (114 assets)</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                        <span className="text-sm font-medium">Furniture</span>
                        <span className="text-sm text-purple-600">20% (91 assets)</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                        <span className="text-sm font-medium">Vehicles</span>
                        <span className="text-sm text-orange-600">10% (46 assets)</span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Asset Age Analysis</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                        <span className="text-sm font-medium">0-1 Years</span>
                        <span className="text-sm text-green-600">15% (68 assets)</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <span className="text-sm font-medium">1-3 Years</span>
                        <span className="text-sm text-blue-600">35% (160 assets)</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                        <span className="text-sm font-medium">3-5 Years</span>
                        <span className="text-sm text-yellow-600">30% (137 assets)</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                        <span className="text-sm font-medium">5+ Years</span>
                        <span className="text-sm text-red-600">20% (91 assets)</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reports" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-green-600" />
                  Asset Reports
                </CardTitle>
                <CardDescription>Generate comprehensive asset reports for compliance and analysis</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="p-6 border rounded-lg hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-semibold">Asset Inventory Report</h3>
                      <Package className="h-5 w-5 text-blue-600" />
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      Complete listing of all assets with current status and values
                    </p>
                    <Button size="sm" className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      Generate Report
                    </Button>
                  </div>
                  <div className="p-6 border rounded-lg hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-semibold">Depreciation Report</h3>
                      <TrendingDown className="h-5 w-5 text-orange-600" />
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      Detailed depreciation calculations and tax implications
                    </p>
                    <Button size="sm" className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      Generate Report
                    </Button>
                  </div>
                  <div className="p-6 border rounded-lg hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-semibold">Maintenance Report</h3>
                      <Wrench className="h-5 w-5 text-green-600" />
                    </div>
                    <p className="text-sm text-gray-600 mb-4">Maintenance history, costs, and upcoming schedules</p>
                    <Button size="sm" className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      Generate Report
                    </Button>
                  </div>
                  <div className="p-6 border rounded-lg hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-semibold">Compliance Report</h3>
                      <CheckCircle className="h-5 w-5 text-purple-600" />
                    </div>
                    <p className="text-sm text-gray-600 mb-4">Regulatory compliance and audit trail documentation</p>
                    <Button size="sm" className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      Generate Report
                    </Button>
                  </div>
                  <div className="p-6 border rounded-lg hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-semibold">Cost Analysis Report</h3>
                      <DollarSign className="h-5 w-5 text-green-600" />
                    </div>
                    <p className="text-sm text-gray-600 mb-4">Total cost of ownership and ROI analysis</p>
                    <Button size="sm" className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      Generate Report
                    </Button>
                  </div>
                  <div className="p-6 border rounded-lg hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-semibold">Custom Report</h3>
                      <BarChart3 className="h-5 w-5 text-indigo-600" />
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      Create custom reports with specific filters and metrics
                    </p>
                    <Button size="sm" className="w-full">
                      <Plus className="h-4 w-4 mr-2" />
                      Create Custom
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
