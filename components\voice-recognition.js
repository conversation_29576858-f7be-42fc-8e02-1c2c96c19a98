"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Mic, MicOff, Volume2, Headphones, Zap, Brain, HelpCircle, Activity, CheckCircle, XCircle } from "lucide-react"
import { useVoiceCommands } from "@/hooks/use-voice-commands"
import { useSpeechRecognition } from "@/hooks/use-speech-recognition"

export default function VoiceRecognition() {
  const {
    isListening: commandListening,
    isProcessing,
    lastCommand,
    commandHistory,
    isSupported: commandSupported,
    startVoiceCommand,
    stopListening: stopCommandListening,
    showHelp,
  } = useVoiceCommands()

  const {
    isListening: dictationListening,
    transcript,
    interimTranscript,
    error,
    confidence,
    startListening: startDictation,
    stopListening: stopDictation,
    resetTranscript,
    isSupported: dictationSupported,
  } = useSpeechRecognition()

  const [mode, setMode] = useState("command") // "command" or "dictation"
  const [isHelpOpen, setIsHelpOpen] = useState(false)
  const [settings, setSettings] = useState({
    autoStop: true,
    audioFeedback: true,
    language: "en-US",
    sensitivity: 0.7,
  })

  const isSupported = commandSupported && dictationSupported
  const isListening = mode === "command" ? commandListening : dictationListening

  const handleStartListening = () => {
    if (mode === "command") {
      startVoiceCommand()
    } else {
      startDictation()
    }
  }

  const handleStopListening = () => {
    if (mode === "command") {
      stopCommandListening()
    } else {
      stopDictation()
    }
  }

  const copyTranscriptToClipboard = async () => {
    if (transcript) {
      try {
        await navigator.clipboard.writeText(transcript)
        // Show success feedback
        if (settings.audioFeedback && "speechSynthesis" in window) {
          const utterance = new SpeechSynthesisUtterance("Text copied to clipboard")
          utterance.rate = 1.2
          utterance.volume = 0.5
          speechSynthesis.speak(utterance)
        }
      } catch (err) {
        console.error("Failed to copy text:", err)
      }
    }
  }

  const insertTranscriptToActiveField = () => {
    const activeElement = document.activeElement
    if (activeElement && (activeElement.tagName === "INPUT" || activeElement.tagName === "TEXTAREA")) {
      const currentValue = activeElement.value
      const cursorPosition = activeElement.selectionStart
      const newValue = currentValue.slice(0, cursorPosition) + transcript + currentValue.slice(cursorPosition)
      activeElement.value = newValue
      activeElement.dispatchEvent(new Event("input", { bubbles: true }))

      // Move cursor to end of inserted text
      const newCursorPosition = cursorPosition + transcript.length
      activeElement.setSelectionRange(newCursorPosition, newCursorPosition)
    }
  }

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return "text-green-600"
    if (confidence >= 0.6) return "text-yellow-600"
    return "text-red-600"
  }

  const getConfidenceLabel = (confidence) => {
    if (confidence >= 0.8) return "High"
    if (confidence >= 0.6) return "Medium"
    return "Low"
  }

  if (!isSupported) {
    return (
      <Alert className="border-orange-200 bg-orange-50">
        <Headphones className="h-4 w-4" />
        <AlertDescription>
          Voice recognition is not supported in your browser. Please use Chrome, Edge, or Safari for the best
          experience.
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6">
      {/* Voice Control Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${isListening ? "bg-red-100" : "bg-blue-100"}`}>
                {isListening ? (
                  <Mic className={`h-6 w-6 ${isListening ? "text-red-600 animate-pulse" : "text-blue-600"}`} />
                ) : (
                  <MicOff className="h-6 w-6 text-gray-400" />
                )}
              </div>
              <div>
                <CardTitle className="text-xl">Voice Recognition</CardTitle>
                <CardDescription>
                  {mode === "command" ? "Voice commands for hands-free navigation" : "Voice-to-text dictation"}
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge className={isListening ? "bg-red-100 text-red-800" : "bg-gray-100 text-gray-800"}>
                {isListening ? "Listening" : "Idle"}
              </Badge>
              {isProcessing && (
                <Badge className="bg-yellow-100 text-yellow-800">
                  <Brain className="h-3 w-3 mr-1" />
                  Processing
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Mode Toggle */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="flex bg-gray-100 rounded-lg p-1">
              <Button
                variant={mode === "command" ? "default" : "ghost"}
                size="sm"
                onClick={() => setMode("command")}
                className={mode === "command" ? "bg-white shadow-sm" : ""}
              >
                <Zap className="h-4 w-4 mr-2" />
                Commands
              </Button>
              <Button
                variant={mode === "dictation" ? "default" : "ghost"}
                size="sm"
                onClick={() => setMode("dictation")}
                className={mode === "dictation" ? "bg-white shadow-sm" : ""}
              >
                <Mic className="h-4 w-4 mr-2" />
                Dictation
              </Button>
            </div>
          </div>

          {/* Control Buttons */}
          <div className="flex items-center space-x-3 mb-6">
            <Button
              onClick={isListening ? handleStopListening : handleStartListening}
              className={`flex-1 ${
                isListening ? "bg-red-600 hover:bg-red-700 text-white" : "bg-blue-600 hover:bg-blue-700 text-white"
              }`}
              disabled={isProcessing}
            >
              {isListening ? (
                <>
                  <MicOff className="h-4 w-4 mr-2" />
                  Stop Listening
                </>
              ) : (
                <>
                  <Mic className="h-4 w-4 mr-2" />
                  Start {mode === "command" ? "Voice Commands" : "Dictation"}
                </>
              )}
            </Button>

            <Dialog open={isHelpOpen} onOpenChange={setIsHelpOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="icon">
                  <HelpCircle className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Voice Commands Help</DialogTitle>
                  <DialogDescription>Learn how to use voice commands for hands-free navigation</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">Navigation Commands</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>"Go to dashboard"</div>
                      <div>"Open users"</div>
                      <div>"Go to tickets"</div>
                      <div>"Open projects"</div>
                      <div>"Go to analytics"</div>
                      <div>"Go back"</div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Action Commands</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>"Create new user"</div>
                      <div>"New ticket"</div>
                      <div>"Add asset"</div>
                      <div>"Schedule appointment"</div>
                      <div>"Refresh page"</div>
                      <div>"Search for [query]"</div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">System Commands</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>"Help"</div>
                      <div>"Stop listening"</div>
                      <div>"Cancel"</div>
                      <div>"What can I say"</div>
                    </div>
                  </div>
                </div>
              </DialogContent>
            </Dialog>

            <Button variant="outline" size="icon" onClick={showHelp}>
              <Volume2 className="h-4 w-4" />
            </Button>
          </div>

          {/* Status Display */}
          {mode === "command" && (
            <div className="space-y-4">
              {lastCommand && (
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-blue-900">Last Command:</span>
                    <Badge className="bg-blue-100 text-blue-800">Executed</Badge>
                  </div>
                  <p className="text-blue-700 mt-1">"{lastCommand}"</p>
                </div>
              )}

              {/* Command History */}
              {commandHistory.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-700">Recent Commands</h4>
                  <div className="space-y-1 max-h-32 overflow-y-auto">
                    {commandHistory
                      .slice(-5)
                      .reverse()
                      .map((cmd, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm">
                          <span className="truncate flex-1">"{cmd.command}"</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-gray-500">{cmd.timestamp.toLocaleTimeString()}</span>
                            {cmd.executed ? (
                              <CheckCircle className="h-3 w-3 text-green-600" />
                            ) : (
                              <XCircle className="h-3 w-3 text-red-600" />
                            )}
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Dictation Mode */}
          {mode === "dictation" && (
            <div className="space-y-4">
              {/* Live Transcript */}
              <div className="min-h-[120px] p-4 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Transcript</span>
                  {confidence > 0 && (
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-gray-500">Confidence:</span>
                      <Badge className={`text-xs ${getConfidenceColor(confidence)} bg-transparent border-0`}>
                        {getConfidenceLabel(confidence)} ({Math.round(confidence * 100)}%)
                      </Badge>
                    </div>
                  )}
                </div>
                <div className="text-gray-900">
                  {transcript && <span className="bg-yellow-100 px-1 rounded">{transcript}</span>}
                  {interimTranscript && <span className="text-gray-500 italic"> {interimTranscript}</span>}
                  {!transcript && !interimTranscript && (
                    <span className="text-gray-400 italic">
                      {isListening ? "Listening... speak now" : "Click 'Start Dictation' to begin"}
                    </span>
                  )}
                </div>
              </div>

              {/* Dictation Controls */}
              {transcript && (
                <div className="flex space-x-2">
                  <Button onClick={copyTranscriptToClipboard} variant="outline" size="sm">
                    Copy Text
                  </Button>
                  <Button onClick={insertTranscriptToActiveField} variant="outline" size="sm">
                    Insert to Field
                  </Button>
                  <Button onClick={resetTranscript} variant="outline" size="sm">
                    Clear
                  </Button>
                </div>
              )}

              {/* Audio Level Indicator */}
              {isListening && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Audio Level</span>
                    <Activity className="h-4 w-4 text-green-600 animate-pulse" />
                  </div>
                  <Progress value={75} className="h-2" />
                </div>
              )}
            </div>
          )}

          {/* Error Display */}
          {error && (
            <Alert className="border-red-200 bg-red-50">
              <XCircle className="h-4 w-4" />
              <AlertDescription className="text-red-800">
                Voice recognition error: {error}. Please try again.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Quick Tips */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Brain className="h-5 w-5 mr-2 text-purple-600" />
            Quick Tips
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">For Best Results:</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• Speak clearly and at normal pace</li>
                <li>• Use a quiet environment</li>
                <li>• Wait for the beep before speaking</li>
                <li>• Pause briefly between commands</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Troubleshooting:</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• Check microphone permissions</li>
                <li>• Ensure stable internet connection</li>
                <li>• Try refreshing the page</li>
                <li>• Use Chrome for best compatibility</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
