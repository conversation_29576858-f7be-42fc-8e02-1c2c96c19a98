"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import {
  DollarSign,
  Users,
  Search,
  MoreHorizontal,
  Download,
  Play,
  Pause,
  FileText,
  Calculator,
  CreditCard,
  AlertTriangle,
  CheckCircle,
  Clock,
  Receipt,
  PieChart,
  BarChart3,
  Mail,
  Settings,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"

export default function PayrollManagementPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedDepartment, setSelectedDepartment] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [isProcessingPayroll, setIsProcessingPayroll] = useState(false)
  const [processingProgress, setProcessingProgress] = useState(0)

  const payrollStats = [
    {
      title: "Total Employees",
      value: "247",
      change: "+5 this month",
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Monthly Payroll",
      value: "$485,230",
      change: "+8.2% from last month",
      icon: DollarSign,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Tax Compliance",
      value: "98.5%",
      change: "All requirements met",
      icon: CheckCircle,
      color: "text-emerald-600",
      bgColor: "bg-emerald-100",
    },
    {
      title: "Processing Time",
      value: "2.3 hrs",
      change: "-15% improvement",
      icon: Clock,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
  ]

  const employees = [
    {
      id: "EMP001",
      name: "John Doe",
      email: "<EMAIL>",
      department: "Engineering",
      position: "Senior Developer",
      salary: 95000,
      hourlyRate: 45.67,
      hoursWorked: 160,
      overtime: 8,
      grossPay: 8200,
      taxes: 1968,
      benefits: 450,
      netPay: 5782,
      status: "Processed",
      payDate: "2024-01-31",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: "EMP002",
      name: "Sarah Wilson",
      email: "<EMAIL>",
      department: "Marketing",
      position: "Marketing Manager",
      salary: 75000,
      hourlyRate: 36.06,
      hoursWorked: 160,
      overtime: 4,
      grossPay: 6400,
      taxes: 1536,
      benefits: 380,
      netPay: 4484,
      status: "Pending",
      payDate: "2024-01-31",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: "EMP003",
      name: "Mike Johnson",
      email: "<EMAIL>",
      department: "Sales",
      position: "Sales Representative",
      salary: 55000,
      hourlyRate: 26.44,
      hoursWorked: 160,
      overtime: 12,
      grossPay: 4800,
      taxes: 1152,
      benefits: 320,
      netPay: 3328,
      status: "Processed",
      payDate: "2024-01-31",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: "EMP004",
      name: "Lisa Chen",
      email: "<EMAIL>",
      department: "HR",
      position: "HR Specialist",
      salary: 65000,
      hourlyRate: 31.25,
      hoursWorked: 160,
      overtime: 0,
      grossPay: 5000,
      taxes: 1200,
      benefits: 350,
      netPay: 3450,
      status: "Review",
      payDate: "2024-01-31",
      avatar: "/placeholder.svg?height=40&width=40",
    },
  ]

  const taxBreakdown = [
    { name: "Federal Income Tax", amount: 45230, percentage: 24.5, color: "bg-red-500" },
    { name: "State Income Tax", amount: 18920, percentage: 10.2, color: "bg-orange-500" },
    { name: "Social Security", amount: 30084, percentage: 16.3, color: "bg-blue-500" },
    { name: "Medicare", amount: 7036, percentage: 3.8, color: "bg-green-500" },
    { name: "Unemployment Tax", amount: 2890, percentage: 1.6, color: "bg-purple-500" },
    { name: "Workers Comp", amount: 1840, percentage: 1.0, color: "bg-pink-500" },
  ]

  const recentPayrolls = [
    {
      id: "PR-2024-01",
      period: "January 2024",
      employees: 247,
      grossAmount: 485230,
      netAmount: 342161,
      status: "Completed",
      processedDate: "2024-01-31",
      processedBy: "Sarah Wilson",
    },
    {
      id: "PR-2023-12",
      period: "December 2023",
      employees: 242,
      grossAmount: 468920,
      netAmount: 330244,
      status: "Completed",
      processedDate: "2023-12-31",
      processedBy: "Sarah Wilson",
    },
    {
      id: "PR-2023-11",
      period: "November 2023",
      employees: 238,
      grossAmount: 452180,
      netAmount: 318526,
      status: "Completed",
      processedDate: "2023-11-30",
      processedBy: "Mike Johnson",
    },
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case "Processed":
        return "bg-green-100 text-green-800"
      case "Pending":
        return "bg-yellow-100 text-yellow-800"
      case "Review":
        return "bg-orange-100 text-orange-800"
      case "Error":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const handleProcessPayroll = async () => {
    setIsProcessingPayroll(true)
    setProcessingProgress(0)

    // Simulate payroll processing with progress updates
    const steps = [
      "Validating employee data...",
      "Calculating gross pay...",
      "Computing tax deductions...",
      "Processing benefits...",
      "Generating payslips...",
      "Finalizing payments...",
    ]

    for (let i = 0; i < steps.length; i++) {
      await new Promise((resolve) => setTimeout(resolve, 1000))
      setProcessingProgress(((i + 1) / steps.length) * 100)
    }

    setIsProcessingPayroll(false)
    setProcessingProgress(0)
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Payroll Management</h1>
            <p className="text-gray-600 mt-1">Automated payroll processing with tax compliance and reporting</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Reports
            </Button>
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Payroll Settings
            </Button>
            <Dialog>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700">
                  <Play className="h-4 w-4 mr-2" />
                  Process Payroll
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Process Monthly Payroll</DialogTitle>
                  <DialogDescription>
                    This will process payroll for all active employees for the current pay period.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label>Pay Period</Label>
                    <Select defaultValue="january-2024">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="january-2024">January 2024</SelectItem>
                        <SelectItem value="february-2024">February 2024</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Employees to Process</Label>
                    <div className="text-sm text-gray-600">247 active employees</div>
                  </div>
                  <div className="space-y-2">
                    <Label>Estimated Total</Label>
                    <div className="text-lg font-semibold text-green-600">{formatCurrency(485230)}</div>
                  </div>
                  {isProcessingPayroll && (
                    <div className="space-y-2">
                      <Label>Processing Progress</Label>
                      <Progress value={processingProgress} className="w-full" />
                      <div className="text-sm text-gray-600">{processingProgress.toFixed(0)}% complete</div>
                    </div>
                  )}
                </div>
                <DialogFooter>
                  <Button
                    onClick={handleProcessPayroll}
                    disabled={isProcessingPayroll}
                    className="bg-gradient-to-r from-green-600 to-emerald-600"
                  >
                    {isProcessingPayroll ? (
                      <>
                        <Pause className="h-4 w-4 mr-2" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Play className="h-4 w-4 mr-2" />
                        Start Processing
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {payrollStats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-green-600">{stat.change}</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="employees" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="employees">Employees</TabsTrigger>
            <TabsTrigger value="processing">Processing</TabsTrigger>
            <TabsTrigger value="taxes">Tax Compliance</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>

          {/* Employees Tab */}
          <TabsContent value="employees" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Employee Payroll Overview</CardTitle>
                <CardDescription>Manage employee payroll information and calculations</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search employees by name, ID, or department..."
                      className="pl-10"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
                    <SelectTrigger className="w-full sm:w-[180px]">
                      <SelectValue placeholder="Department" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Departments</SelectItem>
                      <SelectItem value="engineering">Engineering</SelectItem>
                      <SelectItem value="marketing">Marketing</SelectItem>
                      <SelectItem value="sales">Sales</SelectItem>
                      <SelectItem value="hr">HR</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                    <SelectTrigger className="w-full sm:w-[180px]">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="processed">Processed</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="review">Review</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Employee</TableHead>
                        <TableHead>Department</TableHead>
                        <TableHead>Hours</TableHead>
                        <TableHead>Gross Pay</TableHead>
                        <TableHead>Deductions</TableHead>
                        <TableHead>Net Pay</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {employees.map((employee) => (
                        <TableRow key={employee.id}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <Avatar>
                                <AvatarImage src={employee.avatar || "/placeholder.svg"} alt={employee.name} />
                                <AvatarFallback>
                                  {employee.name
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="font-medium text-gray-900">{employee.name}</div>
                                <div className="text-sm text-gray-500">{employee.position}</div>
                                <div className="text-xs text-gray-400">{employee.id}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{employee.department}</Badge>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="text-sm font-medium">{employee.hoursWorked}h regular</div>
                              {employee.overtime > 0 && (
                                <div className="text-xs text-orange-600">+{employee.overtime}h overtime</div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">{formatCurrency(employee.grossPay)}</div>
                            <div className="text-xs text-gray-500">{formatCurrency(employee.hourlyRate)}/hr</div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="text-sm text-red-600">-{formatCurrency(employee.taxes)} (tax)</div>
                              <div className="text-xs text-blue-600">
                                -{formatCurrency(employee.benefits)} (benefits)
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-semibold text-green-600">{formatCurrency(employee.netPay)}</div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(employee.status)}>{employee.status}</Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem>
                                  <FileText className="mr-2 h-4 w-4" />
                                  View Payslip
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Calculator className="mr-2 h-4 w-4" />
                                  Recalculate
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Mail className="mr-2 h-4 w-4" />
                                  Send Payslip
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <CreditCard className="mr-2 h-4 w-4" />
                                  Payment Details
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tax Compliance Tab */}
          <TabsContent value="taxes" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <PieChart className="h-5 w-5 mr-2 text-blue-600" />
                    Tax Breakdown
                  </CardTitle>
                  <CardDescription>Current month tax deductions by category</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {taxBreakdown.map((tax, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">{tax.name}</span>
                          <span className="text-sm font-semibold">{formatCurrency(tax.amount)}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="flex-1 bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${tax.color}`}
                              style={{ width: `${tax.percentage}%` }}
                            ></div>
                          </div>
                          <span className="text-xs text-gray-500 w-12">{tax.percentage}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="mt-6 pt-4 border-t">
                    <div className="flex items-center justify-between font-semibold">
                      <span>Total Tax Deductions</span>
                      <span className="text-lg">{formatCurrency(106000)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                    Compliance Status
                  </CardTitle>
                  <CardDescription>Tax compliance and regulatory requirements</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                      <div className="flex items-center space-x-3">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <div>
                          <div className="font-medium text-green-900">Federal Tax Filing</div>
                          <div className="text-sm text-green-700">Form 941 submitted on time</div>
                        </div>
                      </div>
                      <Badge className="bg-green-100 text-green-800">Compliant</Badge>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                      <div className="flex items-center space-x-3">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <div>
                          <div className="font-medium text-green-900">State Tax Compliance</div>
                          <div className="text-sm text-green-700">All state requirements met</div>
                        </div>
                      </div>
                      <Badge className="bg-green-100 text-green-800">Compliant</Badge>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                      <div className="flex items-center space-x-3">
                        <AlertTriangle className="h-5 w-5 text-yellow-600" />
                        <div>
                          <div className="font-medium text-yellow-900">W-2 Forms</div>
                          <div className="text-sm text-yellow-700">Due in 15 days</div>
                        </div>
                      </div>
                      <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                      <div className="flex items-center space-x-3">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <div>
                          <div className="font-medium text-green-900">Unemployment Tax</div>
                          <div className="text-sm text-green-700">SUTA payments current</div>
                        </div>
                      </div>
                      <Badge className="bg-green-100 text-green-800">Compliant</Badge>
                    </div>
                  </div>

                  <div className="mt-6 pt-4 border-t">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Overall Compliance Score</span>
                      <div className="flex items-center space-x-2">
                        <Progress value={98.5} className="w-20" />
                        <span className="font-semibold text-green-600">98.5%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Reports Tab */}
          <TabsContent value="reports" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <BarChart3 className="h-5 w-5 mr-2 text-blue-600" />
                    Payroll Summary
                  </CardTitle>
                  <CardDescription>Monthly payroll overview and trends</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Gross Pay</span>
                      <span className="font-semibold">{formatCurrency(485230)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Deductions</span>
                      <span className="font-semibold text-red-600">{formatCurrency(143069)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Net Pay</span>
                      <span className="font-semibold text-green-600">{formatCurrency(342161)}</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <Receipt className="h-5 w-5 mr-2 text-green-600" />
                    Tax Reports
                  </CardTitle>
                  <CardDescription>Tax deductions and compliance reports</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Federal Taxes</span>
                      <span className="font-semibold">{formatCurrency(45230)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">State Taxes</span>
                      <span className="font-semibold">{formatCurrency(18920)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">FICA Taxes</span>
                      <span className="font-semibold">{formatCurrency(37120)}</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <Users className="h-5 w-5 mr-2 text-purple-600" />
                    Employee Reports
                  </CardTitle>
                  <CardDescription>Individual employee payroll details</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Active Employees</span>
                      <span className="font-semibold">247</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Avg. Gross Pay</span>
                      <span className="font-semibold">{formatCurrency(1964)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Avg. Net Pay</span>
                      <span className="font-semibold">{formatCurrency(1386)}</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* History Tab */}
          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Payroll History</CardTitle>
                <CardDescription>Previous payroll runs and their details</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Payroll ID</TableHead>
                        <TableHead>Period</TableHead>
                        <TableHead>Employees</TableHead>
                        <TableHead>Gross Amount</TableHead>
                        <TableHead>Net Amount</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Processed By</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {recentPayrolls.map((payroll) => (
                        <TableRow key={payroll.id}>
                          <TableCell className="font-medium">{payroll.id}</TableCell>
                          <TableCell>{payroll.period}</TableCell>
                          <TableCell>{payroll.employees}</TableCell>
                          <TableCell>{formatCurrency(payroll.grossAmount)}</TableCell>
                          <TableCell className="font-semibold text-green-600">
                            {formatCurrency(payroll.netAmount)}
                          </TableCell>
                          <TableCell>
                            <Badge className="bg-green-100 text-green-800">{payroll.status}</Badge>
                          </TableCell>
                          <TableCell>{payroll.processedBy}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem>
                                  <FileText className="mr-2 h-4 w-4" />
                                  View Details
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Download className="mr-2 h-4 w-4" />
                                  Download Report
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Mail className="mr-2 h-4 w-4" />
                                  Send Summary
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
