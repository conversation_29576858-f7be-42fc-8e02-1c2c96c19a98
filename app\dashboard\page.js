"use client"

import { useEffect, useState } from "react"
import DashboardLayout from "@/components/dashboard-layout"
import MobileLayout from "@/components/mobile-layout"
import MobileDashboard from "@/components/mobile-dashboard"

export default function DashboardPage() {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024)
    }

    checkMobile()
    window.addEventListener("resize", checkMobile)

    return () => window.removeEventListener("resize", checkMobile)
  }, [])

  if (isMobile) {
    return (
      <MobileLayout>
        <MobileDashboard />
      </MobileLayout>
    )
  }

  // Desktop version - keep existing content
  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600 mt-1">Welcome back! Here's what's happening with your business.</p>
          </div>
          {/* <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
            <Bell className="h-4 w-4 mr-2" />
            View All Notifications
          </Button> */}
        </div>

        {/* Notifications */}
        {/* <div className="space-y-3">
          {notifications.map((notification) => (
            <Alert
              key={notification.id}
              className={`border-l-4 ${
                notification.type === "success"
                  ? "border-l-green-500 bg-green-50"
                  : notification.type === "warning"
                    ? "border-l-orange-500 bg-orange-50"
                    : "border-l-blue-500 bg-blue-50"
              }`}
            >
              <div className="flex items-center justify-between">
                <AlertDescription className="flex-1">{notification.message}</AlertDescription>
                <span className="text-xs text-gray-500">{notification.time}</span>
              </div>
            </Alert>
          ))}
        </div> */}

        {/* Stats Cards */}
        {/* <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className={`text-sm ${stat.change.startsWith("+") ? "text-green-600" : "text-red-600"}`}>
                      {stat.change} from last month
                    </p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div> */}

        {/* Modules Grid */}
        {/* <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Management Modules</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {modules.map((module, index) => (
              <Card
                key={index}
                className="hover:shadow-lg transition-all duration-200 hover:scale-105 cursor-pointer group"
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center space-x-3">
                    <div
                      className={`p-2 rounded-lg ${module.color} text-white group-hover:scale-110 transition-transform`}
                    >
                      <module.icon className="h-5 w-5" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{module.name}</CardTitle>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="mb-3">{module.description}</CardDescription>
                  <div className="flex items-center justify-between">
                    <Badge variant="secondary" className="text-xs">
                      {module.stats}
                    </Badge>
                    <Button size="sm" variant="ghost" className="text-blue-600 hover:text-blue-700">
                      Open →
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div> */}

        {/* Recent Activities */}
        {/* <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="h-5 w-5 mr-2 text-blue-600" />
                Recent Activities
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivities.map((activity) => (
                  <div
                    key={activity.id}
                    className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
                  >
                    <div
                      className={`p-2 rounded-full ${
                        activity.type === "user"
                          ? "bg-blue-100 text-blue-600"
                          : activity.type === "payroll"
                            ? "bg-green-100 text-green-600"
                            : activity.type === "ticket"
                              ? "bg-orange-100 text-orange-600"
                              : activity.type === "project"
                                ? "bg-purple-100 text-purple-600"
                                : "bg-indigo-100 text-indigo-600"
                      }`}
                    >
                      {activity.type === "user" && <Users className="h-4 w-4" />}
                      {activity.type === "payroll" && <DollarSign className="h-4 w-4" />}
                      {activity.type === "ticket" && <Ticket className="h-4 w-4" />}
                      {activity.type === "project" && <FolderKanban className="h-4 w-4" />}
                      {activity.type === "asset" && <Package className="h-4 w-4" />}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                      <p className="text-xs text-gray-500">
                        by {activity.user} • {activity.time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2 text-green-600" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Button className="w-full justify-start bg-transparent" variant="outline">
                  <Users className="h-4 w-4 mr-2" />
                  Add New User
                </Button>
                <Button className="w-full justify-start bg-transparent" variant="outline">
                  <Ticket className="h-4 w-4 mr-2" />
                  Create Support Ticket
                </Button>
                <Button className="w-full justify-start bg-transparent" variant="outline">
                  <FolderKanban className="h-4 w-4 mr-2" />
                  Start New Project
                </Button>
                <Button className="w-full justify-start bg-transparent" variant="outline">
                  <Package className="h-4 w-4 mr-2" />
                  Register Asset
                </Button>
                <Button className="w-full justify-start bg-transparent" variant="outline">
                  <Calendar className="h-4 w-4 mr-2" />
                  Schedule Consultation
                </Button>
              </div>
            </CardContent>
          </Card>
        </div> */}
      </div>
    </DashboardLayout>
  )
}
