"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Sheet, SheetContent } from "@/components/ui/sheet"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Building2,
  Bell,
  Settings,
  LogOut,
  Menu,
  Users,
  DollarSign,
  Ticket,
  UserCheck,
  FolderKanban,
  Package,
  Monitor,
  Calendar,
  Home,
  BarChart3,
  Search,
  X,
} from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"

const navigation = [
  { name: "Dashboard", href: "/dashboard", icon: Home },
  { name: "Users", href: "/dashboard/users", icon: Users },
  { name: "Payroll", href: "/dashboard/payroll", icon: DollarSign },
  { name: "Tickets", href: "/dashboard/tickets", icon: Ticket },
  { name: "Customers", href: "/dashboard/customers", icon: UserCheck },
  { name: "Projects", href: "/dashboard/projects", icon: FolderKanban },
  { name: "Assets", href: "/dashboard/assets", icon: Package },
  { name: "IT", href: "/dashboard/it", icon: Monitor },
  { name: "Consultations", href: "/dashboard/consultations", icon: Calendar },
  { name: "Analytics", href: "/dashboard/analytics", icon: BarChart3 },
]

export default function MobileLayout({ children }) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [notifications] = useState(8)
  const [isOnline, setIsOnline] = useState(true)
  const [installPrompt, setInstallPrompt] = useState(null)
  const [showInstallBanner, setShowInstallBanner] = useState(false)
  const pathname = usePathname()

  useEffect(() => {
    // Check online status
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener("online", handleOnline)
    window.addEventListener("offline", handleOffline)

    // PWA install prompt
    const handleBeforeInstallPrompt = (e) => {
      e.preventDefault()
      setInstallPrompt(e)
      setShowInstallBanner(true)
    }

    window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt)

    // Register service worker
    if ("serviceWorker" in navigator) {
      navigator.serviceWorker
        .register("/sw.js")
        .then((registration) => {
          console.log("SW registered: ", registration)
        })
        .catch((registrationError) => {
          console.log("SW registration failed: ", registrationError)
        })
    }

    return () => {
      window.removeEventListener("online", handleOnline)
      window.removeEventListener("offline", handleOffline)
      window.removeEventListener("beforeinstallprompt", handleBeforeInstallPrompt)
    }
  }, [])

  const handleInstallClick = async () => {
    if (!installPrompt) return

    const result = await installPrompt.prompt()
    console.log("Install prompt result:", result)
    setInstallPrompt(null)
    setShowInstallBanner(false)
  }

  const currentPage = navigation.find((item) => item.href === pathname)

  const MobileSidebar = () => (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-2 rounded-lg">
            <Building2 className="h-5 w-5 text-white" />
          </div>
          <div>
            <h2 className="text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              SaaS Platform
            </h2>
            <p className="text-xs text-gray-500">Mobile Dashboard</p>
          </div>
        </div>
        <Button variant="ghost" size="sm" onClick={() => setSidebarOpen(false)} className="h-8 w-8 p-0">
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
        {navigation.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.name}
              href={item.href}
              onClick={() => setSidebarOpen(false)}
              className={`flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors ${
                isActive
                  ? "bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 border border-blue-200"
                  : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
              }`}
            >
              <item.icon className={`mr-3 h-5 w-5 ${isActive ? "text-blue-600" : "text-gray-400"}`} />
              {item.name}
            </Link>
          )
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-3 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-900">Mobile App</h3>
            <Badge className={`text-xs ${isOnline ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}>
              {isOnline ? "Online" : "Offline"}
            </Badge>
          </div>
          <p className="text-xs text-gray-600 mb-2">Access your dashboard anywhere</p>
          {showInstallBanner && (
            <Button
              size="sm"
              onClick={handleInstallClick}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600"
            >
              Install App
            </Button>
          )}
        </div>
      </div>
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar */}
      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
        <SheetContent side="left" className="p-0 w-80">
          <MobileSidebar />
        </SheetContent>
      </Sheet>

      {/* Mobile header */}
      <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm lg:hidden">
        <Button variant="ghost" size="sm" onClick={() => setSidebarOpen(true)} className="h-8 w-8 p-0">
          <Menu className="h-5 w-5" />
        </Button>

        <div className="flex flex-1 items-center justify-between">
          <div className="flex items-center space-x-2">
            {currentPage && (
              <>
                <currentPage.icon className="h-5 w-5 text-blue-600" />
                <h1 className="text-lg font-semibold text-gray-900">{currentPage.name}</h1>
              </>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" className="relative h-8 w-8 p-0">
              <Search className="h-4 w-4" />
            </Button>

            <Button variant="ghost" size="sm" className="relative h-8 w-8 p-0">
              <Bell className="h-4 w-4" />
              {notifications > 0 && (
                <Badge className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs bg-red-500 hover:bg-red-500">
                  {notifications > 9 ? "9+" : notifications}
                </Badge>
              )}
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <Avatar className="h-7 w-7">
                    <AvatarImage src="/placeholder.svg?height=28&width=28" alt="User" />
                    <AvatarFallback className="text-xs">JD</AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium">John Doe</p>
                    <p className="text-xs text-gray-500"><EMAIL></p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Bell className="mr-2 h-4 w-4" />
                  Notifications
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-red-600">
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Desktop layout (hidden on mobile) */}
      <div className="hidden lg:block">{/* Your existing desktop layout would go here */}</div>

      {/* Main content */}
      <main className="py-4 px-4 lg:py-8 lg:px-8">{children}</main>

      {/* Mobile bottom navigation */}
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 lg:hidden">
        <div className="grid grid-cols-5 gap-1 p-2">
          {navigation.slice(0, 4).map((item) => {
            const isActive = pathname === item.href
            return (
              <Link
                key={item.name}
                href={item.href}
                className={`flex flex-col items-center justify-center py-2 px-1 rounded-lg transition-colors ${
                  isActive ? "bg-blue-50 text-blue-600" : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                }`}
              >
                <item.icon className="h-5 w-5 mb-1" />
                <span className="text-xs font-medium truncate">{item.name}</span>
              </Link>
            )
          })}
          <Button
            variant="ghost"
            onClick={() => setSidebarOpen(true)}
            className="flex flex-col items-center justify-center py-2 px-1 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-50"
          >
            <Menu className="h-5 w-5 mb-1" />
            <span className="text-xs font-medium">More</span>
          </Button>
        </div>
      </div>

      {/* Offline indicator */}
      {!isOnline && (
        <div className="fixed top-16 left-4 right-4 z-50 bg-orange-100 border border-orange-200 rounded-lg p-3 lg:hidden">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
            <p className="text-sm text-orange-800">You're offline. Some features may be limited.</p>
          </div>
        </div>
      )}

      {/* Install app banner */}
      {showInstallBanner && (
        <div className="fixed bottom-20 left-4 right-4 z-50 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-4 text-white lg:hidden">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h3 className="text-sm font-medium">Install SaaS Platform</h3>
              <p className="text-xs opacity-90">Get the full app experience</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button size="sm" variant="ghost" onClick={handleInstallClick} className="text-white hover:bg-white/20">
                Install
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setShowInstallBanner(false)}
                className="text-white hover:bg-white/20 h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
