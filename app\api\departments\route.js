import { NextResponse } from "next/server"

const departments = [
  {
    id: "1",
    name: "Information Technology",
    code: "IT",
    parent_id: null,
    manager_id: "1",
    description: "Handles all technology-related operations",
    is_active: true,
    created_at: "2023-01-01T00:00:00Z",
    updated_at: "2023-01-01T00:00:00Z",
  },
  {
    id: "2",
    name: "Human Resources",
    code: "HR",
    parent_id: null,
    manager_id: "2",
    description: "Manages employee relations and policies",
    is_active: true,
    created_at: "2023-01-01T00:00:00Z",
    updated_at: "2023-01-01T00:00:00Z",
  },
  {
    id: "3",
    name: "Finance",
    code: "FIN",
    parent_id: null,
    manager_id: "3",
    description: "Handles financial operations and accounting",
    is_active: true,
    created_at: "2023-01-01T00:00:00Z",
    updated_at: "2023-01-01T00:00:00Z",
  },
  {
    id: "4",
    name: "Sales",
    code: "SALES",
    parent_id: null,
    manager_id: "4",
    description: "Manages sales operations and customer relations",
    is_active: true,
    created_at: "2023-01-01T00:00:00Z",
    updated_at: "2023-01-01T00:00:00Z",
  },
  {
    id: "5",
    name: "Software Development",
    code: "DEV",
    parent_id: "1",
    manager_id: "5",
    description: "Software development and engineering",
    is_active: true,
    created_at: "2023-01-01T00:00:00Z",
    updated_at: "2023-01-01T00:00:00Z",
  },
]

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url)
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "10")
    const search = searchParams.get("search") || ""
    const parent_id = searchParams.get("parent_id")

    let filteredDepartments = departments

    if (search) {
      filteredDepartments = departments.filter(
        (dept) =>
          dept.name.toLowerCase().includes(search.toLowerCase()) ||
          dept.code.toLowerCase().includes(search.toLowerCase()),
      )
    }

    if (parent_id) {
      filteredDepartments = filteredDepartments.filter((dept) => dept.parent_id === parent_id)
    }

    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedDepartments = filteredDepartments.slice(startIndex, endIndex)

    return NextResponse.json({
      data: paginatedDepartments,
      pagination: {
        page,
        limit,
        total: filteredDepartments.length,
        totalPages: Math.ceil(filteredDepartments.length / limit),
      },
    })
  } catch (error) {
    return NextResponse.json({ error: "Failed to fetch departments" }, { status: 500 })
  }
}

export async function POST(request) {
  try {
    const body = await request.json()
    const newDepartment = {
      id: Date.now().toString(),
      ...body,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    departments.push(newDepartment)

    return NextResponse.json({ data: newDepartment }, { status: 201 })
  } catch (error) {
    return NextResponse.json({ error: "Failed to create department" }, { status: 500 })
  }
}
