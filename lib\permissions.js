// Role-based permissions configuration for workflow phases
export const ROLES = {
  ADMIN: "admin",
  PROJECT_MANAGER: "project_manager",
  PORTFOLIO_MANAGER: "portfolio_manager",
  BUSINESS_ANALYST: "business_analyst",
  TEAM_LEAD: "team_lead",
  DEVELOPER: "developer",
  STAKEH<PERSON>DER: "stakeholder",
  VIEWER: "viewer",
}

export const PERMISSIONS = {
  // CREATE Phase Permissions
  CREATE_PROPOSE_IDEA: "create_propose_idea",
  CREATE_REVIEW_IDEA: "create_review_idea",
  CREATE_APPROVE_REQUEST: "create_approve_request",
  CREATE_EDIT_PROPOSAL: "create_edit_proposal",

  // SELECT Phase Permissions
  SELECT_REQUEST_REVIEW: "select_request_review",
  SELECT_PORTFOLIO_SELECTION: "select_portfolio_selection",
  SELECT_COMPLETE_SELECTION: "select_complete_selection",
  SELECT_REJECT_PROJECT: "select_reject_project",

  // PLAN Phase Permissions
  PLAN_CREATE_BUSINESS_CASE: "plan_create_business_case",
  PLAN_REVIEW_BUSINESS_CASE: "plan_review_business_case",
  PLAN_APPROVE_BUSINESS_CASE: "plan_approve_business_case",
  PLAN_DENY_BUSINESS_CASE: "plan_deny_business_case",
  PLAN_EDIT_BUSINESS_CASE: "plan_edit_business_case",

  // MANAGE Phase Permissions
  MANAGE_START_PROJECT: "manage_start_project",
  MANAGE_SUSPEND_PROJECT: "manage_suspend_project",
  MANAGE_COMPLETE_PROJECT: "manage_complete_project",
  MANAGE_POST_IMPLEMENTATION: "manage_post_implementation",
  MANAGE_ASSIGN_TEAM: "manage_assign_team",

  // General Permissions
  VIEW_PROJECT: "view_project",
  EDIT_PROJECT: "edit_project",
  DELETE_PROJECT: "delete_project",
  VIEW_FINANCIALS: "view_financials",
  EDIT_FINANCIALS: "edit_financials",
  MANAGE_TEAM: "manage_team",
  VIEW_REPORTS: "view_reports",
}

// Role-Permission mapping
export const ROLE_PERMISSIONS = {
  [ROLES.ADMIN]: [
    // All permissions
    ...Object.values(PERMISSIONS),
  ],

  [ROLES.PROJECT_MANAGER]: [
    PERMISSIONS.CREATE_PROPOSE_IDEA,
    PERMISSIONS.CREATE_EDIT_PROPOSAL,
    PERMISSIONS.SELECT_REQUEST_REVIEW,
    PERMISSIONS.PLAN_CREATE_BUSINESS_CASE,
    PERMISSIONS.PLAN_EDIT_BUSINESS_CASE,
    PERMISSIONS.MANAGE_START_PROJECT,
    PERMISSIONS.MANAGE_SUSPEND_PROJECT,
    PERMISSIONS.MANAGE_COMPLETE_PROJECT,
    PERMISSIONS.MANAGE_ASSIGN_TEAM,
    PERMISSIONS.VIEW_PROJECT,
    PERMISSIONS.EDIT_PROJECT,
    PERMISSIONS.VIEW_FINANCIALS,
    PERMISSIONS.MANAGE_TEAM,
    PERMISSIONS.VIEW_REPORTS,
  ],

  [ROLES.PORTFOLIO_MANAGER]: [
    PERMISSIONS.CREATE_REVIEW_IDEA,
    PERMISSIONS.CREATE_APPROVE_REQUEST,
    PERMISSIONS.SELECT_PORTFOLIO_SELECTION,
    PERMISSIONS.SELECT_COMPLETE_SELECTION,
    PERMISSIONS.SELECT_REJECT_PROJECT,
    PERMISSIONS.PLAN_REVIEW_BUSINESS_CASE,
    PERMISSIONS.PLAN_APPROVE_BUSINESS_CASE,
    PERMISSIONS.PLAN_DENY_BUSINESS_CASE,
    PERMISSIONS.VIEW_PROJECT,
    PERMISSIONS.VIEW_FINANCIALS,
    PERMISSIONS.VIEW_REPORTS,
  ],

  [ROLES.BUSINESS_ANALYST]: [
    PERMISSIONS.CREATE_PROPOSE_IDEA,
    PERMISSIONS.CREATE_EDIT_PROPOSAL,
    PERMISSIONS.PLAN_CREATE_BUSINESS_CASE,
    PERMISSIONS.PLAN_EDIT_BUSINESS_CASE,
    PERMISSIONS.VIEW_PROJECT,
    PERMISSIONS.VIEW_FINANCIALS,
    PERMISSIONS.VIEW_REPORTS,
  ],

  [ROLES.TEAM_LEAD]: [
    PERMISSIONS.MANAGE_START_PROJECT,
    PERMISSIONS.MANAGE_ASSIGN_TEAM,
    PERMISSIONS.VIEW_PROJECT,
    PERMISSIONS.EDIT_PROJECT,
    PERMISSIONS.MANAGE_TEAM,
    PERMISSIONS.VIEW_REPORTS,
  ],

  [ROLES.DEVELOPER]: [PERMISSIONS.VIEW_PROJECT, PERMISSIONS.VIEW_REPORTS],

  [ROLES.STAKEHOLDER]: [PERMISSIONS.CREATE_PROPOSE_IDEA, PERMISSIONS.VIEW_PROJECT, PERMISSIONS.VIEW_REPORTS],

  [ROLES.VIEWER]: [PERMISSIONS.VIEW_PROJECT],
}

// Phase-specific permission requirements
export const PHASE_PERMISSIONS = {
  CREATE: {
    PROPOSE_IDEA: [PERMISSIONS.CREATE_PROPOSE_IDEA],
    INITIAL_REVIEW: [PERMISSIONS.CREATE_REVIEW_IDEA],
    COMPLETED_REQUEST: [PERMISSIONS.CREATE_APPROVE_REQUEST],
  },
  SELECT: {
    REQUEST_REVIEW: [PERMISSIONS.SELECT_REQUEST_REVIEW],
    PORTFOLIO_SELECTION: [PERMISSIONS.SELECT_PORTFOLIO_SELECTION],
    COMPLETED_SELECTION: [PERMISSIONS.SELECT_COMPLETE_SELECTION],
  },
  PLAN: {
    FULL_BUSINESS_CASE: [PERMISSIONS.PLAN_CREATE_BUSINESS_CASE],
    REVIEW_AND_APPROVAL: [PERMISSIONS.PLAN_REVIEW_BUSINESS_CASE],
    APPROVED: [PERMISSIONS.PLAN_APPROVE_BUSINESS_CASE],
    DENIED: [PERMISSIONS.PLAN_DENY_BUSINESS_CASE],
  },
  MANAGE: {
    DELIVER_PROJECT: [PERMISSIONS.MANAGE_START_PROJECT],
    PROJECT_SUSPENDED: [PERMISSIONS.MANAGE_SUSPEND_PROJECT],
    POST_PROJECT_IMPLEMENTATION: [PERMISSIONS.MANAGE_POST_IMPLEMENTATION],
  },
}

// Helper functions
export const hasPermission = (userRole, permission) => {
  if (!userRole || !permission) return false
  return ROLE_PERMISSIONS[userRole]?.includes(permission) || false
}

export const hasAnyPermission = (userRole, permissions) => {
  if (!userRole || !permissions || !Array.isArray(permissions)) return false
  return permissions.some((permission) => hasPermission(userRole, permission))
}

export const canPerformAction = (userRole, phase, status) => {
  if (!userRole || !phase || !status) return false
  const requiredPermissions = PHASE_PERMISSIONS[phase]?.[status]
  if (!requiredPermissions) return false
  return hasAnyPermission(userRole, requiredPermissions)
}

export const getAvailableActions = (userRole, phase, status) => {
  const actions = []

  // Check phase transition permissions
  if (hasPermission(userRole, PERMISSIONS.EDIT_PROJECT)) {
    actions.push("edit_project")
  }

  if (hasPermission(userRole, PERMISSIONS.VIEW_FINANCIALS)) {
    actions.push("view_financials")
  }

  if (hasPermission(userRole, PERMISSIONS.MANAGE_TEAM)) {
    actions.push("manage_team")
  }

  // Phase-specific actions
  switch (phase) {
    case "CREATE":
      if (hasPermission(userRole, PERMISSIONS.CREATE_PROPOSE_IDEA)) {
        actions.push("propose_idea")
      }
      if (hasPermission(userRole, PERMISSIONS.CREATE_REVIEW_IDEA)) {
        actions.push("review_idea")
      }
      if (hasPermission(userRole, PERMISSIONS.CREATE_APPROVE_REQUEST)) {
        actions.push("approve_request")
      }
      break

    case "SELECT":
      if (hasPermission(userRole, PERMISSIONS.SELECT_PORTFOLIO_SELECTION)) {
        actions.push("portfolio_selection")
      }
      if (hasPermission(userRole, PERMISSIONS.SELECT_COMPLETE_SELECTION)) {
        actions.push("complete_selection")
      }
      break

    case "PLAN":
      if (hasPermission(userRole, PERMISSIONS.PLAN_APPROVE_BUSINESS_CASE)) {
        actions.push("approve_business_case")
      }
      if (hasPermission(userRole, PERMISSIONS.PLAN_DENY_BUSINESS_CASE)) {
        actions.push("deny_business_case")
      }
      break

    case "MANAGE":
      if (hasPermission(userRole, PERMISSIONS.MANAGE_START_PROJECT)) {
        actions.push("start_project")
      }
      if (hasPermission(userRole, PERMISSIONS.MANAGE_SUSPEND_PROJECT)) {
        actions.push("suspend_project")
      }
      break
  }

  return actions
}

export const getRoleDisplayName = (role) => {
  const roleNames = {
    [ROLES.ADMIN]: "Administrator",
    [ROLES.PROJECT_MANAGER]: "Project Manager",
    [ROLES.PORTFOLIO_MANAGER]: "Portfolio Manager",
    [ROLES.BUSINESS_ANALYST]: "Business Analyst",
    [ROLES.TEAM_LEAD]: "Team Lead",
    [ROLES.DEVELOPER]: "Developer",
    [ROLES.STAKEHOLDER]: "Stakeholder",
    [ROLES.VIEWER]: "Viewer",
  }
  return roleNames[role] || role
}
