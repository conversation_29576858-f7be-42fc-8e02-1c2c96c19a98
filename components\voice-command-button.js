"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Mi<PERSON>, MicOff, Zap, Volume2 } from "lucide-react"
import { useVoiceCommands } from "@/hooks/use-voice-commands"

export default function VoiceCommandButton({ className = "" }) {
  const { isListening, isProcessing, lastCommand, isSupported, startVoiceCommand, stopListening, showHelp } =
    useVoiceCommands()

  const [showPopover, setShowPopover] = useState(false)

  if (!isSupported) {
    return null
  }

  const handleClick = () => {
    if (isListening) {
      stopListening()
    } else {
      startVoiceCommand()
      setShowPopover(true)
      setTimeout(() => setShowPopover(false), 3000)
    }
  }

  return (
    <Popover open={showPopover} onOpenChange={setShowPopover}>
      <PopoverTrigger asChild>
        <Button
          onClick={handleClick}
          variant={isListening ? "destructive" : "outline"}
          size="sm"
          className={`relative ${className}`}
          disabled={isProcessing}
        >
          {isListening ? (
            <>
              <MicOff className="h-4 w-4 mr-2" />
              Stop
            </>
          ) : (
            <>
              <Mic className="h-4 w-4 mr-2" />
              Voice
            </>
          )}

          {isProcessing && (
            <Badge className="absolute -top-1 -right-1 h-4 w-4 rounded-full p-0 bg-yellow-500 animate-pulse">
              <Zap className="h-2 w-2" />
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="end">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">Voice Commands</h4>
            <Badge className={isListening ? "bg-red-100 text-red-800" : "bg-gray-100 text-gray-800"}>
              {isListening ? "Listening..." : "Ready"}
            </Badge>
          </div>

          {lastCommand && (
            <div className="p-2 bg-blue-50 rounded text-sm">
              <span className="font-medium">Last: </span>"{lastCommand}"
            </div>
          )}

          <div className="text-sm text-gray-600">
            <p className="mb-2">Try saying:</p>
            <ul className="space-y-1">
              <li>• "Go to dashboard"</li>
              <li>• "Open users"</li>
              <li>• "Create new ticket"</li>
              <li>• "Search for John"</li>
            </ul>
          </div>

          <div className="flex space-x-2">
            <Button size="sm" variant="outline" onClick={showHelp} className="flex-1 bg-transparent">
              <Volume2 className="h-3 w-3 mr-1" />
              Help
            </Button>
            {isListening && (
              <Button size="sm" variant="outline" onClick={stopListening} className="flex-1 bg-transparent">
                <MicOff className="h-3 w-3 mr-1" />
                Stop
              </Button>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
