import { NextResponse } from "next/server"

// Mock data - replace with actual database operations
const countries = [
  { id: "1", name: "United States", code: "US", currency_code: "USD", phone_code: "+1", is_active: true },
  { id: "2", name: "India", code: "IN", currency_code: "INR", phone_code: "+91", is_active: true },
  { id: "3", name: "United Kingdom", code: "GB", currency_code: "GBP", phone_code: "+44", is_active: true },
  { id: "4", name: "Canada", code: "CA", currency_code: "CAD", phone_code: "+1", is_active: true },
  { id: "5", name: "Australia", code: "AU", currency_code: "AUD", phone_code: "+61", is_active: true },
]

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url)
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "10")
    const search = searchParams.get("search") || ""

    let filteredCountries = countries

    if (search) {
      filteredCountries = countries.filter(
        (country) =>
          country.name.toLowerCase().includes(search.toLowerCase()) ||
          country.code.toLowerCase().includes(search.toLowerCase()),
      )
    }

    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedCountries = filteredCountries.slice(startIndex, endIndex)

    return NextResponse.json({
      data: paginatedCountries,
      pagination: {
        page,
        limit,
        total: filteredCountries.length,
        totalPages: Math.ceil(filteredCountries.length / limit),
      },
    })
  } catch (error) {
    return NextResponse.json({ error: "Failed to fetch countries" }, { status: 500 })
  }
}

export async function POST(request) {
  try {
    const body = await request.json()
    const newCountry = {
      id: Date.now().toString(),
      ...body,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    countries.push(newCountry)

    return NextResponse.json({ data: newCountry }, { status: 201 })
  } catch (error) {
    return NextResponse.json({ error: "Failed to create country" }, { status: 500 })
  }
}
