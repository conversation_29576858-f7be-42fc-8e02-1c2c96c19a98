"use client"

import { useState, useEffect, useContext, createContext } from "react"
import { hasPermission, hasAnyPermission, canPerformAction, getAvailableActions } from "@/lib/permissions"

const PermissionsContext = createContext()

export function PermissionsProvider({ children, userRole, userId }) {
  const [permissions, setPermissions] = useState([])
  const [customPermissions, setCustomPermissions] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Fetch user permissions from API
    const fetchPermissions = async () => {
      try {
        setLoading(true)
        // Mock API call - replace with actual API
        const response = await fetch(`/api/users/${userId}/permissions`)
        if (response.ok) {
          const data = await response.json()
          setPermissions(data.permissions || [])
          setCustomPermissions(data.customPermissions || [])
        }
      } catch (error) {
        console.error("Failed to fetch permissions:", error)
      } finally {
        setLoading(false)
      }
    }

    if (userId) {
      fetchPermissions()
    } else {
      setLoading(false)
    }
  }, [userId])

  const checkPermission = (permission) => {
    return hasPermission(userRole, permission) || customPermissions.includes(permission)
  }

  const checkAnyPermission = (permissionList) => {
    return hasAnyPermission(userRole, permissionList) || permissionList.some((p) => customPermissions.includes(p))
  }

  const checkPhaseAction = (phase, status) => {
    return canPerformAction(userRole, phase, status)
  }

  const getActions = (phase, status) => {
    return getAvailableActions(userRole, phase, status)
  }

  const updateCustomPermissions = async (newPermissions) => {
    try {
      const response = await fetch(`/api/users/${userId}/permissions`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ customPermissions: newPermissions }),
      })

      if (response.ok) {
        setCustomPermissions(newPermissions)
        return true
      }
      return false
    } catch (error) {
      console.error("Failed to update permissions:", error)
      return false
    }
  }

  const value = {
    userRole,
    permissions,
    customPermissions,
    loading,
    hasPermission: checkPermission,
    hasAnyPermission: checkAnyPermission,
    canPerformAction: checkPhaseAction,
    getAvailableActions: getActions,
    updateCustomPermissions,
  }

  return <PermissionsContext.Provider value={value}>{children}</PermissionsContext.Provider>
}

export function usePermissions() {
  const context = useContext(PermissionsContext)
  if (!context) {
    throw new Error("usePermissions must be used within a PermissionsProvider")
  }
  return context
}

// Hook for checking specific permissions
export function useHasPermission(permission) {
  const { hasPermission } = usePermissions()
  return hasPermission(permission)
}

// Hook for checking multiple permissions
export function useHasAnyPermission(permissions) {
  const { hasAnyPermission } = usePermissions()
  return hasAnyPermission(permissions)
}

// Hook for phase-specific actions
export function usePhaseActions(phase, status) {
  const { canPerformAction, getAvailableActions } = usePermissions()
  return {
    canPerform: canPerformAction(phase, status),
    availableActions: getAvailableActions(phase, status),
  }
}
