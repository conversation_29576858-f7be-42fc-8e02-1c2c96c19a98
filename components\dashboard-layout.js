import React from "react"
import { BarChart, Building2, FileIcon as FileTemplate, Home, ListChecks, Settings, User2 } from "lucide-react"

import { MainNav } from "@/components/main-nav"
import { Sidebar } from "@/components/sidebar"

const sidebarNavItems = [
  {
    title: "Home",
    href: "/dashboard",
    icon: Home,
  },
  {
    title: "Assets",
    href: "/dashboard/assets",
    icon: Building2,
    items: [
      {
        title: "Overview",
        href: "/dashboard/assets",
        icon: Bar<PERSON>hart,
      },
      {
        title: "Transfer Templates",
        href: "/dashboard/assets/templates",
        icon: FileTemplate,
      },
    ],
  },
  {
    title: "Tasks",
    href: "/dashboard/tasks",
    icon: ListChecks,
  },
  {
    title: "Profile",
    href: "/dashboard/profile",
    icon: User2,
  },
  {
    title: "Settings",
    href: "/dashboard/settings",
    icon: Settings,
  },
]

export const DashboardLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="flex h-screen">
      <Sidebar className="w-64 border-r flex-shrink-0">
        <MainNav className="flex flex-col space-y-6" items={sidebarNavItems} />
      </Sidebar>
      <div className="flex-1 p-6">{children}</div>
    </div>
  )
}
