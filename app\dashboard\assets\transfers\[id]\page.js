"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  ArrowRightLeft,
  CheckCircle,
  XCircle,
  Clock,
  Package,
  FileText,
  MessageSquare,
  Download,
  Edit,
  AlertTriangle,
  History,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import Link from "next/link"

export default function TransferDetailsPage({ params }) {
  const [isApprovalDialogOpen, setIsApprovalDialogOpen] = useState(false)
  const [approvalAction, setApprovalAction] = useState("")
  const [approvalComment, setApprovalComment] = useState("")

  // Mock transfer data - in real app, fetch based on params.id
  const transfer = {
    id: "TRF-001",
    assetId: "AST-001",
    assetName: "MacBook Pro 16-inch",
    assetImage: "/placeholder.svg?height=200&width=300&text=MacBook+Pro",
    transferType: "user",
    fromUser: "John Doe",
    toUser: "Sarah Wilson",
    fromLocation: "Office Floor 2",
    toLocation: "Office Floor 3",
    status: "pending_approval",
    priority: "normal",
    requestedBy: "John Doe",
    requestedDate: "2024-01-15",
    approver: "IT Manager",
    reason: "Employee relocation to new department",
    estimatedCompletion: "2024-01-20",
    comments: "Asset in excellent condition, includes original packaging",
    approvalSteps: [
      {
        step: 1,
        name: "Direct Manager",
        status: "approved",
        approver: "Mike Johnson",
        date: "2024-01-16",
        comment: "Approved for department transfer. Employee has been performing well.",
      },
      {
        step: 2,
        name: "IT Manager",
        status: "pending",
        approver: "Lisa Chen",
        date: null,
        comment: null,
      },
      {
        step: 3,
        name: "Asset Manager",
        status: "pending",
        approver: "David Brown",
        date: null,
        comment: null,
      },
    ],
    documents: [
      { name: "Transfer Request Form.pdf", size: "245 KB", uploadedBy: "John Doe", uploadedDate: "2024-01-15" },
      { name: "Asset Condition Report.pdf", size: "1.2 MB", uploadedBy: "IT Support", uploadedDate: "2024-01-16" },
    ],
    timeline: [
      {
        date: "2024-01-15 10:30 AM",
        action: "Transfer Request Created",
        user: "John Doe",
        description: "Initial transfer request submitted for MacBook Pro 16-inch",
        type: "created",
      },
      {
        date: "2024-01-15 10:35 AM",
        action: "Notification Sent",
        user: "System",
        description: "Approval request sent to Direct Manager (Mike Johnson)",
        type: "notification",
      },
      {
        date: "2024-01-16 2:15 PM",
        action: "Step 1 Approved",
        user: "Mike Johnson",
        description: "Direct Manager approved the transfer request",
        type: "approved",
      },
      {
        date: "2024-01-16 2:16 PM",
        action: "Notification Sent",
        user: "System",
        description: "Approval request sent to IT Manager (Lisa Chen)",
        type: "notification",
      },
    ],
  }

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case "pending_approval":
        return "bg-yellow-100 text-yellow-800"
      case "approved":
        return "bg-green-100 text-green-800"
      case "in_progress":
        return "bg-blue-100 text-blue-800"
      case "completed":
        return "bg-green-100 text-green-800"
      case "rejected":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority.toLowerCase()) {
      case "high":
        return "bg-red-100 text-red-800"
      case "normal":
        return "bg-blue-100 text-blue-800"
      case "low":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStepStatusIcon = (status) => {
    switch (status) {
      case "approved":
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case "rejected":
        return <XCircle className="h-5 w-5 text-red-600" />
      case "pending":
        return <Clock className="h-5 w-5 text-orange-600" />
      default:
        return <Clock className="h-5 w-5 text-gray-400" />
    }
  }

  const getTimelineIcon = (type) => {
    switch (type) {
      case "created":
        return <ArrowRightLeft className="h-4 w-4 text-blue-600" />
      case "approved":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "rejected":
        return <XCircle className="h-4 w-4 text-red-600" />
      case "notification":
        return <MessageSquare className="h-4 w-4 text-purple-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const handleApproval = (action) => {
    setApprovalAction(action)
    setIsApprovalDialogOpen(true)
  }

  const submitApproval = () => {
    // Handle approval/rejection logic here
    console.log(`${approvalAction} transfer with comment:`, approvalComment)
    setIsApprovalDialogOpen(false)
    setApprovalComment("")
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/assets/transfers">
              <Button variant="outline" size="sm">
                ← Back to Transfers
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Transfer Details</h1>
              <p className="text-gray-600 mt-1">
                {transfer.id} • {transfer.assetName}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Badge className={getStatusColor(transfer.status)}>{transfer.status.replace("_", " ").toUpperCase()}</Badge>
            <Badge className={getPriorityColor(transfer.priority)} variant="outline">
              {transfer.priority.toUpperCase()} PRIORITY
            </Badge>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Main Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Asset Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Package className="h-5 w-5 mr-2 text-indigo-600" />
                  Asset Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-start space-x-6">
                  <img
                    src={transfer.assetImage || "/placeholder.svg"}
                    alt={transfer.assetName}
                    className="w-32 h-24 object-cover rounded-lg border"
                  />
                  <div className="flex-1 space-y-3">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{transfer.assetName}</h3>
                      <p className="text-sm text-gray-600">Asset ID: {transfer.assetId}</p>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Current User:</span>
                        <p className="text-gray-600">{transfer.fromUser}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Current Location:</span>
                        <p className="text-gray-600">{transfer.fromLocation}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">New User:</span>
                        <p className="text-gray-600">{transfer.toUser}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">New Location:</span>
                        <p className="text-gray-600">{transfer.toLocation}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Transfer Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <ArrowRightLeft className="h-5 w-5 mr-2 text-indigo-600" />
                  Transfer Details
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Requested By</Label>
                      <p className="text-gray-900">{transfer.requestedBy}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Request Date</Label>
                      <p className="text-gray-900">{transfer.requestedDate}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Transfer Type</Label>
                      <p className="text-gray-900 capitalize">{transfer.transferType} Transfer</p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Priority</Label>
                      <Badge className={getPriorityColor(transfer.priority)} variant="outline">
                        {transfer.priority.toUpperCase()}
                      </Badge>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Expected Completion</Label>
                      <p className="text-gray-900">{transfer.estimatedCompletion}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Current Approver</Label>
                      <p className="text-gray-900">{transfer.approver}</p>
                    </div>
                  </div>
                </div>
                <div className="mt-6">
                  <Label className="text-sm font-medium text-gray-700">Transfer Reason</Label>
                  <p className="text-gray-900 mt-1">{transfer.reason}</p>
                </div>
                {transfer.comments && (
                  <div className="mt-4">
                    <Label className="text-sm font-medium text-gray-700">Additional Comments</Label>
                    <p className="text-gray-900 mt-1">{transfer.comments}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Approval Workflow */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CheckCircle className="h-5 w-5 mr-2 text-indigo-600" />
                  Approval Workflow
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {transfer.approvalSteps.map((step, index) => (
                    <div key={step.step} className="flex items-start space-x-4">
                      <div className="flex-shrink-0">{getStepStatusIcon(step.status)}</div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-sm font-medium text-gray-900">{step.name}</h4>
                            <p className="text-sm text-gray-600">{step.approver}</p>
                          </div>
                          <div className="text-right">
                            <Badge
                              className={
                                step.status === "approved"
                                  ? "bg-green-100 text-green-800"
                                  : step.status === "rejected"
                                    ? "bg-red-100 text-red-800"
                                    : "bg-yellow-100 text-yellow-800"
                              }
                              variant="outline"
                            >
                              {step.status.toUpperCase()}
                            </Badge>
                            {step.date && <p className="text-xs text-gray-500 mt-1">{step.date}</p>}
                          </div>
                        </div>
                        {step.comment && (
                          <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                            <p className="text-sm text-gray-700">{step.comment}</p>
                          </div>
                        )}
                      </div>
                      {index < transfer.approvalSteps.length - 1 && (
                        <div className="absolute left-2 mt-8 w-px h-8 bg-gray-200"></div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Tabs for Additional Information */}
            <Tabs defaultValue="timeline" className="space-y-4">
              <TabsList>
                <TabsTrigger value="timeline">Timeline</TabsTrigger>
                <TabsTrigger value="documents">Documents</TabsTrigger>
                <TabsTrigger value="comments">Comments</TabsTrigger>
              </TabsList>

              <TabsContent value="timeline">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <History className="h-5 w-5 mr-2 text-indigo-600" />
                      Transfer Timeline
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {transfer.timeline.map((event, index) => (
                        <div key={index} className="flex items-start space-x-3">
                          <div className="flex-shrink-0 mt-1">{getTimelineIcon(event.type)}</div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <h4 className="text-sm font-medium text-gray-900">{event.action}</h4>
                              <span className="text-xs text-gray-500">{event.date}</span>
                            </div>
                            <p className="text-sm text-gray-600">{event.description}</p>
                            <p className="text-xs text-gray-500 mt-1">by {event.user}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="documents">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <FileText className="h-5 w-5 mr-2 text-indigo-600" />
                      Transfer Documents
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {transfer.documents.map((doc, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            <FileText className="h-5 w-5 text-gray-400" />
                            <div>
                              <p className="text-sm font-medium text-gray-900">{doc.name}</p>
                              <p className="text-xs text-gray-500">
                                {doc.size} • Uploaded by {doc.uploadedBy} on {doc.uploadedDate}
                              </p>
                            </div>
                          </div>
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-2" />
                            Download
                          </Button>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="comments">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <MessageSquare className="h-5 w-5 mr-2 text-indigo-600" />
                      Comments & Notes
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-900">John Doe</span>
                          <span className="text-xs text-gray-500">2024-01-15 10:30 AM</span>
                        </div>
                        <p className="text-sm text-gray-700">
                          Initiating transfer request for department relocation. Asset is in excellent condition.
                        </p>
                      </div>
                      <div className="p-4 bg-green-50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-900">Mike Johnson</span>
                          <span className="text-xs text-gray-500">2024-01-16 2:15 PM</span>
                        </div>
                        <p className="text-sm text-gray-700">
                          Approved for department transfer. Employee has been performing well and the move is justified.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Right Column - Actions & Status */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {transfer.status === "pending_approval" && (
                  <>
                    <Button
                      className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"
                      onClick={() => handleApproval("approve")}
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Approve Transfer
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full text-red-600 border-red-200 hover:bg-red-50 bg-transparent"
                      onClick={() => handleApproval("reject")}
                    >
                      <XCircle className="h-4 w-4 mr-2" />
                      Reject Transfer
                    </Button>
                  </>
                )}
                <Button variant="outline" className="w-full bg-transparent">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Transfer
                </Button>
                <Button variant="outline" className="w-full bg-transparent">
                  <Download className="h-4 w-4 mr-2" />
                  Export Details
                </Button>
              </CardContent>
            </Card>

            {/* Transfer Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Transfer Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Status:</span>
                  <Badge className={getStatusColor(transfer.status)}>
                    {transfer.status.replace("_", " ").toUpperCase()}
                  </Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Priority:</span>
                  <Badge className={getPriorityColor(transfer.priority)} variant="outline">
                    {transfer.priority.toUpperCase()}
                  </Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Progress:</span>
                  <span className="font-medium">1 of 3 approvals</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Days remaining:</span>
                  <span className="font-medium">4 days</span>
                </div>
              </CardContent>
            </Card>

            {/* Risk Assessment */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <AlertTriangle className="h-5 w-5 mr-2 text-orange-600" />
                  Risk Assessment
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Risk Level:</span>
                    <Badge className="bg-green-100 text-green-800" variant="outline">
                      LOW
                    </Badge>
                  </div>
                  <div className="text-sm text-gray-600">
                    <p>• Asset value within normal range</p>
                    <p>• Standard approval workflow</p>
                    <p>• No compliance issues detected</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Approval Dialog */}
        <Dialog open={isApprovalDialogOpen} onOpenChange={setIsApprovalDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{approvalAction === "approve" ? "Approve Transfer" : "Reject Transfer"}</DialogTitle>
              <DialogDescription>
                {approvalAction === "approve"
                  ? "Please provide any comments for approving this transfer request."
                  : "Please provide a reason for rejecting this transfer request."}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="approval-comment">
                  {approvalAction === "approve" ? "Approval Comments" : "Rejection Reason"}
                </Label>
                <Textarea
                  id="approval-comment"
                  placeholder={
                    approvalAction === "approve"
                      ? "Add any comments about this approval..."
                      : "Explain why this transfer is being rejected..."
                  }
                  value={approvalComment}
                  onChange={(e) => setApprovalComment(e.target.value)}
                  className="mt-2"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsApprovalDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={submitApproval}
                className={
                  approvalAction === "approve"
                    ? "bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"
                    : "bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800"
                }
              >
                {approvalAction === "approve" ? (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Approve Transfer
                  </>
                ) : (
                  <>
                    <XCircle className="h-4 w-4 mr-2" />
                    Reject Transfer
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}
