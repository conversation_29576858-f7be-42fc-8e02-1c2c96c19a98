"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  FolderKanban,
  Users,
  Clock,
  CheckCircle,
  Search,
  MoreHorizontal,
  Plus,
  Calendar,
  BarChart3,
  Timer,
  Play,
  Pause,
  Square,
  Target,
  TrendingUp,
  Activity,
  Settings,
  Download,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"

export default function ProjectManagementPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [selectedPriority, setSelectedPriority] = useState("all")
  const [isCreateProjectOpen, setIsCreateProjectOpen] = useState(false)
  const [activeTimer, setActiveTimer] = useState(null)
  const [timerSeconds, setTimerSeconds] = useState(0)

  const projectStats = [
    {
      title: "Active Projects",
      value: "24",
      change: "+3 this month",
      icon: FolderKanban,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Team Members",
      value: "48",
      change: "+5 new members",
      icon: Users,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Hours Tracked",
      value: "1,247",
      change: "+12% this week",
      icon: Clock,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      title: "Completed Tasks",
      value: "342",
      change: "89% completion rate",
      icon: CheckCircle,
      color: "text-emerald-600",
      bgColor: "bg-emerald-100",
    },
  ]

  const projects = [
    {
      id: "PRJ001",
      name: "E-commerce Platform Redesign",
      description: "Complete overhaul of the customer-facing e-commerce platform with modern UI/UX",
      status: "In Progress",
      priority: "High",
      progress: 68,
      startDate: "2024-01-15",
      endDate: "2024-03-30",
      budget: 85000,
      spent: 42500,
      teamSize: 8,
      tasksTotal: 45,
      tasksCompleted: 31,
      hoursLogged: 324,
      manager: "Sarah Wilson",
      team: [
        { name: "John Doe", avatar: "/placeholder.svg?height=32&width=32", role: "Frontend Dev" },
        { name: "Lisa Chen", avatar: "/placeholder.svg?height=32&width=32", role: "Designer" },
        { name: "Mike Johnson", avatar: "/placeholder.svg?height=32&width=32", role: "Backend Dev" },
        { name: "Emma Davis", avatar: "/placeholder.svg?height=32&width=32", role: "QA Engineer" },
      ],
    },
    {
      id: "PRJ002",
      name: "Mobile App Development",
      description: "Native iOS and Android app development for customer engagement",
      status: "Planning",
      priority: "Medium",
      progress: 15,
      startDate: "2024-02-01",
      endDate: "2024-06-15",
      budget: 120000,
      spent: 18000,
      teamSize: 6,
      tasksTotal: 67,
      tasksCompleted: 10,
      hoursLogged: 156,
      manager: "Tom Anderson",
      team: [
        { name: "David Park", avatar: "/placeholder.svg?height=32&width=32", role: "iOS Dev" },
        { name: "Jennifer Kim", avatar: "/placeholder.svg?height=32&width=32", role: "Android Dev" },
        { name: "Alex Rodriguez", avatar: "/placeholder.svg?height=32&width=32", role: "UI Designer" },
      ],
    },
    {
      id: "PRJ003",
      name: "Data Analytics Dashboard",
      description: "Business intelligence dashboard for real-time analytics and reporting",
      status: "In Progress",
      priority: "High",
      progress: 82,
      startDate: "2023-11-20",
      endDate: "2024-02-28",
      budget: 65000,
      spent: 53200,
      teamSize: 5,
      tasksTotal: 38,
      tasksCompleted: 31,
      hoursLogged: 287,
      manager: "Lisa Wilson",
      team: [
        { name: "Robert Chen", avatar: "/placeholder.svg?height=32&width=32", role: "Data Engineer" },
        { name: "Maria Garcia", avatar: "/placeholder.svg?height=32&width=32", role: "Frontend Dev" },
        { name: "James Wilson", avatar: "/placeholder.svg?height=32&width=32", role: "Data Analyst" },
      ],
    },
    {
      id: "PRJ004",
      name: "Security Audit & Compliance",
      description: "Comprehensive security audit and implementation of compliance measures",
      status: "Review",
      priority: "Critical",
      progress: 95,
      startDate: "2023-12-01",
      endDate: "2024-01-31",
      budget: 45000,
      spent: 42750,
      teamSize: 4,
      tasksTotal: 28,
      tasksCompleted: 27,
      hoursLogged: 198,
      manager: "Michael Brown",
      team: [
        { name: "Sarah Johnson", avatar: "/placeholder.svg?height=32&width=32", role: "Security Expert" },
        { name: "Kevin Lee", avatar: "/placeholder.svg?height=32&width=32", role: "DevOps" },
      ],
    },
  ]

  const recentActivities = [
    {
      id: 1,
      type: "task_completed",
      description: "User authentication module completed",
      project: "E-commerce Platform Redesign",
      user: "John Doe",
      time: "2 hours ago",
    },
    {
      id: 2,
      type: "comment_added",
      description: "Added feedback on mobile wireframes",
      project: "Mobile App Development",
      user: "Lisa Chen",
      time: "4 hours ago",
    },
    {
      id: 3,
      type: "time_logged",
      description: "Logged 6 hours on dashboard development",
      project: "Data Analytics Dashboard",
      user: "Maria Garcia",
      time: "1 day ago",
    },
    {
      id: 4,
      type: "milestone_reached",
      description: "Security testing phase completed",
      project: "Security Audit & Compliance",
      user: "Sarah Johnson",
      time: "2 days ago",
    },
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case "Planning":
        return "bg-gray-100 text-gray-800"
      case "In Progress":
        return "bg-blue-100 text-blue-800"
      case "Review":
        return "bg-yellow-100 text-yellow-800"
      case "Completed":
        return "bg-green-100 text-green-800"
      case "On Hold":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "Critical":
        return "bg-red-100 text-red-800"
      case "High":
        return "bg-orange-100 text-orange-800"
      case "Medium":
        return "bg-yellow-100 text-yellow-800"
      case "Low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getActivityIcon = (type) => {
    switch (type) {
      case "task_completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "comment_added":
        return <Activity className="h-4 w-4 text-blue-600" />
      case "time_logged":
        return <Clock className="h-4 w-4 text-purple-600" />
      case "milestone_reached":
        return <Target className="h-4 w-4 text-orange-600" />
      default:
        return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const startTimer = (projectId) => {
    setActiveTimer(projectId)
    setTimerSeconds(0)
    // In a real app, you'd start an actual timer here
  }

  const stopTimer = () => {
    setActiveTimer(null)
    setTimerSeconds(0)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Project Management</h1>
            <p className="text-gray-600 mt-1">Manage projects, tasks, and team collaboration with time tracking</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Reports
            </Button>
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Project Settings
            </Button>
            <Dialog open={isCreateProjectOpen} onOpenChange={setIsCreateProjectOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  New Project
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[525px]">
                <DialogHeader>
                  <DialogTitle>Create New Project</DialogTitle>
                  <DialogDescription>Set up a new project with team members and timeline.</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="projectName" className="text-right">
                      Name
                    </Label>
                    <Input id="projectName" placeholder="Project name" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-start gap-4">
                    <Label htmlFor="description" className="text-right mt-2">
                      Description
                    </Label>
                    <Textarea id="description" placeholder="Project description..." className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="priority" className="text-right">
                      Priority
                    </Label>
                    <Select>
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select priority" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="critical">Critical</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="budget" className="text-right">
                      Budget
                    </Label>
                    <Input id="budget" type="number" placeholder="Project budget" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="startDate">Start Date</Label>
                      <Input id="startDate" type="date" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="endDate">End Date</Label>
                      <Input id="endDate" type="date" />
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" className="bg-gradient-to-r from-purple-600 to-blue-600">
                    Create Project
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {projectStats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-green-600">{stat.change}</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="kanban">Kanban Board</TabsTrigger>
            <TabsTrigger value="gantt">Gantt Chart</TabsTrigger>
            <TabsTrigger value="time-tracking">Time Tracking</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Projects List */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Active Projects</CardTitle>
                      <div className="flex space-x-2">
                        <div className="relative">
                          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            placeholder="Search projects..."
                            className="pl-10 w-64"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                          />
                        </div>
                        <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                          <SelectTrigger className="w-32">
                            <SelectValue placeholder="Status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Status</SelectItem>
                            <SelectItem value="planning">Planning</SelectItem>
                            <SelectItem value="in-progress">In Progress</SelectItem>
                            <SelectItem value="review">Review</SelectItem>
                            <SelectItem value="completed">Completed</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {projects.map((project) => (
                        <Card key={project.id} className="hover:shadow-md transition-shadow cursor-pointer">
                          <CardContent className="p-4">
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex-1">
                                <div className="flex items-center space-x-3 mb-2">
                                  <h3 className="font-semibold text-gray-900">{project.name}</h3>
                                  <Badge className={getStatusColor(project.status)}>{project.status}</Badge>
                                  <Badge className={getPriorityColor(project.priority)}>{project.priority}</Badge>
                                </div>
                                <p className="text-sm text-gray-600 mb-3">{project.description}</p>
                                <div className="flex items-center space-x-6 text-sm text-gray-500">
                                  <span>Manager: {project.manager}</span>
                                  <span>Team: {project.teamSize} members</span>
                                  <span>
                                    {formatDate(project.startDate)} - {formatDate(project.endDate)}
                                  </span>
                                </div>
                              </div>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" className="h-8 w-8 p-0">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem>View Details</DropdownMenuItem>
                                  <DropdownMenuItem>Edit Project</DropdownMenuItem>
                                  <DropdownMenuItem>View Kanban</DropdownMenuItem>
                                  <DropdownMenuItem>Time Tracking</DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem>Archive Project</DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>

                            <div className="space-y-3">
                              <div>
                                <div className="flex items-center justify-between mb-1">
                                  <span className="text-sm text-gray-600">Progress</span>
                                  <span className="text-sm font-medium">{project.progress}%</span>
                                </div>
                                <Progress value={project.progress} className="h-2" />
                              </div>

                              <div className="grid grid-cols-4 gap-4 text-sm">
                                <div>
                                  <span className="text-gray-600">Tasks</span>
                                  <div className="font-semibold">
                                    {project.tasksCompleted}/{project.tasksTotal}
                                  </div>
                                </div>
                                <div>
                                  <span className="text-gray-600">Budget</span>
                                  <div className="font-semibold">{formatCurrency(project.budget)}</div>
                                </div>
                                <div>
                                  <span className="text-gray-600">Spent</span>
                                  <div className="font-semibold text-orange-600">{formatCurrency(project.spent)}</div>
                                </div>
                                <div>
                                  <span className="text-gray-600">Hours</span>
                                  <div className="font-semibold">{project.hoursLogged}h</div>
                                </div>
                              </div>

                              <div className="flex items-center justify-between">
                                <div className="flex -space-x-2">
                                  {project.team.slice(0, 4).map((member, index) => (
                                    <Avatar key={index} className="h-6 w-6 border-2 border-white">
                                      <AvatarImage src={member.avatar || "/placeholder.svg"} alt={member.name} />
                                      <AvatarFallback className="text-xs">
                                        {member.name
                                          .split(" ")
                                          .map((n) => n[0])
                                          .join("")}
                                      </AvatarFallback>
                                    </Avatar>
                                  ))}
                                  {project.team.length > 4 && (
                                    <div className="h-6 w-6 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center">
                                      <span className="text-xs text-gray-600">+{project.team.length - 4}</span>
                                    </div>
                                  )}
                                </div>
                                <div className="flex space-x-2">
                                  {activeTimer === project.id ? (
                                    <Button size="sm" variant="outline" onClick={stopTimer}>
                                      <Square className="h-3 w-3 mr-1" />
                                      Stop
                                    </Button>
                                  ) : (
                                    <Button size="sm" variant="outline" onClick={() => startTimer(project.id)}>
                                      <Play className="h-3 w-3 mr-1" />
                                      Start Timer
                                    </Button>
                                  )}
                                  <Button size="sm" variant="outline">
                                    View Board
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Recent Activities */}
              <div>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Activity className="h-5 w-5 mr-2 text-orange-600" />
                      Recent Activities
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {recentActivities.map((activity) => (
                        <div key={activity.id} className="flex items-start space-x-3">
                          <div className="p-1 bg-gray-100 rounded-lg">{getActivityIcon(activity.type)}</div>
                          <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium text-gray-900">{activity.description}</div>
                            <div className="text-xs text-gray-500">
                              {activity.project} • {activity.user}
                            </div>
                            <div className="text-xs text-gray-400">{activity.time}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Quick Stats */}
                <Card className="mt-6">
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <BarChart3 className="h-5 w-5 mr-2 text-purple-600" />
                      Quick Stats
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Projects On Track</span>
                        <span className="font-semibold text-green-600">18/24</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Overdue Tasks</span>
                        <span className="font-semibold text-red-600">7</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Team Utilization</span>
                        <span className="font-semibold">87%</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Avg. Project Duration</span>
                        <span className="font-semibold">3.2 months</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Kanban Board Tab */}
          <TabsContent value="kanban" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <FolderKanban className="h-5 w-5 mr-2 text-blue-600" />
                    Kanban Board - E-commerce Platform Redesign
                  </CardTitle>
                  <div className="flex space-x-2">
                    <Select defaultValue="all-members">
                      <SelectTrigger className="w-40">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all-members">All Members</SelectItem>
                        <SelectItem value="john-doe">John Doe</SelectItem>
                        <SelectItem value="lisa-chen">Lisa Chen</SelectItem>
                        <SelectItem value="mike-johnson">Mike Johnson</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button variant="outline" size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Task
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  {/* To Do Column */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-gray-900">To Do</h3>
                      <Badge variant="outline">8</Badge>
                    </div>
                    <div className="space-y-3">
                      <Card className="hover:shadow-md transition-shadow cursor-pointer">
                        <CardContent className="p-3">
                          <div className="flex items-start justify-between mb-2">
                            <Badge className="bg-red-100 text-red-800 text-xs">High</Badge>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-6 w-6 p-0">
                                  <MoreHorizontal className="h-3 w-3" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>Edit Task</DropdownMenuItem>
                                <DropdownMenuItem>Assign Member</DropdownMenuItem>
                                <DropdownMenuItem>Set Due Date</DropdownMenuItem>
                                <DropdownMenuItem>Delete Task</DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                          <h4 className="font-medium text-sm mb-2">Implement user authentication</h4>
                          <p className="text-xs text-gray-600 mb-3">
                            Set up secure login system with JWT tokens and password encryption
                          </p>
                          <div className="flex items-center justify-between">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src="/placeholder.svg" alt="John Doe" />
                              <AvatarFallback className="text-xs">JD</AvatarFallback>
                            </Avatar>
                            <div className="flex items-center space-x-1 text-xs text-gray-500">
                              <Calendar className="h-3 w-3" />
                              <span>Jan 25</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="hover:shadow-md transition-shadow cursor-pointer">
                        <CardContent className="p-3">
                          <div className="flex items-start justify-between mb-2">
                            <Badge className="bg-yellow-100 text-yellow-800 text-xs">Medium</Badge>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-6 w-6 p-0">
                                  <MoreHorizontal className="h-3 w-3" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>Edit Task</DropdownMenuItem>
                                <DropdownMenuItem>Assign Member</DropdownMenuItem>
                                <DropdownMenuItem>Set Due Date</DropdownMenuItem>
                                <DropdownMenuItem>Delete Task</DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                          <h4 className="font-medium text-sm mb-2">Design product catalog layout</h4>
                          <p className="text-xs text-gray-600 mb-3">
                            Create responsive grid layout for product listings
                          </p>
                          <div className="flex items-center justify-between">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src="/placeholder.svg" alt="Lisa Chen" />
                              <AvatarFallback className="text-xs">LC</AvatarFallback>
                            </Avatar>
                            <div className="flex items-center space-x-1 text-xs text-gray-500">
                              <Calendar className="h-3 w-3" />
                              <span>Jan 28</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>

                  {/* In Progress Column */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-gray-900">In Progress</h3>
                      <Badge variant="outline">5</Badge>
                    </div>
                    <div className="space-y-3">
                      <Card className="hover:shadow-md transition-shadow cursor-pointer border-blue-200">
                        <CardContent className="p-3">
                          <div className="flex items-start justify-between mb-2">
                            <Badge className="bg-orange-100 text-orange-800 text-xs">High</Badge>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-6 w-6 p-0">
                                  <MoreHorizontal className="h-3 w-3" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>Edit Task</DropdownMenuItem>
                                <DropdownMenuItem>Assign Member</DropdownMenuItem>
                                <DropdownMenuItem>Set Due Date</DropdownMenuItem>
                                <DropdownMenuItem>Delete Task</DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                          <h4 className="font-medium text-sm mb-2">Shopping cart functionality</h4>
                          <p className="text-xs text-gray-600 mb-3">
                            Implement add to cart, quantity updates, and checkout flow
                          </p>
                          <div className="mb-2">
                            <Progress value={65} className="h-1" />
                            <div className="text-xs text-gray-500 mt-1">65% complete</div>
                          </div>
                          <div className="flex items-center justify-between">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src="/placeholder.svg" alt="Mike Johnson" />
                              <AvatarFallback className="text-xs">MJ</AvatarFallback>
                            </Avatar>
                            <div className="flex items-center space-x-1 text-xs text-gray-500">
                              <Timer className="h-3 w-3" />
                              <span>4.5h</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>

                  {/* Review Column */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-gray-900">Review</h3>
                      <Badge variant="outline">3</Badge>
                    </div>
                    <div className="space-y-3">
                      <Card className="hover:shadow-md transition-shadow cursor-pointer border-yellow-200">
                        <CardContent className="p-3">
                          <div className="flex items-start justify-between mb-2">
                            <Badge className="bg-green-100 text-green-800 text-xs">Low</Badge>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-6 w-6 p-0">
                                  <MoreHorizontal className="h-3 w-3" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>Edit Task</DropdownMenuItem>
                                <DropdownMenuItem>Assign Member</DropdownMenuItem>
                                <DropdownMenuItem>Set Due Date</DropdownMenuItem>
                                <DropdownMenuItem>Delete Task</DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                          <h4 className="font-medium text-sm mb-2">Payment gateway integration</h4>
                          <p className="text-xs text-gray-600 mb-3">Stripe payment processing implementation</p>
                          <div className="flex items-center justify-between">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src="/placeholder.svg" alt="Emma Davis" />
                              <AvatarFallback className="text-xs">ED</AvatarFallback>
                            </Avatar>
                            <div className="flex items-center space-x-1 text-xs text-green-600">
                              <CheckCircle className="h-3 w-3" />
                              <span>Ready</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>

                  {/* Done Column */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-gray-900">Done</h3>
                      <Badge variant="outline">12</Badge>
                    </div>
                    <div className="space-y-3">
                      <Card className="hover:shadow-md transition-shadow cursor-pointer border-green-200 bg-green-50">
                        <CardContent className="p-3">
                          <div className="flex items-start justify-between mb-2">
                            <Badge className="bg-green-100 text-green-800 text-xs">Completed</Badge>
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          </div>
                          <h4 className="font-medium text-sm mb-2">Database schema design</h4>
                          <p className="text-xs text-gray-600 mb-3">Complete database structure for e-commerce data</p>
                          <div className="flex items-center justify-between">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src="/placeholder.svg" alt="John Doe" />
                              <AvatarFallback className="text-xs">JD</AvatarFallback>
                            </Avatar>
                            <div className="flex items-center space-x-1 text-xs text-gray-500">
                              <span>Completed</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Gantt Chart Tab */}
          <TabsContent value="gantt" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2 text-purple-600" />
                  Gantt Chart - Project Timeline
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Select defaultValue="e-commerce">
                      <SelectTrigger className="w-64">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="e-commerce">E-commerce Platform Redesign</SelectItem>
                        <SelectItem value="mobile-app">Mobile App Development</SelectItem>
                        <SelectItem value="analytics">Data Analytics Dashboard</SelectItem>
                      </SelectContent>
                    </Select>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        <Calendar className="h-4 w-4 mr-2" />
                        View: Month
                      </Button>
                      <Button variant="outline" size="sm">
                        Export Chart
                      </Button>
                    </div>
                  </div>

                  {/* Gantt Chart Visualization */}
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <div className="grid grid-cols-12 gap-1 mb-4">
                      <div className="col-span-3 font-semibold text-sm">Task</div>
                      <div className="col-span-9 grid grid-cols-12 gap-1">
                        {["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"].map(
                          (month, index) => (
                            <div key={index} className="text-xs text-center font-medium text-gray-600">
                              {month}
                            </div>
                          ),
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      {/* Task 1 */}
                      <div className="grid grid-cols-12 gap-1 items-center">
                        <div className="col-span-3 text-sm">Project Planning</div>
                        <div className="col-span-9 grid grid-cols-12 gap-1">
                          <div className="bg-blue-500 h-6 rounded col-span-1"></div>
                          <div className="col-span-11"></div>
                        </div>
                      </div>

                      {/* Task 2 */}
                      <div className="grid grid-cols-12 gap-1 items-center">
                        <div className="col-span-3 text-sm">UI/UX Design</div>
                        <div className="col-span-9 grid grid-cols-12 gap-1">
                          <div className="col-span-1"></div>
                          <div className="bg-green-500 h-6 rounded col-span-2"></div>
                          <div className="col-span-9"></div>
                        </div>
                      </div>

                      {/* Task 3 */}
                      <div className="grid grid-cols-12 gap-1 items-center">
                        <div className="col-span-3 text-sm">Frontend Development</div>
                        <div className="col-span-9 grid grid-cols-12 gap-1">
                          <div className="col-span-2"></div>
                          <div className="bg-purple-500 h-6 rounded col-span-3"></div>
                          <div className="col-span-7"></div>
                        </div>
                      </div>

                      {/* Task 4 */}
                      <div className="grid grid-cols-12 gap-1 items-center">
                        <div className="col-span-3 text-sm">Backend Development</div>
                        <div className="col-span-9 grid grid-cols-12 gap-1">
                          <div className="col-span-2"></div>
                          <div className="bg-orange-500 h-6 rounded col-span-4"></div>
                          <div className="col-span-6"></div>
                        </div>
                      </div>

                      {/* Task 5 */}
                      <div className="grid grid-cols-12 gap-1 items-center">
                        <div className="col-span-3 text-sm">Testing & QA</div>
                        <div className="col-span-9 grid grid-cols-12 gap-1">
                          <div className="col-span-5"></div>
                          <div className="bg-red-500 h-6 rounded col-span-2"></div>
                          <div className="col-span-5"></div>
                        </div>
                      </div>

                      {/* Task 6 */}
                      <div className="grid grid-cols-12 gap-1 items-center">
                        <div className="col-span-3 text-sm">Deployment</div>
                        <div className="col-span-9 grid grid-cols-12 gap-1">
                          <div className="col-span-6"></div>
                          <div className="bg-indigo-500 h-6 rounded col-span-1"></div>
                          <div className="col-span-5"></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Timeline Legend */}
                  <div className="flex items-center space-x-6 text-sm">
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 bg-blue-500 rounded"></div>
                      <span>Planning</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 bg-green-500 rounded"></div>
                      <span>Design</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 bg-purple-500 rounded"></div>
                      <span>Frontend</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 bg-orange-500 rounded"></div>
                      <span>Backend</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 bg-red-500 rounded"></div>
                      <span>Testing</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 bg-indigo-500 rounded"></div>
                      <span>Deployment</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Time Tracking Tab */}
          <TabsContent value="time-tracking" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Timer className="h-5 w-5 mr-2 text-green-600" />
                      Active Time Tracking
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {projects.slice(0, 3).map((project) => (
                        <div key={project.id} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex items-center space-x-4">
                            <div className="p-2 bg-purple-100 rounded-lg">
                              <FolderKanban className="h-5 w-5 text-purple-600" />
                            </div>
                            <div>
                              <h4 className="font-medium">{project.name}</h4>
                              <p className="text-sm text-gray-600">Task: Shopping cart functionality</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-4">
                            <div className="text-right">
                              <div className="font-mono text-lg font-semibold">
                                {activeTimer === project.id ? "02:34:15" : "00:00:00"}
                              </div>
                              <div className="text-xs text-gray-500">Today: 6h 23m</div>
                            </div>
                            <div className="flex space-x-2">
                              {activeTimer === project.id ? (
                                <>
                                  <Button size="sm" variant="outline" onClick={stopTimer}>
                                    <Pause className="h-3 w-3 mr-1" />
                                    Pause
                                  </Button>
                                  <Button size="sm" variant="outline" onClick={stopTimer}>
                                    <Square className="h-3 w-3 mr-1" />
                                    Stop
                                  </Button>
                                </>
                              ) : (
                                <Button size="sm" onClick={() => startTimer(project.id)}>
                                  <Play className="h-3 w-3 mr-1" />
                                  Start
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card className="mt-6">
                  <CardHeader>
                    <CardTitle>Time Logs</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {[
                        {
                          project: "E-commerce Platform",
                          task: "Shopping cart functionality",
                          duration: "2h 45m",
                          date: "Today",
                          user: "Mike Johnson",
                        },
                        {
                          project: "Mobile App Development",
                          task: "User interface design",
                          duration: "4h 15m",
                          date: "Yesterday",
                          user: "Lisa Chen",
                        },
                        {
                          project: "Data Analytics Dashboard",
                          task: "Chart implementation",
                          duration: "3h 30m",
                          date: "Jan 22",
                          user: "Maria Garcia",
                        },
                      ].map((log, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <div className="font-medium text-sm">{log.task}</div>
                            <div className="text-xs text-gray-600">{log.project}</div>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold">{log.duration}</div>
                            <div className="text-xs text-gray-500">
                              {log.date} • {log.user}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Clock className="h-5 w-5 mr-2 text-blue-600" />
                      Time Summary
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-purple-600">8h 23m</div>
                        <div className="text-sm text-gray-600">Today</div>
                      </div>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">This Week</span>
                          <span className="font-semibold">42h 15m</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">This Month</span>
                          <span className="font-semibold">168h 45m</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Billable Hours</span>
                          <span className="font-semibold text-green-600">156h 30m</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="mt-6">
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <TrendingUp className="h-5 w-5 mr-2 text-orange-600" />
                      Productivity
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-gray-600">Daily Goal</span>
                          <span className="text-sm font-medium">8h</span>
                        </div>
                        <Progress value={85} className="h-2" />
                        <div className="text-xs text-gray-500 mt-1">85% complete</div>
                      </div>
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-gray-600">Weekly Goal</span>
                          <span className="text-sm font-medium">40h</span>
                        </div>
                        <Progress value={92} className="h-2" />
                        <div className="text-xs text-gray-500 mt-1">92% complete</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Reports Tab */}
          <TabsContent value="reports" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <BarChart3 className="h-5 w-5 mr-2 text-blue-600" />
                    Project Performance
                  </CardTitle>
                  <CardDescription>Progress, budget, and timeline analysis</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">On-time Delivery</span>
                      <span className="font-semibold text-green-600">87%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Budget Utilization</span>
                      <span className="font-semibold">73%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Team Satisfaction</span>
                      <span className="font-semibold text-blue-600">4.2/5</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <Clock className="h-5 w-5 mr-2 text-purple-600" />
                    Time Analytics
                  </CardTitle>
                  <CardDescription>Time tracking and productivity metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Hours</span>
                      <span className="font-semibold">1,247h</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Billable Hours</span>
                      <span className="font-semibold text-green-600">1,156h</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Avg. Daily Hours</span>
                      <span className="font-semibold">7.8h</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <Users className="h-5 w-5 mr-2 text-green-600" />
                    Team Performance
                  </CardTitle>
                  <CardDescription>Individual and team productivity analysis</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Team Utilization</span>
                      <span className="font-semibold">87%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Tasks Completed</span>
                      <span className="font-semibold text-green-600">342</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Avg. Task Duration</span>
                      <span className="font-semibold">2.3 days</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
