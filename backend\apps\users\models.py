from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin
from django.db import models
from django.utils import timezone
from django.core.validators import RegexValidator
from phonenumber_field.modelfields import PhoneNumberField
from apps.companies.models import BaseModel, Company, Grade
from .managers import UserManager
import uuid


class User(AbstractBaseUser, PermissionsMixin, BaseModel):
    """Custom User model for enterprise user management"""
    
    ACCOUNT_TYPES = [
        ('employee', 'Employee'),
        ('contractor', 'Contractor'),
        ('vendor', 'Vendor'),
        ('customer', 'Customer'),
        ('admin', 'System Admin'),
    ]
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('suspended', 'Suspended'),
        ('terminated', 'Terminated'),
        ('pending', 'Pending Activation'),
    ]
    
    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
        ('O', 'Other'),
        ('N', 'Prefer not to say'),
    ]
    
    # Basic Information
    username = models.CharField(max_length=150, unique=True)
    email = models.EmailField(unique=True)
    employee_id = models.CharField(max_length=50, blank=True)
    
    # Personal Information
    first_name = models.CharField(max_length=150)
    middle_name = models.CharField(max_length=150, blank=True)
    last_name = models.CharField(max_length=150)
    display_name = models.CharField(max_length=255, blank=True)
    date_of_birth = models.DateField(null=True, blank=True)
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES, blank=True)
    
    # Contact Information
    phone_primary = PhoneNumberField(blank=True)
    phone_secondary = PhoneNumberField(blank=True)
    emergency_contact_name = models.CharField(max_length=255, blank=True)
    emergency_contact_phone = PhoneNumberField(blank=True)
    emergency_contact_relationship = models.CharField(max_length=100, blank=True)
    
    # Address Information
    current_address = models.TextField(blank=True)
    permanent_address = models.TextField(blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=100, blank=True)
    country = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    
    # Professional Information
    nationality = models.CharField(max_length=100, blank=True)
    languages_spoken = models.JSONField(default=list, blank=True)
    
    # Account Information
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='users')
    account_type = models.CharField(max_length=20, choices=ACCOUNT_TYPES, default='employee')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    security_clearance_level = models.CharField(max_length=50, blank=True)
    
    # Profile
    profile_picture = models.ImageField(upload_to='profile_pictures/', null=True, blank=True)
    bio = models.TextField(blank=True)
    
    # Django User Model Fields
    is_staff = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    date_joined = models.DateTimeField(default=timezone.now)
    last_login = models.DateTimeField(null=True, blank=True)
    
    # Password Management
    password_changed_at = models.DateTimeField(null=True, blank=True)
    password_expires_at = models.DateTimeField(null=True, blank=True)
    failed_login_attempts = models.IntegerField(default=0)
    account_locked_until = models.DateTimeField(null=True, blank=True)
    
    objects = UserManager()
    
    USERNAME_FIELD = 'username'
    REQUIRED_FIELDS = ['email', 'first_name', 'last_name']
    
    class Meta:
        verbose_name = 'User'
        verbose_name_plural = 'Users'
        ordering = ['first_name', 'last_name']
        indexes = [
            models.Index(fields=['username']),
            models.Index(fields=['email']),
            models.Index(fields=['employee_id']),
            models.Index(fields=['company', 'status']),
            models.Index(fields=['status']),
        ]
    
    def __str__(self):
        return f"{self.get_full_name()} ({self.username})"
    
    def get_full_name(self):
        """Return the full name of the user"""
        if self.middle_name:
            return f"{self.first_name} {self.middle_name} {self.last_name}"
        return f"{self.first_name} {self.last_name}"
    
    def get_short_name(self):
        """Return the short name for the user"""
        return self.first_name
    
    def get_display_name(self):
        """Return the display name or full name"""
        return self.display_name or self.get_full_name()
    
    def is_account_locked(self):
        """Check if account is currently locked"""
        if self.account_locked_until:
            return timezone.now() < self.account_locked_until
        return False
    
    def is_password_expired(self):
        """Check if password has expired"""
        if self.password_expires_at:
            return timezone.now() > self.password_expires_at
        return False
    
    def can_login(self):
        """Check if user can login"""
        return (
            self.is_active and 
            self.status == 'active' and 
            not self.is_account_locked() and 
            not self.is_password_expired()
        )


class UserEmployment(BaseModel):
    """User employment details and work information"""
    
    EMPLOYMENT_TYPES = [
        ('full_time', 'Full Time'),
        ('part_time', 'Part Time'),
        ('contract', 'Contract'),
        ('intern', 'Intern'),
        ('consultant', 'Consultant'),
    ]
    
    EMPLOYMENT_STATUS = [
        ('active', 'Active'),
        ('on_leave', 'On Leave'),
        ('terminated', 'Terminated'),
        ('resigned', 'Resigned'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='employment')
    
    # Employment Details
    employment_type = models.CharField(max_length=20, choices=EMPLOYMENT_TYPES)
    employment_status = models.CharField(max_length=20, choices=EMPLOYMENT_STATUS, default='active')
    hire_date = models.DateField()
    termination_date = models.DateField(null=True, blank=True)
    probation_end_date = models.DateField(null=True, blank=True)
    
    # Organizational Assignment
    department = models.ForeignKey(
        'departments.Department', 
        on_delete=models.SET_NULL, 
        null=True, 
        related_name='employees'
    )
    location = models.ForeignKey(
        'locations.Location', 
        on_delete=models.SET_NULL, 
        null=True, 
        related_name='employees'
    )
    designation = models.ForeignKey(
        'Designation', 
        on_delete=models.SET_NULL, 
        null=True, 
        related_name='employees'
    )
    grade = models.ForeignKey(Grade, on_delete=models.SET_NULL, null=True, related_name='employees')
    cost_center = models.ForeignKey(
        'companies.CostCenter', 
        on_delete=models.SET_NULL, 
        null=True, 
        related_name='employees'
    )
    
    # Reporting Structure
    reporting_manager = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='direct_reports'
    )
    
    # Work Arrangements
    work_schedule = models.CharField(max_length=100, blank=True)
    remote_work_allowed = models.BooleanField(default=False)
    work_location_flexibility = models.CharField(max_length=100, blank=True)
    
    # Compensation (basic info - detailed compensation in separate app)
    salary_grade_band = models.CharField(max_length=50, blank=True)
    
    class Meta:
        verbose_name = 'User Employment'
        verbose_name_plural = 'User Employment Records'
        indexes = [
            models.Index(fields=['employment_status']),
            models.Index(fields=['hire_date']),
            models.Index(fields=['department']),
            models.Index(fields=['location']),
        ]
    
    def __str__(self):
        return f"{self.user.get_full_name()} - {self.employment_type}"
    
    def get_tenure_years(self):
        """Calculate tenure in years"""
        end_date = self.termination_date or timezone.now().date()
        return (end_date - self.hire_date).days / 365.25


class Designation(BaseModel):
    """Job designation/title model"""
    
    DESIGNATION_LEVELS = [
        ('c_level', 'C-Level'),
        ('vp', 'Vice President'),
        ('director', 'Director'),
        ('manager', 'Manager'),
        ('senior', 'Senior'),
        ('mid', 'Mid-Level'),
        ('junior', 'Junior'),
        ('entry', 'Entry Level'),
    ]
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    ]
    
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='designations')
    code = models.CharField(max_length=50)
    title = models.CharField(max_length=255)
    level = models.CharField(max_length=20, choices=DESIGNATION_LEVELS)
    department_category = models.CharField(max_length=100, blank=True)
    job_family = models.CharField(max_length=100, blank=True)
    career_track = models.CharField(max_length=100, blank=True)
    minimum_experience_years = models.IntegerField(default=0)
    salary_grade_min = models.ForeignKey(
        Grade, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='designation_min_grades'
    )
    salary_grade_max = models.ForeignKey(
        Grade, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='designation_max_grades'
    )
    reports_to = models.ForeignKey(
        'self', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='subordinate_designations'
    )
    description = models.TextField(blank=True)
    responsibilities = models.TextField(blank=True)
    required_skills = models.JSONField(default=list, blank=True)
    preferred_qualifications = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    
    class Meta:
        unique_together = ['company', 'code']
        ordering = ['company', 'title']
        indexes = [
            models.Index(fields=['company', 'level']),
            models.Index(fields=['status']),
        ]
    
    def __str__(self):
        return f"{self.title} ({self.code})"


class UserSkill(BaseModel):
    """User skills and competencies"""
    
    PROFICIENCY_LEVELS = [
        ('beginner', 'Beginner'),
        ('intermediate', 'Intermediate'),
        ('advanced', 'Advanced'),
        ('expert', 'Expert'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='skills')
    skill = models.ForeignKey('Skill', on_delete=models.CASCADE, related_name='user_skills')
    proficiency_level = models.CharField(max_length=20, choices=PROFICIENCY_LEVELS)
    years_of_experience = models.DecimalField(max_digits=4, decimal_places=1, default=0)
    last_used_date = models.DateField(null=True, blank=True)
    certified = models.BooleanField(default=False)
    certification_details = models.TextField(blank=True)
    self_assessed = models.BooleanField(default=True)
    manager_verified = models.BooleanField(default=False)
    verified_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='verified_skills'
    )
    verified_date = models.DateField(null=True, blank=True)
    
    class Meta:
        unique_together = ['user', 'skill']
        ordering = ['user', 'skill']
    
    def __str__(self):
        return f"{self.user.get_full_name()} - {self.skill.name} ({self.proficiency_level})"


class Skill(BaseModel):
    """Skills master data"""
    
    SKILL_CATEGORIES = [
        ('technical', 'Technical'),
        ('functional', 'Functional'),
        ('behavioral', 'Behavioral'),
        ('leadership', 'Leadership'),
        ('language', 'Language'),
    ]
    
    SKILL_TYPES = [
        ('hard', 'Hard Skill'),
        ('soft', 'Soft Skill'),
    ]
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    ]
    
    code = models.CharField(max_length=50, unique=True)
    name = models.CharField(max_length=255)
    category = models.CharField(max_length=20, choices=SKILL_CATEGORIES)
    skill_type = models.CharField(max_length=10, choices=SKILL_TYPES)
    proficiency_levels = models.JSONField(default=list)  # Custom proficiency levels if needed
    certification_available = models.BooleanField(default=False)
    industry_standard = models.BooleanField(default=False)
    description = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    
    class Meta:
        ordering = ['category', 'name']
        indexes = [
            models.Index(fields=['category']),
            models.Index(fields=['skill_type']),
            models.Index(fields=['status']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.category})"
