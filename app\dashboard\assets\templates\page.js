"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import {
  Plus,
  Search,
  Filter,
  Download,
  Edit,
  Copy,
  Trash2,
  FileIcon as FileTemplate,
  Users,
  Building,
  Wrench,
  UserPlus,
  UserMinus,
  AlertTriangle,
  MoreHorizontal,
  Star,
  Clock,
  CheckCircle,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"

export default function TransferTemplatesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [isNewTemplateOpen, setIsNewTemplateOpen] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [isEditTemplateOpen, setIsEditTemplateOpen] = useState(false)

  // Mock data for transfer templates
  const [templates] = useState([
    {
      id: "TPL-001",
      name: "Employee Onboarding - IT Equipment",
      description: "Standard IT equipment assignment for new employees",
      category: "onboarding",
      icon: UserPlus,
      color: "text-green-600",
      bgColor: "bg-green-100",
      isActive: true,
      isDefault: true,
      usageCount: 45,
      lastUsed: "2024-01-18",
      createdBy: "IT Manager",
      createdDate: "2023-12-01",
      transferType: "user",
      priority: "normal",
      estimatedDuration: "2-3 days",
      approvalSteps: [
        { name: "Direct Manager", required: true },
        { name: "IT Manager", required: true },
      ],
      assetCategories: ["Laptops", "Monitors", "Keyboards", "Mice"],
      defaultReason: "New employee equipment assignment as per company policy",
      requiredFields: ["employee_id", "department", "start_date", "manager_approval"],
      autoAssignApprovers: true,
      notificationSettings: {
        sendToManager: true,
        sendToIT: true,
        sendToEmployee: true,
        reminderInterval: 24,
      },
    },
    {
      id: "TPL-002",
      name: "Department Relocation",
      description: "Transfer assets when entire departments move locations",
      category: "department_move",
      icon: Building,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      isActive: true,
      isDefault: false,
      usageCount: 12,
      lastUsed: "2024-01-15",
      createdBy: "Facilities Manager",
      createdDate: "2023-11-15",
      transferType: "location",
      priority: "high",
      estimatedDuration: "5-7 days",
      approvalSteps: [
        { name: "Department Head", required: true },
        { name: "Facilities Manager", required: true },
        { name: "IT Manager", required: false },
      ],
      assetCategories: ["All Categories"],
      defaultReason: "Department relocation to optimize office space utilization",
      requiredFields: ["department", "new_location", "move_date", "contact_person"],
      autoAssignApprovers: true,
      notificationSettings: {
        sendToManager: true,
        sendToIT: true,
        sendToEmployee: false,
        reminderInterval: 48,
      },
    },
    {
      id: "TPL-003",
      name: "Equipment Upgrade Program",
      description: "Systematic replacement of outdated equipment with newer models",
      category: "upgrade",
      icon: Wrench,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
      isActive: true,
      isDefault: false,
      usageCount: 28,
      lastUsed: "2024-01-20",
      createdBy: "Asset Manager",
      createdDate: "2023-10-01",
      transferType: "both",
      priority: "normal",
      estimatedDuration: "3-5 days",
      approvalSteps: [
        { name: "Asset Manager", required: true },
        { name: "Budget Approver", required: true },
      ],
      assetCategories: ["Laptops", "Desktops", "Servers", "Network Equipment"],
      defaultReason: "Equipment upgrade as part of technology refresh program",
      requiredFields: ["old_asset_id", "new_asset_id", "upgrade_reason", "budget_code"],
      autoAssignApprovers: false,
      notificationSettings: {
        sendToManager: true,
        sendToIT: true,
        sendToEmployee: true,
        reminderInterval: 24,
      },
    },
    {
      id: "TPL-004",
      name: "Employee Offboarding",
      description: "Asset collection and reassignment when employees leave",
      category: "offboarding",
      icon: UserMinus,
      color: "text-red-600",
      bgColor: "bg-red-100",
      isActive: true,
      isDefault: true,
      usageCount: 33,
      lastUsed: "2024-01-19",
      createdBy: "HR Manager",
      createdDate: "2023-12-01",
      transferType: "user",
      priority: "high",
      estimatedDuration: "1-2 days",
      approvalSteps: [
        { name: "HR Manager", required: true },
        { name: "IT Manager", required: true },
      ],
      assetCategories: ["All Categories"],
      defaultReason: "Asset collection due to employee departure",
      requiredFields: ["employee_id", "last_working_day", "return_location", "hr_approval"],
      autoAssignApprovers: true,
      notificationSettings: {
        sendToManager: true,
        sendToIT: true,
        sendToEmployee: false,
        reminderInterval: 12,
      },
    },
    {
      id: "TPL-005",
      name: "Maintenance Transfer",
      description: "Temporary transfer of assets for maintenance and repairs",
      category: "maintenance",
      icon: Wrench,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
      isActive: true,
      isDefault: false,
      usageCount: 67,
      lastUsed: "2024-01-21",
      createdBy: "Maintenance Team",
      createdDate: "2023-09-15",
      transferType: "location",
      priority: "normal",
      estimatedDuration: "1-3 days",
      approvalSteps: [
        { name: "Asset Owner", required: true },
        { name: "Maintenance Supervisor", required: true },
      ],
      assetCategories: ["Equipment", "Machinery", "Vehicles"],
      defaultReason: "Asset transfer for scheduled maintenance and repair",
      requiredFields: ["maintenance_type", "service_provider", "expected_return_date"],
      autoAssignApprovers: true,
      notificationSettings: {
        sendToManager: true,
        sendToIT: false,
        sendToEmployee: true,
        reminderInterval: 24,
      },
    },
    {
      id: "TPL-006",
      name: "Emergency Asset Transfer",
      description: "Urgent asset transfers for business continuity",
      category: "emergency",
      icon: AlertTriangle,
      color: "text-red-600",
      bgColor: "bg-red-100",
      isActive: true,
      isDefault: false,
      usageCount: 8,
      lastUsed: "2024-01-10",
      createdBy: "Operations Manager",
      createdDate: "2023-11-01",
      transferType: "both",
      priority: "high",
      estimatedDuration: "Same day",
      approvalSteps: [{ name: "Operations Manager", required: true }],
      assetCategories: ["All Categories"],
      defaultReason: "Emergency asset transfer for business continuity",
      requiredFields: ["emergency_type", "business_impact", "contact_person"],
      autoAssignApprovers: true,
      notificationSettings: {
        sendToManager: true,
        sendToIT: true,
        sendToEmployee: true,
        reminderInterval: 2,
      },
    },
  ])

  const stats = [
    {
      title: "Active Templates",
      value: "6",
      change: "+1",
      icon: FileTemplate,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Total Usage",
      value: "193",
      change: "+15%",
      icon: Users,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Most Used",
      value: "Maintenance",
      change: "67 uses",
      icon: Star,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      title: "Avg. Processing Time",
      value: "2.8 days",
      change: "-0.3",
      icon: Clock,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
  ]

  const categories = [
    { value: "all", label: "All Categories" },
    { value: "onboarding", label: "Employee Onboarding" },
    { value: "offboarding", label: "Employee Offboarding" },
    { value: "department_move", label: "Department Moves" },
    { value: "upgrade", label: "Equipment Upgrades" },
    { value: "maintenance", label: "Maintenance" },
    { value: "emergency", label: "Emergency" },
  ]

  const getCategoryColor = (category) => {
    switch (category) {
      case "onboarding":
        return "bg-green-100 text-green-800"
      case "offboarding":
        return "bg-red-100 text-red-800"
      case "department_move":
        return "bg-blue-100 text-blue-800"
      case "upgrade":
        return "bg-purple-100 text-purple-800"
      case "maintenance":
        return "bg-orange-100 text-orange-800"
      case "emergency":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority.toLowerCase()) {
      case "high":
        return "bg-red-100 text-red-800"
      case "normal":
        return "bg-blue-100 text-blue-800"
      case "low":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const filteredTemplates = templates.filter((template) => {
    const matchesSearch =
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = categoryFilter === "all" || template.category === categoryFilter
    return matchesSearch && matchesCategory
  })

  const handleNewTemplate = (e) => {
    e.preventDefault()
    // Handle template creation logic here
    setIsNewTemplateOpen(false)
  }

  const handleEditTemplate = (template) => {
    setSelectedTemplate(template)
    setIsEditTemplateOpen(true)
  }

  const handleDuplicateTemplate = (template) => {
    // Handle template duplication logic here
    console.log("Duplicating template:", template.id)
  }

  const handleDeleteTemplate = (template) => {
    // Handle template deletion logic here
    console.log("Deleting template:", template.id)
  }

  const handleUseTemplate = (template) => {
    // Redirect to transfer creation with template pre-filled
    window.location.href = `/dashboard/assets/transfers?template=${template.id}`
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Transfer Templates</h1>
            <p className="text-gray-600 mt-1">Manage predefined templates for common asset transfer scenarios</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" className="flex items-center bg-transparent">
              <Download className="h-4 w-4 mr-2" />
              Export Templates
            </Button>
            <Dialog open={isNewTemplateOpen} onOpenChange={setIsNewTemplateOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700">
                  <Plus className="h-4 w-4 mr-2" />
                  New Template
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Create Transfer Template</DialogTitle>
                  <DialogDescription>Create a reusable template for common transfer scenarios</DialogDescription>
                </DialogHeader>
                <form onSubmit={handleNewTemplate}>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="template-name">Template Name</Label>
                        <Input id="template-name" placeholder="Enter template name" required />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="template-category">Category</Label>
                        <Select required>
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="onboarding">Employee Onboarding</SelectItem>
                            <SelectItem value="offboarding">Employee Offboarding</SelectItem>
                            <SelectItem value="department_move">Department Moves</SelectItem>
                            <SelectItem value="upgrade">Equipment Upgrades</SelectItem>
                            <SelectItem value="maintenance">Maintenance</SelectItem>
                            <SelectItem value="emergency">Emergency</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="template-description">Description</Label>
                      <Textarea id="template-description" placeholder="Describe when to use this template" required />
                    </div>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="transfer-type">Transfer Type</Label>
                        <Select required>
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="user">User Transfer</SelectItem>
                            <SelectItem value="location">Location Transfer</SelectItem>
                            <SelectItem value="both">User & Location</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="priority">Default Priority</Label>
                        <Select required>
                          <SelectTrigger>
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="low">Low</SelectItem>
                            <SelectItem value="normal">Normal</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="duration">Estimated Duration</Label>
                        <Input id="duration" placeholder="e.g., 2-3 days" required />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="default-reason">Default Transfer Reason</Label>
                      <Textarea id="default-reason" placeholder="Standard reason for this type of transfer" required />
                    </div>
                    <div className="space-y-2">
                      <Label>Asset Categories (Select applicable categories)</Label>
                      <div className="grid grid-cols-3 gap-2">
                        {[
                          "Laptops",
                          "Desktops",
                          "Monitors",
                          "Keyboards",
                          "Mice",
                          "Printers",
                          "Servers",
                          "Network Equipment",
                          "Furniture",
                          "Vehicles",
                          "Machinery",
                          "All Categories",
                        ].map((category) => (
                          <div key={category} className="flex items-center space-x-2">
                            <input type="checkbox" id={category} className="rounded" />
                            <Label htmlFor={category} className="text-sm">
                              {category}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>Approval Workflow</Label>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2 p-3 border rounded-lg">
                          <input type="checkbox" id="manager-approval" className="rounded" />
                          <Label htmlFor="manager-approval" className="flex-1">
                            Direct Manager Approval
                          </Label>
                          <Badge variant="outline" className="text-xs">
                            Required
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-2 p-3 border rounded-lg">
                          <input type="checkbox" id="it-approval" className="rounded" />
                          <Label htmlFor="it-approval" className="flex-1">
                            IT Manager Approval
                          </Label>
                          <Badge variant="outline" className="text-xs">
                            Optional
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-2 p-3 border rounded-lg">
                          <input type="checkbox" id="asset-approval" className="rounded" />
                          <Label htmlFor="asset-approval" className="flex-1">
                            Asset Manager Approval
                          </Label>
                          <Badge variant="outline" className="text-xs">
                            Optional
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>Template Settings</Label>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <Label htmlFor="is-default">Set as Default Template</Label>
                            <p className="text-sm text-gray-500">Make this the default for its category</p>
                          </div>
                          <Switch id="is-default" />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label htmlFor="auto-assign">Auto-assign Approvers</Label>
                            <p className="text-sm text-gray-500">Automatically assign approvers based on org chart</p>
                          </div>
                          <Switch id="auto-assign" />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label htmlFor="is-active">Active Template</Label>
                            <p className="text-sm text-gray-500">Template is available for use</p>
                          </div>
                          <Switch id="is-active" defaultChecked />
                        </div>
                      </div>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setIsNewTemplateOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" className="bg-gradient-to-r from-indigo-600 to-purple-600">
                      <FileTemplate className="h-4 w-4 mr-2" />
                      Create Template
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className={`text-sm ${stat.change.startsWith("+") ? "text-green-600" : "text-red-600"}`}>
                      {stat.change} from last month
                    </p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content */}
        <Tabs defaultValue="templates" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="templates">All Templates</TabsTrigger>
            <TabsTrigger value="popular">Popular Templates</TabsTrigger>
            <TabsTrigger value="analytics">Template Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="templates" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Transfer Templates</CardTitle>
                    <CardDescription>Manage and organize your asset transfer templates</CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Filter className="h-4 w-4 mr-2" />
                      Filter
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search templates..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Filter by category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredTemplates.map((template) => (
                    <Card
                      key={template.id}
                      className="hover:shadow-lg transition-shadow border-l-4 border-l-indigo-500"
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center space-x-3">
                            <div className={`p-2 rounded-lg ${template.bgColor}`}>
                              <template.icon className={`h-5 w-5 ${template.color}`} />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h3 className="font-semibold text-gray-900 truncate">{template.name}</h3>
                              <div className="flex items-center space-x-2 mt-1">
                                <Badge className={getCategoryColor(template.category)} variant="outline">
                                  {template.category.replace("_", " ").toUpperCase()}
                                </Badge>
                                {template.isDefault && (
                                  <Badge className="bg-yellow-100 text-yellow-800" variant="outline">
                                    DEFAULT
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleUseTemplate(template)}>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Use Template
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleEditTemplate(template)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Template
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDuplicateTemplate(template)}>
                                <Copy className="mr-2 h-4 w-4" />
                                Duplicate
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-red-600" onClick={() => handleDeleteTemplate(template)}>
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <p className="text-sm text-gray-600 mb-4 line-clamp-2">{template.description}</p>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-500">Priority:</span>
                            <Badge className={getPriorityColor(template.priority)} variant="outline">
                              {template.priority.toUpperCase()}
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-500">Duration:</span>
                            <span className="font-medium">{template.estimatedDuration}</span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-500">Usage:</span>
                            <span className="font-medium">{template.usageCount} times</span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-500">Last used:</span>
                            <span className="font-medium">{template.lastUsed}</span>
                          </div>
                        </div>
                        <div className="mt-4 pt-4 border-t">
                          <div className="flex items-center justify-between">
                            <div className="text-xs text-gray-500">{template.approvalSteps.length} approval steps</div>
                            <Button
                              size="sm"
                              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700"
                              onClick={() => handleUseTemplate(template)}
                            >
                              Use Template
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="popular" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Star className="h-5 w-5 mr-2 text-yellow-600" />
                  Most Popular Templates
                </CardTitle>
                <CardDescription>Templates ranked by usage frequency and user ratings</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {templates
                    .sort((a, b) => b.usageCount - a.usageCount)
                    .slice(0, 5)
                    .map((template, index) => (
                      <div key={template.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                        <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-full font-bold">
                          {index + 1}
                        </div>
                        <div className={`p-2 rounded-lg ${template.bgColor}`}>
                          <template.icon className={`h-5 w-5 ${template.color}`} />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900">{template.name}</h3>
                          <p className="text-sm text-gray-600">{template.description}</p>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-lg text-indigo-600">{template.usageCount}</div>
                          <div className="text-sm text-gray-500">uses</div>
                        </div>
                        <Button size="sm" variant="outline" onClick={() => handleUseTemplate(template)}>
                          Use Template
                        </Button>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Template Usage by Category</CardTitle>
                  <CardDescription>Distribution of template usage across different categories</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {categories.slice(1).map((category) => {
                      const categoryTemplates = templates.filter((t) => t.category === category.value)
                      const totalUsage = categoryTemplates.reduce((sum, t) => sum + t.usageCount, 0)
                      const maxUsage = Math.max(...templates.map((t) => t.usageCount))
                      const percentage = (totalUsage / maxUsage) * 100

                      return (
                        <div key={category.value} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-gray-700">{category.label}</span>
                            <span className="text-sm text-gray-500">{totalUsage} uses</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-gradient-to-r from-indigo-600 to-purple-600 h-2 rounded-full"
                              style={{ width: `${percentage}%` }}
                            ></div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Template Performance</CardTitle>
                  <CardDescription>Average processing times and success rates</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">98.5%</div>
                        <div className="text-sm text-green-700">Success Rate</div>
                      </div>
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">2.8</div>
                        <div className="text-sm text-blue-700">Avg. Days</div>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Fastest Template</span>
                        <span className="text-sm font-medium">Emergency Transfer (Same day)</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Most Reliable</span>
                        <span className="text-sm font-medium">Employee Onboarding (99.2%)</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Most Complex</span>
                        <span className="text-sm font-medium">Department Relocation (5-7 days)</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Edit Template Dialog */}
        <Dialog open={isEditTemplateOpen} onOpenChange={setIsEditTemplateOpen}>
          <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Template</DialogTitle>
              <DialogDescription>Modify the template settings and configuration</DialogDescription>
            </DialogHeader>
            {selectedTemplate && (
              <form>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="edit-template-name">Template Name</Label>
                      <Input id="edit-template-name" defaultValue={selectedTemplate.name} required />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edit-template-category">Category</Label>
                      <Select defaultValue={selectedTemplate.category} required>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="onboarding">Employee Onboarding</SelectItem>
                          <SelectItem value="offboarding">Employee Offboarding</SelectItem>
                          <SelectItem value="department_move">Department Moves</SelectItem>
                          <SelectItem value="upgrade">Equipment Upgrades</SelectItem>
                          <SelectItem value="maintenance">Maintenance</SelectItem>
                          <SelectItem value="emergency">Emergency</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-template-description">Description</Label>
                    <Textarea id="edit-template-description" defaultValue={selectedTemplate.description} required />
                  </div>
                  <div className="space-y-2">
                    <Label>Template Settings</Label>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="edit-is-active">Active Template</Label>
                          <p className="text-sm text-gray-500">Template is available for use</p>
                        </div>
                        <Switch id="edit-is-active" defaultChecked={selectedTemplate.isActive} />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="edit-is-default">Set as Default Template</Label>
                          <p className="text-sm text-gray-500">Make this the default for its category</p>
                        </div>
                        <Switch id="edit-is-default" defaultChecked={selectedTemplate.isDefault} />
                      </div>
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setIsEditTemplateOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit" className="bg-gradient-to-r from-indigo-600 to-purple-600">
                    <Edit className="h-4 w-4 mr-2" />
                    Update Template
                  </Button>
                </DialogFooter>
              </form>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}
