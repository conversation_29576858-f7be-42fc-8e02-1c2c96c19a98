import { ROLES, PERMISSIONS } from "./permissions"

// Project Type Templates
export const PROJECT_TYPES = {
  SOFTWARE_DEVELOPMENT: "software_development",
  CONSTRUCTION: "construction",
  MARKETING: "marketing",
  RESEARCH: "research",
  CONSULTING: "consulting",
  MANUFACTURING: "manufacturing",
  FINANCE: "finance",
  HR: "hr",
}

// Role Combination Templates
export const ROLE_COMBINATIONS = {
  PROJECT_CORE_TEAM: "project_core_team",
  LEADERSHIP_TEAM: "leadership_team",
  TECHNICAL_TEAM: "technical_team",
  BUSINESS_TEAM: "business_team",
  APPROVAL_COMMITTEE: "approval_committee",
  EXECUTION_TEAM: "execution_team",
  STAKEHOLDER_GROUP: "stakeholder_group",
  AUDIT_TEAM: "audit_team",
}

// Project Type Permission Templates
export const PROJECT_TYPE_TEMPLATES = {
  [PROJECT_TYPES.SOFTWARE_DEVELOPMENT]: {
    name: "Software Development Project",
    description: "Permissions optimized for software development projects with agile workflows",
    icon: "Code",
    color: "blue",
    phases: {
      CREATE: {
        requiredRoles: [ROLES.BUSINESS_ANALYST, ROLES.PROJECT_MANAGER],
        optionalRoles: [ROLES.STAKEHOLDER, ROLES.TEAM_LEAD],
        permissions: [PERMISSIONS.CREATE_PROPOSE_IDEA, PERMISSIONS.CREATE_EDIT_PROPOSAL, PERMISSIONS.VIEW_PROJECT],
      },
      SELECT: {
        requiredRoles: [ROLES.PORTFOLIO_MANAGER, ROLES.PROJECT_MANAGER],
        optionalRoles: [ROLES.BUSINESS_ANALYST],
        permissions: [
          PERMISSIONS.SELECT_REQUEST_REVIEW,
          PERMISSIONS.SELECT_PORTFOLIO_SELECTION,
          PERMISSIONS.VIEW_PROJECT,
          PERMISSIONS.VIEW_REPORTS,
        ],
      },
      PLAN: {
        requiredRoles: [ROLES.PROJECT_MANAGER, ROLES.BUSINESS_ANALYST, ROLES.TEAM_LEAD],
        optionalRoles: [ROLES.DEVELOPER],
        permissions: [
          PERMISSIONS.PLAN_CREATE_BUSINESS_CASE,
          PERMISSIONS.PLAN_EDIT_BUSINESS_CASE,
          PERMISSIONS.VIEW_PROJECT,
          PERMISSIONS.EDIT_PROJECT,
          PERMISSIONS.MANAGE_TEAM,
        ],
      },
      MANAGE: {
        requiredRoles: [ROLES.PROJECT_MANAGER, ROLES.TEAM_LEAD, ROLES.DEVELOPER],
        optionalRoles: [ROLES.BUSINESS_ANALYST],
        permissions: [
          PERMISSIONS.MANAGE_START_PROJECT,
          PERMISSIONS.MANAGE_ASSIGN_TEAM,
          PERMISSIONS.MANAGE_COMPLETE_PROJECT,
          PERMISSIONS.VIEW_PROJECT,
          PERMISSIONS.EDIT_PROJECT,
          PERMISSIONS.MANAGE_TEAM,
          PERMISSIONS.VIEW_REPORTS,
        ],
      },
    },
    customSettings: {
      allowDeveloperApproval: false,
      requireTechnicalReview: true,
      enableAgileWorkflow: true,
      requireCodeReview: true,
    },
  },

  [PROJECT_TYPES.CONSTRUCTION]: {
    name: "Construction Project",
    description: "Permissions for construction projects with safety and compliance focus",
    icon: "HardHat",
    color: "orange",
    phases: {
      CREATE: {
        requiredRoles: [ROLES.PROJECT_MANAGER, ROLES.BUSINESS_ANALYST],
        optionalRoles: [ROLES.STAKEHOLDER],
        permissions: [
          PERMISSIONS.CREATE_PROPOSE_IDEA,
          PERMISSIONS.CREATE_EDIT_PROPOSAL,
          PERMISSIONS.VIEW_PROJECT,
          PERMISSIONS.VIEW_FINANCIALS,
        ],
      },
      SELECT: {
        requiredRoles: [ROLES.PORTFOLIO_MANAGER, ROLES.PROJECT_MANAGER],
        optionalRoles: [ROLES.BUSINESS_ANALYST],
        permissions: [
          PERMISSIONS.SELECT_REQUEST_REVIEW,
          PERMISSIONS.SELECT_PORTFOLIO_SELECTION,
          PERMISSIONS.VIEW_PROJECT,
          PERMISSIONS.VIEW_FINANCIALS,
          PERMISSIONS.VIEW_REPORTS,
        ],
      },
      PLAN: {
        requiredRoles: [ROLES.PROJECT_MANAGER, ROLES.BUSINESS_ANALYST],
        optionalRoles: [ROLES.TEAM_LEAD],
        permissions: [
          PERMISSIONS.PLAN_CREATE_BUSINESS_CASE,
          PERMISSIONS.PLAN_EDIT_BUSINESS_CASE,
          PERMISSIONS.VIEW_PROJECT,
          PERMISSIONS.EDIT_PROJECT,
          PERMISSIONS.VIEW_FINANCIALS,
          PERMISSIONS.EDIT_FINANCIALS,
          PERMISSIONS.MANAGE_TEAM,
        ],
      },
      MANAGE: {
        requiredRoles: [ROLES.PROJECT_MANAGER, ROLES.TEAM_LEAD],
        optionalRoles: [ROLES.DEVELOPER],
        permissions: [
          PERMISSIONS.MANAGE_START_PROJECT,
          PERMISSIONS.MANAGE_ASSIGN_TEAM,
          PERMISSIONS.MANAGE_SUSPEND_PROJECT,
          PERMISSIONS.MANAGE_COMPLETE_PROJECT,
          PERMISSIONS.VIEW_PROJECT,
          PERMISSIONS.EDIT_PROJECT,
          PERMISSIONS.VIEW_FINANCIALS,
          PERMISSIONS.MANAGE_TEAM,
          PERMISSIONS.VIEW_REPORTS,
        ],
      },
    },
    customSettings: {
      requireSafetyApproval: true,
      enableComplianceTracking: true,
      requireBudgetApproval: true,
      enableResourceTracking: true,
    },
  },

  [PROJECT_TYPES.MARKETING]: {
    name: "Marketing Campaign",
    description: "Permissions for marketing campaigns with creative and approval workflows",
    icon: "Megaphone",
    color: "pink",
    phases: {
      CREATE: {
        requiredRoles: [ROLES.BUSINESS_ANALYST, ROLES.STAKEHOLDER],
        optionalRoles: [ROLES.PROJECT_MANAGER],
        permissions: [PERMISSIONS.CREATE_PROPOSE_IDEA, PERMISSIONS.CREATE_EDIT_PROPOSAL, PERMISSIONS.VIEW_PROJECT],
      },
      SELECT: {
        requiredRoles: [ROLES.PORTFOLIO_MANAGER, ROLES.BUSINESS_ANALYST],
        optionalRoles: [ROLES.STAKEHOLDER],
        permissions: [
          PERMISSIONS.SELECT_REQUEST_REVIEW,
          PERMISSIONS.SELECT_PORTFOLIO_SELECTION,
          PERMISSIONS.VIEW_PROJECT,
          PERMISSIONS.VIEW_FINANCIALS,
        ],
      },
      PLAN: {
        requiredRoles: [ROLES.PROJECT_MANAGER, ROLES.BUSINESS_ANALYST],
        optionalRoles: [ROLES.TEAM_LEAD],
        permissions: [
          PERMISSIONS.PLAN_CREATE_BUSINESS_CASE,
          PERMISSIONS.PLAN_EDIT_BUSINESS_CASE,
          PERMISSIONS.VIEW_PROJECT,
          PERMISSIONS.EDIT_PROJECT,
          PERMISSIONS.VIEW_FINANCIALS,
        ],
      },
      MANAGE: {
        requiredRoles: [ROLES.PROJECT_MANAGER, ROLES.TEAM_LEAD],
        optionalRoles: [ROLES.DEVELOPER],
        permissions: [
          PERMISSIONS.MANAGE_START_PROJECT,
          PERMISSIONS.MANAGE_ASSIGN_TEAM,
          PERMISSIONS.MANAGE_COMPLETE_PROJECT,
          PERMISSIONS.VIEW_PROJECT,
          PERMISSIONS.EDIT_PROJECT,
          PERMISSIONS.MANAGE_TEAM,
          PERMISSIONS.VIEW_REPORTS,
        ],
      },
    },
    customSettings: {
      requireCreativeApproval: true,
      enableBrandGuidelines: true,
      requireLegalReview: true,
      enableCampaignTracking: true,
    },
  },

  [PROJECT_TYPES.RESEARCH]: {
    name: "Research Project",
    description: "Permissions for research projects with academic and scientific workflows",
    icon: "Search",
    color: "green",
    phases: {
      CREATE: {
        requiredRoles: [ROLES.BUSINESS_ANALYST, ROLES.STAKEHOLDER],
        optionalRoles: [ROLES.PROJECT_MANAGER],
        permissions: [
          PERMISSIONS.CREATE_PROPOSE_IDEA,
          PERMISSIONS.CREATE_EDIT_PROPOSAL,
          PERMISSIONS.VIEW_PROJECT,
          PERMISSIONS.VIEW_REPORTS,
        ],
      },
      SELECT: {
        requiredRoles: [ROLES.PORTFOLIO_MANAGER, ROLES.BUSINESS_ANALYST],
        optionalRoles: [ROLES.STAKEHOLDER],
        permissions: [
          PERMISSIONS.SELECT_REQUEST_REVIEW,
          PERMISSIONS.SELECT_PORTFOLIO_SELECTION,
          PERMISSIONS.VIEW_PROJECT,
          PERMISSIONS.VIEW_REPORTS,
        ],
      },
      PLAN: {
        requiredRoles: [ROLES.PROJECT_MANAGER, ROLES.BUSINESS_ANALYST],
        optionalRoles: [ROLES.TEAM_LEAD],
        permissions: [
          PERMISSIONS.PLAN_CREATE_BUSINESS_CASE,
          PERMISSIONS.PLAN_EDIT_BUSINESS_CASE,
          PERMISSIONS.VIEW_PROJECT,
          PERMISSIONS.EDIT_PROJECT,
          PERMISSIONS.VIEW_REPORTS,
        ],
      },
      MANAGE: {
        requiredRoles: [ROLES.PROJECT_MANAGER, ROLES.BUSINESS_ANALYST],
        optionalRoles: [ROLES.TEAM_LEAD, ROLES.DEVELOPER],
        permissions: [
          PERMISSIONS.MANAGE_START_PROJECT,
          PERMISSIONS.MANAGE_ASSIGN_TEAM,
          PERMISSIONS.MANAGE_COMPLETE_PROJECT,
          PERMISSIONS.VIEW_PROJECT,
          PERMISSIONS.EDIT_PROJECT,
          PERMISSIONS.MANAGE_TEAM,
          PERMISSIONS.VIEW_REPORTS,
        ],
      },
    },
    customSettings: {
      requireEthicsApproval: true,
      enableDataProtection: true,
      requirePeerReview: true,
      enablePublicationTracking: true,
    },
  },
}

// Role Combination Templates
export const ROLE_COMBINATION_TEMPLATES = {
  [ROLE_COMBINATIONS.PROJECT_CORE_TEAM]: {
    name: "Project Core Team",
    description: "Essential team members for project execution",
    icon: "Users",
    color: "blue",
    roles: [ROLES.PROJECT_MANAGER, ROLES.TEAM_LEAD, ROLES.BUSINESS_ANALYST],
    permissions: [
      PERMISSIONS.VIEW_PROJECT,
      PERMISSIONS.EDIT_PROJECT,
      PERMISSIONS.MANAGE_TEAM,
      PERMISSIONS.VIEW_REPORTS,
      PERMISSIONS.PLAN_CREATE_BUSINESS_CASE,
      PERMISSIONS.PLAN_EDIT_BUSINESS_CASE,
      PERMISSIONS.MANAGE_START_PROJECT,
      PERMISSIONS.MANAGE_ASSIGN_TEAM,
    ],
    applicablePhases: ["CREATE", "SELECT", "PLAN", "MANAGE"],
  },

  [ROLE_COMBINATIONS.LEADERSHIP_TEAM]: {
    name: "Leadership Team",
    description: "Senior leadership with approval and oversight responsibilities",
    icon: "Crown",
    color: "purple",
    roles: [ROLES.ADMIN, ROLES.PORTFOLIO_MANAGER],
    permissions: [
      PERMISSIONS.CREATE_REVIEW_IDEA,
      PERMISSIONS.CREATE_APPROVE_REQUEST,
      PERMISSIONS.SELECT_PORTFOLIO_SELECTION,
      PERMISSIONS.SELECT_COMPLETE_SELECTION,
      PERMISSIONS.PLAN_REVIEW_BUSINESS_CASE,
      PERMISSIONS.PLAN_APPROVE_BUSINESS_CASE,
      PERMISSIONS.PLAN_DENY_BUSINESS_CASE,
      PERMISSIONS.VIEW_PROJECT,
      PERMISSIONS.VIEW_FINANCIALS,
      PERMISSIONS.VIEW_REPORTS,
    ],
    applicablePhases: ["CREATE", "SELECT", "PLAN"],
  },

  [ROLE_COMBINATIONS.TECHNICAL_TEAM]: {
    name: "Technical Team",
    description: "Technical contributors and implementers",
    icon: "Code",
    color: "green",
    roles: [ROLES.TEAM_LEAD, ROLES.DEVELOPER],
    permissions: [
      PERMISSIONS.VIEW_PROJECT,
      PERMISSIONS.MANAGE_START_PROJECT,
      PERMISSIONS.MANAGE_ASSIGN_TEAM,
      PERMISSIONS.VIEW_REPORTS,
    ],
    applicablePhases: ["PLAN", "MANAGE"],
  },

  [ROLE_COMBINATIONS.BUSINESS_TEAM]: {
    name: "Business Team",
    description: "Business analysts and stakeholders",
    icon: "Briefcase",
    color: "orange",
    roles: [ROLES.BUSINESS_ANALYST, ROLES.STAKEHOLDER],
    permissions: [
      PERMISSIONS.CREATE_PROPOSE_IDEA,
      PERMISSIONS.CREATE_EDIT_PROPOSAL,
      PERMISSIONS.PLAN_CREATE_BUSINESS_CASE,
      PERMISSIONS.PLAN_EDIT_BUSINESS_CASE,
      PERMISSIONS.VIEW_PROJECT,
      PERMISSIONS.VIEW_REPORTS,
    ],
    applicablePhases: ["CREATE", "PLAN"],
  },

  [ROLE_COMBINATIONS.APPROVAL_COMMITTEE]: {
    name: "Approval Committee",
    description: "Decision makers for project approvals",
    icon: "CheckCircle",
    color: "red",
    roles: [ROLES.ADMIN, ROLES.PORTFOLIO_MANAGER, ROLES.PROJECT_MANAGER],
    permissions: [
      PERMISSIONS.CREATE_REVIEW_IDEA,
      PERMISSIONS.CREATE_APPROVE_REQUEST,
      PERMISSIONS.SELECT_PORTFOLIO_SELECTION,
      PERMISSIONS.SELECT_COMPLETE_SELECTION,
      PERMISSIONS.PLAN_REVIEW_BUSINESS_CASE,
      PERMISSIONS.PLAN_APPROVE_BUSINESS_CASE,
      PERMISSIONS.PLAN_DENY_BUSINESS_CASE,
      PERMISSIONS.VIEW_PROJECT,
      PERMISSIONS.VIEW_FINANCIALS,
    ],
    applicablePhases: ["CREATE", "SELECT", "PLAN"],
  },

  [ROLE_COMBINATIONS.EXECUTION_TEAM]: {
    name: "Execution Team",
    description: "Team responsible for project delivery",
    icon: "Play",
    color: "teal",
    roles: [ROLES.PROJECT_MANAGER, ROLES.TEAM_LEAD, ROLES.DEVELOPER],
    permissions: [
      PERMISSIONS.MANAGE_START_PROJECT,
      PERMISSIONS.MANAGE_ASSIGN_TEAM,
      PERMISSIONS.MANAGE_SUSPEND_PROJECT,
      PERMISSIONS.MANAGE_COMPLETE_PROJECT,
      PERMISSIONS.VIEW_PROJECT,
      PERMISSIONS.EDIT_PROJECT,
      PERMISSIONS.MANAGE_TEAM,
      PERMISSIONS.VIEW_REPORTS,
    ],
    applicablePhases: ["MANAGE"],
  },

  [ROLE_COMBINATIONS.STAKEHOLDER_GROUP]: {
    name: "Stakeholder Group",
    description: "Project stakeholders and viewers",
    icon: "Eye",
    color: "gray",
    roles: [ROLES.STAKEHOLDER, ROLES.VIEWER],
    permissions: [PERMISSIONS.VIEW_PROJECT, PERMISSIONS.VIEW_REPORTS, PERMISSIONS.CREATE_PROPOSE_IDEA],
    applicablePhases: ["CREATE", "SELECT", "PLAN", "MANAGE"],
  },

  [ROLE_COMBINATIONS.AUDIT_TEAM]: {
    name: "Audit Team",
    description: "Auditors and compliance reviewers",
    icon: "Shield",
    color: "yellow",
    roles: [ROLES.ADMIN, ROLES.VIEWER],
    permissions: [
      PERMISSIONS.VIEW_PROJECT,
      PERMISSIONS.VIEW_FINANCIALS,
      PERMISSIONS.VIEW_REPORTS,
      PERMISSIONS.PLAN_REVIEW_BUSINESS_CASE,
    ],
    applicablePhases: ["SELECT", "PLAN", "MANAGE"],
  },
}

// Template Application Functions
export const applyProjectTypeTemplate = (projectType, projectId) => {
  const template = PROJECT_TYPE_TEMPLATES[projectType]
  if (!template) return null

  return {
    projectId,
    projectType,
    templateName: template.name,
    appliedAt: new Date().toISOString(),
    phases: template.phases,
    customSettings: template.customSettings,
  }
}

export const applyRoleCombinationTemplate = (combination, userIds, projectId) => {
  const template = ROLE_COMBINATION_TEMPLATES[combination]
  if (!template) return null

  return {
    projectId,
    combination,
    templateName: template.name,
    userIds,
    roles: template.roles,
    permissions: template.permissions,
    applicablePhases: template.applicablePhases,
    appliedAt: new Date().toISOString(),
  }
}

// Template Validation
export const validateTemplate = (template, projectPhase) => {
  const errors = []

  if (!template.applicablePhases.includes(projectPhase)) {
    errors.push(`Template not applicable for phase: ${projectPhase}`)
  }

  if (template.roles.length === 0) {
    errors.push("Template must include at least one role")
  }

  if (template.permissions.length === 0) {
    errors.push("Template must include at least one permission")
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

// Template Recommendations
export const getRecommendedTemplates = (projectType, currentPhase, teamSize) => {
  const recommendations = []

  // Project type specific recommendations
  if (PROJECT_TYPE_TEMPLATES[projectType]) {
    recommendations.push({
      type: "project_type",
      template: PROJECT_TYPE_TEMPLATES[projectType],
      reason: "Optimized for your project type",
      priority: "high",
    })
  }

  // Phase specific recommendations
  const phaseRecommendations = Object.entries(ROLE_COMBINATION_TEMPLATES)
    .filter(([_, template]) => template.applicablePhases.includes(currentPhase))
    .map(([key, template]) => ({
      type: "role_combination",
      key,
      template,
      reason: `Suitable for ${currentPhase} phase`,
      priority: "medium",
    }))

  recommendations.push(...phaseRecommendations)

  // Team size recommendations
  if (teamSize <= 5) {
    recommendations.push({
      type: "role_combination",
      key: ROLE_COMBINATIONS.PROJECT_CORE_TEAM,
      template: ROLE_COMBINATION_TEMPLATES[ROLE_COMBINATIONS.PROJECT_CORE_TEAM],
      reason: "Perfect for small teams",
      priority: "high",
    })
  }

  return recommendations.sort((a, b) => {
    const priorityOrder = { high: 3, medium: 2, low: 1 }
    return priorityOrder[b.priority] - priorityOrder[a.priority]
  })
}

// Helper functions
export const getProjectTypeDisplayName = (projectType) => {
  return PROJECT_TYPE_TEMPLATES[projectType]?.name || projectType
}

export const getRoleCombinationDisplayName = (combination) => {
  return ROLE_COMBINATION_TEMPLATES[combination]?.name || combination
}

export const getTemplateIcon = (template) => {
  return template.icon || "Settings"
}

export const getTemplateColor = (template) => {
  return template.color || "gray"
}
