"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Globe,
  MapPin,
  Building,
  Users,
  Clock,
  DollarSign,
  FileText,
  Settings,
  Map,
  Layers,
  UserCheck,
  Timer,
  Receipt,
  CreditCard,
  Landmark,
  Shield,
} from "lucide-react"
import CountryManagement from "@/components/master-data/country-management"

const masterDataModules = [
  {
    id: "countries",
    name: "Countries",
    description: "Manage countries and their details",
    icon: Globe,
    count: 195,
    color: "bg-blue-500",
  },
  {
    id: "states",
    name: "States/Provinces",
    description: "Manage states and provinces",
    icon: Map,
    count: 1250,
    color: "bg-green-500",
  },
  {
    id: "cities",
    name: "Cities",
    description: "Manage cities and localities",
    icon: MapPin,
    count: 5420,
    color: "bg-purple-500",
  },
  {
    id: "locations",
    name: "Locations",
    description: "Manage office and site locations",
    icon: Building,
    count: 45,
    color: "bg-orange-500",
  },
  {
    id: "zones",
    name: "Zones/Regions",
    description: "Manage geographical zones",
    icon: Layers,
    count: 12,
    color: "bg-teal-500",
  },
  {
    id: "floors",
    name: "Floors",
    description: "Manage building floors",
    icon: Layers,
    count: 156,
    color: "bg-indigo-500",
  },
  {
    id: "departments",
    name: "Departments",
    description: "Manage organizational departments",
    icon: Users,
    count: 25,
    color: "bg-red-500",
  },
  {
    id: "designations",
    name: "Designations",
    description: "Manage job positions and roles",
    icon: UserCheck,
    count: 78,
    color: "bg-yellow-500",
  },
  {
    id: "shifts",
    name: "Shifts",
    description: "Manage work shifts and schedules",
    icon: Clock,
    count: 8,
    color: "bg-pink-500",
  },
  {
    id: "currencies",
    name: "Currencies",
    description: "Manage currencies and exchange rates",
    icon: DollarSign,
    count: 168,
    color: "bg-emerald-500",
  },
  {
    id: "timezones",
    name: "Timezones",
    description: "Manage timezone configurations",
    icon: Timer,
    count: 24,
    color: "bg-cyan-500",
  },
  {
    id: "tax-categories",
    name: "Tax Categories",
    description: "Manage tax types and rates",
    icon: Receipt,
    count: 15,
    color: "bg-lime-500",
  },
  {
    id: "uoms",
    name: "Units of Measurement",
    description: "Manage measurement units",
    icon: Settings,
    count: 42,
    color: "bg-violet-500",
  },
  {
    id: "payment-terms",
    name: "Payment Terms",
    description: "Manage payment terms and conditions",
    icon: CreditCard,
    count: 18,
    color: "bg-rose-500",
  },
  {
    id: "bank-details",
    name: "Bank Details",
    description: "Manage bank account information",
    icon: Landmark,
    count: 8,
    color: "bg-amber-500",
  },
  {
    id: "document-types",
    name: "Document Types",
    description: "Manage document categories",
    icon: FileText,
    count: 32,
    color: "bg-slate-500",
  },
  {
    id: "user-roles",
    name: "User Roles & Permissions",
    description: "Manage user access control",
    icon: Shield,
    count: 12,
    color: "bg-gray-500",
  },
]

export default function MasterDataPage() {
  const [selectedModule, setSelectedModule] = useState(null)

  if (selectedModule === "countries") {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => setSelectedModule(null)}>
            ← Back to Master Data
          </Button>
        </div>
        <CountryManagement />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Master Data Management</h1>
          <p className="text-gray-600">Configure and manage all master data entities</p>
        </div>
        <Badge variant="outline" className="text-sm">
          {masterDataModules.length} Modules
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {masterDataModules.map((module) => {
          const IconComponent = module.icon
          return (
            <Card
              key={module.id}
              className="cursor-pointer hover:shadow-lg transition-shadow duration-200 border-l-4"
              style={{ borderLeftColor: module.color.replace("bg-", "#") }}
              onClick={() => setSelectedModule(module.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className={`p-2 rounded-lg ${module.color} text-white`}>
                    <IconComponent className="h-5 w-5" />
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {module.count}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <CardTitle className="text-lg mb-2">{module.name}</CardTitle>
                <p className="text-sm text-gray-600">{module.description}</p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Master Data Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">
                {masterDataModules.reduce((sum, module) => sum + module.count, 0).toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Total Records</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">{masterDataModules.length}</div>
              <div className="text-sm text-gray-600">Active Modules</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">100%</div>
              <div className="text-sm text-gray-600">Data Integrity</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
