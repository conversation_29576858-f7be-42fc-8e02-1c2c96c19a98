"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  ArrowLeft,
  Users,
  Clock,
  DollarSign,
  Calendar,
  Plus,
  MessageSquare,
  Paperclip,
  Play,
  Square,
  Edit,
  Settings,
  BarChart3,
  Target,
  FileText,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import Link from "next/link"

export default function ProjectDetailPage({ params }) {
  const [isAddTaskOpen, setIsAddTaskOpen] = useState(false)
  const [isAddCommentOpen, setIsAddCommentOpen] = useState(false)
  const [activeTimer, setActiveTimer] = useState(null)

  // Mock project data - in real app, fetch based on params.id
  const project = {
    id: "PRJ001",
    name: "E-commerce Platform Redesign",
    description:
      "Complete overhaul of the customer-facing e-commerce platform with modern UI/UX and improved performance",
    status: "In Progress",
    priority: "High",
    progress: 68,
    startDate: "2024-01-15",
    endDate: "2024-03-30",
    budget: 85000,
    spent: 42500,
    hoursLogged: 324,
    hoursEstimated: 480,
    manager: "Sarah Wilson",
    client: "TechCorp Solutions",
    team: [
      { id: 1, name: "John Doe", avatar: "/placeholder.svg?height=40&width=40", role: "Frontend Developer", hours: 89 },
      { id: 2, name: "Lisa Chen", avatar: "/placeholder.svg?height=40&width=40", role: "UI/UX Designer", hours: 76 },
      {
        id: 3,
        name: "Mike Johnson",
        avatar: "/placeholder.svg?height=40&width=40",
        role: "Backend Developer",
        hours: 92,
      },
      { id: 4, name: "Emma Davis", avatar: "/placeholder.svg?height=40&width=40", role: "QA Engineer", hours: 67 },
    ],
  }

  const tasks = [
    {
      id: "TSK001",
      title: "Implement user authentication",
      description: "Set up secure login system with JWT tokens and password encryption",
      status: "In Progress",
      priority: "High",
      assignee: "John Doe",
      dueDate: "2024-01-25",
      progress: 75,
      timeLogged: "12h 30m",
      comments: 5,
    },
    {
      id: "TSK002",
      title: "Design product catalog layout",
      description: "Create responsive grid layout for product listings with filters",
      status: "Completed",
      priority: "Medium",
      assignee: "Lisa Chen",
      dueDate: "2024-01-20",
      progress: 100,
      timeLogged: "8h 45m",
      comments: 3,
    },
    {
      id: "TSK003",
      title: "Shopping cart functionality",
      description: "Implement add to cart, quantity updates, and checkout flow",
      status: "In Progress",
      priority: "High",
      assignee: "Mike Johnson",
      dueDate: "2024-01-28",
      progress: 65,
      timeLogged: "15h 20m",
      comments: 8,
    },
    {
      id: "TSK004",
      title: "Payment gateway integration",
      description: "Stripe payment processing implementation with error handling",
      status: "Review",
      priority: "Critical",
      assignee: "Emma Davis",
      dueDate: "2024-01-30",
      progress: 90,
      timeLogged: "10h 15m",
      comments: 2,
    },
  ]

  const comments = [
    {
      id: 1,
      user: "Sarah Wilson",
      avatar: "/placeholder.svg?height=32&width=32",
      content: "Great progress on the authentication module! The JWT implementation looks solid.",
      timestamp: "2 hours ago",
      type: "comment",
    },
    {
      id: 2,
      user: "John Doe",
      avatar: "/placeholder.svg?height=32&width=32",
      content: "Thanks! I've also added password strength validation and rate limiting for login attempts.",
      timestamp: "1 hour ago",
      type: "comment",
    },
    {
      id: 3,
      user: "Lisa Chen",
      avatar: "/placeholder.svg?height=32&width=32",
      content: "The new product catalog design is ready for review. I've uploaded the mockups to the shared folder.",
      timestamp: "3 hours ago",
      type: "update",
    },
    {
      id: 4,
      user: "Mike Johnson",
      avatar: "/placeholder.svg?height=32&width=32",
      content: "Working on the shopping cart API endpoints. Should have the basic CRUD operations done by EOD.",
      timestamp: "4 hours ago",
      type: "status",
    },
  ]

  const milestones = [
    {
      id: 1,
      title: "Project Kickoff",
      date: "2024-01-15",
      status: "Completed",
      description: "Initial project setup and team onboarding",
    },
    {
      id: 2,
      title: "Design Phase Complete",
      date: "2024-01-30",
      status: "In Progress",
      description: "All UI/UX designs approved and ready for development",
    },
    {
      id: 3,
      title: "Core Features MVP",
      date: "2024-02-15",
      status: "Upcoming",
      description: "Basic e-commerce functionality implemented",
    },
    {
      id: 4,
      title: "Testing & QA",
      date: "2024-03-01",
      status: "Upcoming",
      description: "Comprehensive testing and bug fixes",
    },
    {
      id: 5,
      title: "Production Launch",
      date: "2024-03-30",
      status: "Upcoming",
      description: "Final deployment and go-live",
    },
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case "To Do":
        return "bg-gray-100 text-gray-800"
      case "In Progress":
        return "bg-blue-100 text-blue-800"
      case "Review":
        return "bg-yellow-100 text-yellow-800"
      case "Completed":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "Critical":
        return "bg-red-100 text-red-800"
      case "High":
        return "bg-orange-100 text-orange-800"
      case "Medium":
        return "bg-yellow-100 text-yellow-800"
      case "Low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getMilestoneColor = (status) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-800"
      case "In Progress":
        return "bg-blue-100 text-blue-800"
      case "Upcoming":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const startTimer = (taskId) => {
    setActiveTimer(taskId)
  }

  const stopTimer = () => {
    setActiveTimer(null)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/projects">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Projects
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{project.name}</h1>
              <p className="text-gray-600 mt-1">Detailed project overview and task management</p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              Edit Project
            </Button>
            <Dialog open={isAddTaskOpen} onOpenChange={setIsAddTaskOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Task
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[525px]">
                <DialogHeader>
                  <DialogTitle>Create New Task</DialogTitle>
                  <DialogDescription>Add a new task to the project with details and assignments.</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="taskTitle" className="text-right">
                      Title
                    </Label>
                    <Input id="taskTitle" placeholder="Task title" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-start gap-4">
                    <Label htmlFor="taskDescription" className="text-right mt-2">
                      Description
                    </Label>
                    <Textarea id="taskDescription" placeholder="Task description..." className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="assignee" className="text-right">
                      Assignee
                    </Label>
                    <Select>
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select team member" />
                      </SelectTrigger>
                      <SelectContent>
                        {project.team.map((member) => (
                          <SelectItem key={member.id} value={member.name}>
                            {member.name} - {member.role}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="priority" className="text-right">
                      Priority
                    </Label>
                    <Select>
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select priority" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="critical">Critical</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="dueDate" className="text-right">
                      Due Date
                    </Label>
                    <Input id="dueDate" type="date" className="col-span-3" />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" className="bg-gradient-to-r from-purple-600 to-blue-600">
                    Create Task
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Project Overview Card */}
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <div className="flex items-center space-x-3 mb-4">
                  <Badge className={getStatusColor(project.status)}>{project.status}</Badge>
                  <Badge className={getPriorityColor(project.priority)}>{project.priority}</Badge>
                  <span className="text-sm text-gray-500">Managed by {project.manager}</span>
                </div>
                <p className="text-gray-600 mb-4">{project.description}</p>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Start Date</span>
                    <div className="font-semibold">{formatDate(project.startDate)}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">End Date</span>
                    <div className="font-semibold">{formatDate(project.endDate)}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Budget</span>
                    <div className="font-semibold">{formatCurrency(project.budget)}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Client</span>
                    <div className="font-semibold">{project.client}</div>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">Progress</span>
                    <span className="text-sm font-medium">{project.progress}%</span>
                  </div>
                  <Progress value={project.progress} className="h-3" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">Budget Used</span>
                    <span className="text-sm font-medium">{Math.round((project.spent / project.budget) * 100)}%</span>
                  </div>
                  <Progress value={(project.spent / project.budget) * 100} className="h-3" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">Time Progress</span>
                    <span className="text-sm font-medium">
                      {Math.round((project.hoursLogged / project.hoursEstimated) * 100)}%
                    </span>
                  </div>
                  <Progress value={(project.hoursLogged / project.hoursEstimated) * 100} className="h-3" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Team Size</p>
                  <p className="text-2xl font-bold text-gray-900">{project.team.length}</p>
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Hours Logged</p>
                  <p className="text-2xl font-bold text-gray-900">{project.hoursLogged}h</p>
                </div>
                <Clock className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Budget Spent</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(project.spent)}</p>
                </div>
                <DollarSign className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Days Left</p>
                  <p className="text-2xl font-bold text-gray-900">45</p>
                </div>
                <Calendar className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="tasks" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="tasks">Tasks</TabsTrigger>
            <TabsTrigger value="team">Team</TabsTrigger>
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
            <TabsTrigger value="files">Files</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
          </TabsList>

          {/* Tasks Tab */}
          <TabsContent value="tasks" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Project Tasks</CardTitle>
                <CardDescription>Track and manage all project tasks</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {tasks.map((task) => (
                    <Card key={task.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h4 className="font-semibold text-gray-900">{task.title}</h4>
                              <Badge className={getStatusColor(task.status)}>{task.status}</Badge>
                              <Badge className={getPriorityColor(task.priority)}>{task.priority}</Badge>
                            </div>
                            <p className="text-sm text-gray-600 mb-3">{task.description}</p>
                            <div className="flex items-center space-x-6 text-sm text-gray-500">
                              <span>Assigned to: {task.assignee}</span>
                              <span>Due: {formatDate(task.dueDate)}</span>
                              <span>Time: {task.timeLogged}</span>
                              <div className="flex items-center space-x-1">
                                <MessageSquare className="h-3 w-3" />
                                <span>{task.comments}</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            {activeTimer === task.id ? (
                              <Button size="sm" variant="outline" onClick={stopTimer}>
                                <Square className="h-3 w-3 mr-1" />
                                Stop
                              </Button>
                            ) : (
                              <Button size="sm" variant="outline" onClick={() => startTimer(task.id)}>
                                <Play className="h-3 w-3 mr-1" />
                                Start
                              </Button>
                            )}
                            <Button size="sm" variant="outline">
                              <Edit className="h-3 w-3 mr-1" />
                              Edit
                            </Button>
                          </div>
                        </div>
                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm text-gray-600">Progress</span>
                            <span className="text-sm font-medium">{task.progress}%</span>
                          </div>
                          <Progress value={task.progress} className="h-2" />
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Team Tab */}
          <TabsContent value="team" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Team Members</CardTitle>
                <CardDescription>Project team and their contributions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {project.team.map((member) => (
                    <Card key={member.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-4">
                          <Avatar className="h-12 w-12">
                            <AvatarImage src={member.avatar || "/placeholder.svg"} alt={member.name} />
                            <AvatarFallback>
                              {member.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900">{member.name}</h4>
                            <p className="text-sm text-gray-600">{member.role}</p>
                            <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                              <div className="flex items-center space-x-1">
                                <Clock className="h-3 w-3" />
                                <span>{member.hours}h logged</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Target className="h-3 w-3" />
                                <span>3 tasks active</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Timeline Tab */}
          <TabsContent value="timeline" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Project Milestones</CardTitle>
                <CardDescription>Key project milestones and deadlines</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {milestones.map((milestone) => (
                    <div key={milestone.id} className="flex items-start space-x-4 p-4 border rounded-lg">
                      <div className="p-2 bg-purple-100 rounded-lg">
                        <Target className="h-5 w-5 text-purple-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="font-semibold text-gray-900">{milestone.title}</h4>
                          <Badge className={getMilestoneColor(milestone.status)}>{milestone.status}</Badge>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{milestone.description}</p>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Calendar className="h-3 w-3" />
                          <span>{formatDate(milestone.date)}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Files Tab */}
          <TabsContent value="files" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Project Files</CardTitle>
                    <CardDescription>Documents, designs, and project assets</CardDescription>
                  </div>
                  <Button variant="outline">
                    <Paperclip className="h-4 w-4 mr-2" />
                    Upload File
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-gray-500">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No files uploaded yet</p>
                  <p className="text-sm">Upload project documents, designs, and other files</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Activity Tab */}
          <TabsContent value="activity" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Project Activity</CardTitle>
                    <CardDescription>Recent updates and team communications</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {comments.map((comment) => (
                        <div key={comment.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={comment.avatar || "/placeholder.svg"} alt={comment.user} />
                            <AvatarFallback>
                              {comment.user
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className="font-medium text-sm">{comment.user}</span>
                              <Badge variant="outline" className="text-xs">
                                {comment.type}
                              </Badge>
                              <span className="text-xs text-gray-500">{comment.timestamp}</span>
                            </div>
                            <p className="text-sm text-gray-700">{comment.content}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div>
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">Add Comment</CardTitle>
                      <Dialog open={isAddCommentOpen} onOpenChange={setIsAddCommentOpen}>
                        <DialogTrigger asChild>
                          <Button size="sm">
                            <Plus className="h-3 w-3 mr-1" />
                            Comment
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-[425px]">
                          <DialogHeader>
                            <DialogTitle>Add Comment</DialogTitle>
                            <DialogDescription>Share an update or ask a question about the project.</DialogDescription>
                          </DialogHeader>
                          <div className="grid gap-4 py-4">
                            <div className="space-y-2">
                              <Label htmlFor="comment">Comment</Label>
                              <Textarea
                                id="comment"
                                placeholder="Share your thoughts or updates..."
                                className="min-h-[100px]"
                              />
                            </div>
                          </div>
                          <DialogFooter>
                            <Button type="submit" className="bg-gradient-to-r from-purple-600 to-blue-600">
                              Post Comment
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="text-center text-sm text-gray-500">
                        <MessageSquare className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                        <p>Join the conversation</p>
                        <p className="text-xs">Share updates and collaborate with your team</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="mt-6">
                  <CardHeader>
                    <CardTitle className="flex items-center text-lg">
                      <BarChart3 className="h-5 w-5 mr-2 text-purple-600" />
                      Quick Stats
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Tasks Completed</span>
                        <span className="font-semibold text-green-600">12/16</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">On Schedule</span>
                        <span className="font-semibold text-blue-600">Yes</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Team Velocity</span>
                        <span className="font-semibold">8.5 pts/week</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Risk Level</span>
                        <span className="font-semibold text-green-600">Low</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
