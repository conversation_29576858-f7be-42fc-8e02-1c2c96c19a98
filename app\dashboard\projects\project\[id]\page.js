"use client"

import React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  ArrowLeft,
  Users,
  DollarSign,
  Calendar,
  Play,
  Edit,
  Settings,
  Target,
  FileText,
  CheckCircle,
  XCircle,
  AlertCircle,
  Lightbulb,
  Hand,
  GitBranch,
  Package,
  ArrowRight,
  Pause,
  RotateCcw,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import Link from "next/link"

import { usePermissions } from "@/hooks/use-permissions"
import { PERMISSIONS } from "@/lib/permissions"

export default function ProjectDetailPage({ params }) {
  const [isAddTaskOpen, setIsAddTaskOpen] = useState(false)
  const [isAddCommentOpen, setIsAddCommentOpen] = useState(false)
  const [activeTimer, setActiveTimer] = useState(null)
  const [currentPhase, setCurrentPhase] = useState("PLAN")
  const [projectStatus, setProjectStatus] = useState("REVIEW_AND_APPROVAL")

  const { hasPermission, canPerformAction, getAvailableActions } = usePermissions()

  // Mock project data with workflow phases
  const project = {
    id: "PRJ001",
    name: "E-commerce Platform Redesign",
    description:
      "Complete overhaul of the customer-facing e-commerce platform with modern UI/UX and improved performance",
    phase: currentPhase,
    status: projectStatus,
    priority: "High",
    progress: 68,
    startDate: "2024-01-15",
    endDate: "2024-03-30",
    budget: 85000,
    spent: 42500,
    hoursLogged: 324,
    hoursEstimated: 480,
    manager: "Sarah Wilson",
    client: "TechCorp Solutions",
    proposedBy: "John Smith",
    businessCase: "Increase online sales by 40% and improve user experience",
    team: [
      { id: 1, name: "John Doe", avatar: "/placeholder.svg?height=40&width=40", role: "Frontend Developer", hours: 89 },
      { id: 2, name: "Lisa Chen", avatar: "/placeholder.svg?height=40&width=40", role: "UI/UX Designer", hours: 76 },
      {
        id: 3,
        name: "Mike Johnson",
        avatar: "/placeholder.svg?height=40&width=40",
        role: "Backend Developer",
        hours: 92,
      },
      { id: 4, name: "Emma Davis", avatar: "/placeholder.svg?height=40&width=40", role: "QA Engineer", hours: 67 },
    ],
  }

  // Workflow phases and statuses
  const workflowPhases = {
    CREATE: {
      color: "bg-purple-600",
      lightColor: "bg-purple-100",
      textColor: "text-purple-800",
      icon: Lightbulb,
      statuses: [
        { key: "PROPOSE_IDEA", label: "Propose Idea", icon: Lightbulb },
        { key: "INITIAL_REVIEW", label: "Initial Review of Idea", icon: AlertCircle },
        { key: "COMPLETED_REQUEST", label: "Completed Request", icon: CheckCircle },
      ],
    },
    SELECT: {
      color: "bg-teal-600",
      lightColor: "bg-teal-100",
      textColor: "text-teal-800",
      icon: Hand,
      statuses: [
        { key: "REQUEST_REVIEW", label: "Request Review", icon: FileText },
        { key: "PORTFOLIO_SELECTION", label: "Portfolio Selection", icon: Target },
        { key: "COMPLETED_SELECTION", label: "Completed Selection", icon: CheckCircle },
      ],
    },
    PLAN: {
      color: "bg-slate-700",
      lightColor: "bg-slate-100",
      textColor: "text-slate-800",
      icon: GitBranch,
      statuses: [
        { key: "FULL_BUSINESS_CASE", label: "Full Business Case", icon: FileText },
        { key: "REVIEW_AND_APPROVAL", label: "Review and Approval of Business Case", icon: AlertCircle },
        { key: "APPROVED", label: "Approved", icon: CheckCircle },
        { key: "DENIED", label: "Denied", icon: XCircle },
      ],
    },
    MANAGE: {
      color: "bg-orange-600",
      lightColor: "bg-orange-100",
      textColor: "text-orange-800",
      icon: Package,
      statuses: [
        { key: "DELIVER_PROJECT", label: "Deliver Project", icon: Play },
        { key: "PROJECT_SUSPENDED", label: "Project Suspended", icon: Pause },
        { key: "POST_PROJECT_IMPLEMENTATION", label: "Post Project Implementation", icon: RotateCcw },
      ],
    },
  }

  const getCurrentPhaseData = () => workflowPhases[currentPhase]
  const getCurrentStatusData = () => {
    const phaseData = getCurrentPhaseData()
    return phaseData.statuses.find((status) => status.key === projectStatus)
  }

  const getStatusColor = (status) => {
    switch (status) {
      case "APPROVED":
      case "COMPLETED_REQUEST":
      case "COMPLETED_SELECTION":
      case "POST_PROJECT_IMPLEMENTATION":
        return "bg-green-100 text-green-800"
      case "DENIED":
        return "bg-red-100 text-red-800"
      case "PROJECT_SUSPENDED":
        return "bg-yellow-100 text-yellow-800"
      case "REVIEW_AND_APPROVAL":
      case "INITIAL_REVIEW":
      case "REQUEST_REVIEW":
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "Critical":
        return "bg-red-100 text-red-800"
      case "High":
        return "bg-orange-100 text-orange-800"
      case "Medium":
        return "bg-yellow-100 text-yellow-800"
      case "Low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const moveToNextStatus = () => {
    const phaseData = getCurrentPhaseData()
    const currentStatusIndex = phaseData.statuses.findIndex((status) => status.key === projectStatus)

    if (currentStatusIndex < phaseData.statuses.length - 1) {
      setProjectStatus(phaseData.statuses[currentStatusIndex + 1].key)
    } else {
      // Move to next phase
      const phases = Object.keys(workflowPhases)
      const currentPhaseIndex = phases.indexOf(currentPhase)
      if (currentPhaseIndex < phases.length - 1) {
        const nextPhase = phases[currentPhaseIndex + 1]
        setCurrentPhase(nextPhase)
        setProjectStatus(workflowPhases[nextPhase].statuses[0].key)
      }
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/projects">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Projects
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{project.name}</h1>
              <p className="text-gray-600 mt-1">Project workflow management and tracking</p>
            </div>
          </div>
          <div className="flex space-x-3">
            {hasPermission(PERMISSIONS.VIEW_PROJECT) && (
              <Button variant="outline">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            )}
            {hasPermission(PERMISSIONS.EDIT_PROJECT) && (
              <Button variant="outline">
                <Edit className="h-4 w-4 mr-2" />
                Edit Project
              </Button>
            )}
          </div>
        </div>

        {/* Workflow Phase Navigation */}
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-4 gap-4 mb-6">
              {Object.entries(workflowPhases).map(([phaseKey, phaseData]) => {
                const PhaseIcon = phaseData.icon
                const isActive = currentPhase === phaseKey
                const isCompleted =
                  Object.keys(workflowPhases).indexOf(phaseKey) < Object.keys(workflowPhases).indexOf(currentPhase)

                return (
                  <div
                    key={phaseKey}
                    className={`relative p-4 rounded-lg cursor-pointer transition-all ${
                      isActive
                        ? `${phaseData.color} text-white`
                        : isCompleted
                          ? `${phaseData.lightColor} ${phaseData.textColor}`
                          : "bg-gray-100 text-gray-500"
                    }`}
                    onClick={() => setCurrentPhase(phaseKey)}
                  >
                    <div className="flex flex-col items-center text-center">
                      <PhaseIcon className="h-8 w-8 mb-2" />
                      <h3 className="font-bold text-sm">{phaseKey}</h3>
                      {isCompleted && <CheckCircle className="h-4 w-4 mt-1 text-green-600" />}
                    </div>
                    {Object.keys(workflowPhases).indexOf(phaseKey) < 3 && (
                      <ArrowRight className="absolute -right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    )}
                  </div>
                )
              })}
            </div>

            {/* Current Phase Status */}
            <div className="border-t pt-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Current Phase: {currentPhase}</h3>
                <Badge className={getStatusColor(projectStatus)}>
                  {getCurrentStatusData()?.label || projectStatus}
                </Badge>
              </div>

              {/* Phase Status Steps */}
              <div className="flex items-center space-x-4">
                {getCurrentPhaseData().statuses.map((status, index) => {
                  const StatusIcon = status.icon
                  const isCurrentStatus = status.key === projectStatus
                  const isCompletedStatus =
                    getCurrentPhaseData().statuses.findIndex((s) => s.key === projectStatus) > index

                  return (
                    <div key={status.key} className="flex items-center">
                      <div
                        className={`flex items-center space-x-2 p-2 rounded-lg ${
                          isCurrentStatus
                            ? `${getCurrentPhaseData().lightColor} ${getCurrentPhaseData().textColor}`
                            : isCompletedStatus
                              ? "bg-green-100 text-green-800"
                              : "bg-gray-100 text-gray-500"
                        }`}
                      >
                        <StatusIcon className="h-4 w-4" />
                        <span className="text-sm font-medium">{status.label}</span>
                        {isCompletedStatus && <CheckCircle className="h-3 w-3 text-green-600" />}
                      </div>
                      {index < getCurrentPhaseData().statuses.length - 1 && (
                        <ArrowRight className="h-3 w-3 mx-2 text-gray-400" />
                      )}
                    </div>
                  )
                })}
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3 mt-4">
                {hasPermission(PERMISSIONS.EDIT_PROJECT) &&
                  projectStatus !== "DENIED" &&
                  projectStatus !== "POST_PROJECT_IMPLEMENTATION" && (
                    <Button onClick={moveToNextStatus} className={`${getCurrentPhaseData().color} hover:opacity-90`}>
                      <ArrowRight className="h-4 w-4 mr-2" />
                      Move to Next Step
                    </Button>
                  )}
                {projectStatus === "REVIEW_AND_APPROVAL" && (
                  <>
                    {hasPermission(PERMISSIONS.PLAN_APPROVE_BUSINESS_CASE) && (
                      <Button onClick={() => setProjectStatus("APPROVED")} className="bg-green-600 hover:bg-green-700">
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Approve
                      </Button>
                    )}
                    {hasPermission(PERMISSIONS.PLAN_DENY_BUSINESS_CASE) && (
                      <Button onClick={() => setProjectStatus("DENIED")} variant="destructive">
                        <XCircle className="h-4 w-4 mr-2" />
                        Deny
                      </Button>
                    )}
                  </>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Project Overview Card */}
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <div className="flex items-center space-x-3 mb-4">
                  <Badge className={getStatusColor(projectStatus)}>
                    {getCurrentStatusData()?.label || projectStatus}
                  </Badge>
                  <Badge className={getPriorityColor(project.priority)}>{project.priority}</Badge>
                  <span className="text-sm text-gray-500">Managed by {project.manager}</span>
                </div>
                <p className="text-gray-600 mb-4">{project.description}</p>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Proposed By</span>
                    <div className="font-semibold">{project.proposedBy}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Start Date</span>
                    <div className="font-semibold">{formatDate(project.startDate)}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">End Date</span>
                    <div className="font-semibold">{formatDate(project.endDate)}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Client</span>
                    <div className="font-semibold">{project.client}</div>
                  </div>
                </div>

                {/* Business Case */}
                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-semibold text-blue-900 mb-2">Business Case</h4>
                  <p className="text-blue-800 text-sm">{project.businessCase}</p>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">Progress</span>
                    <span className="text-sm font-medium">{project.progress}%</span>
                  </div>
                  <Progress value={project.progress} className="h-3" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">Budget Used</span>
                    <span className="text-sm font-medium">{Math.round((project.spent / project.budget) * 100)}%</span>
                  </div>
                  <Progress value={(project.spent / project.budget) * 100} className="h-3" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">Time Progress</span>
                    <span className="text-sm font-medium">
                      {Math.round((project.hoursLogged / project.hoursEstimated) * 100)}%
                    </span>
                  </div>
                  <Progress value={(project.hoursLogged / project.hoursEstimated) * 100} className="h-3" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Current Phase</p>
                  <p className="text-2xl font-bold text-gray-900">{currentPhase}</p>
                </div>
                {React.createElement(getCurrentPhaseData().icon, { className: "h-8 w-8 text-purple-600" })}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Team Size</p>
                  <p className="text-2xl font-bold text-gray-900">{project.team.length}</p>
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Budget</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(project.budget)}</p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Days Left</p>
                  <p className="text-2xl font-bold text-gray-900">45</p>
                </div>
                <Calendar className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Workflow History */}
        <Card>
          <CardHeader>
            <CardTitle>Workflow History</CardTitle>
            <CardDescription>Track the project's journey through different phases</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                {
                  phase: "CREATE",
                  status: "COMPLETED_REQUEST",
                  date: "2024-01-10",
                  user: "John Smith",
                  action: "Completed initial project request",
                },
                {
                  phase: "SELECT",
                  status: "COMPLETED_SELECTION",
                  date: "2024-01-12",
                  user: "Sarah Wilson",
                  action: "Project selected for portfolio",
                },
                {
                  phase: "PLAN",
                  status: "REVIEW_AND_APPROVAL",
                  date: "2024-01-15",
                  user: "Mike Johnson",
                  action: "Business case under review",
                },
              ].map((entry, index) => {
                const phaseData = workflowPhases[entry.phase]
                const PhaseIcon = phaseData.icon

                return (
                  <div key={index} className="flex items-start space-x-4 p-4 border rounded-lg">
                    <div className={`p-2 rounded-lg ${phaseData.lightColor}`}>
                      <PhaseIcon className={`h-5 w-5 ${phaseData.textColor}`} />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-1">
                        <span className="font-semibold">{entry.phase}</span>
                        <Badge className={getStatusColor(entry.status)} variant="outline">
                          {entry.status.replace(/_/g, " ")}
                        </Badge>
                        <span className="text-sm text-gray-500">{formatDate(entry.date)}</span>
                      </div>
                      <p className="text-sm text-gray-600">{entry.action}</p>
                      <p className="text-xs text-gray-500 mt-1">by {entry.user}</p>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
