"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  ArrowLeft,
  Users,
  DollarSign,
  Calendar,
  MapPin,
  Plus,
  Edit,
  Settings,
  AlertTriangle,
  FileText,
  Truck,
  HardHat,
  Shield,
  TrendingUp,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import Link from "next/link"

export default function ConstructionProjectDetailPage({ params }) {
  const [isAddTaskOpen, setIsAddTaskOpen] = useState(false)
  const [isAddIncidentOpen, setIsAddIncidentOpen] = useState(false)

  // Mock project data - in real app, fetch based on params.id
  const project = {
    id: "CONST001",
    name: "Downtown Office Complex",
    description: "25-story commercial building with underground parking and retail space",
    status: "In Progress",
    priority: "High",
    progress: 68,
    startDate: "2024-01-15",
    endDate: "2024-12-30",
    budget: 850000,
    spent: 578000,
    location: "Downtown District, 123 Main Street",
    contractor: "BuildCorp Construction",
    projectManager: "Sarah Wilson",
    architect: "Johnson & Associates",
    client: "Metro Development Corp",
    workforce: 45,
    safetyScore: 92,
    phases: [
      { name: "Foundation", status: "Completed", progress: 100, startDate: "2024-01-15", endDate: "2024-03-01" },
      { name: "Structure", status: "In Progress", progress: 75, startDate: "2024-02-15", endDate: "2024-07-30" },
      { name: "MEP Systems", status: "Planning", progress: 15, startDate: "2024-06-01", endDate: "2024-10-15" },
      { name: "Finishing", status: "Not Started", progress: 0, startDate: "2024-09-01", endDate: "2024-12-30" },
    ],
    team: [
      {
        id: 1,
        name: "John Smith",
        role: "Site Supervisor",
        avatar: "/placeholder.svg?height=40&width=40",
        phone: "(*************",
      },
      {
        id: 2,
        name: "Maria Garcia",
        role: "Safety Officer",
        avatar: "/placeholder.svg?height=40&width=40",
        phone: "(*************",
      },
      {
        id: 3,
        name: "David Chen",
        role: "Quality Inspector",
        avatar: "/placeholder.svg?height=40&width=40",
        phone: "(*************",
      },
      {
        id: 4,
        name: "Lisa Johnson",
        role: "Architect",
        avatar: "/placeholder.svg?height=40&width=40",
        phone: "(*************",
      },
      {
        id: 5,
        name: "Robert Brown",
        role: "Foreman",
        avatar: "/placeholder.svg?height=40&width=40",
        phone: "(*************",
      },
      {
        id: 6,
        name: "Jennifer Lee",
        role: "Engineer",
        avatar: "/placeholder.svg?height=40&width=40",
        phone: "(*************",
      },
    ],
  }

  const tasks = [
    {
      id: "TSK001",
      title: "Install steel framework Level 15-20",
      description: "Complete steel beam installation for floors 15 through 20",
      status: "In Progress",
      priority: "High",
      assignee: "John Smith",
      dueDate: "2024-02-15",
      progress: 65,
      phase: "Structure",
    },
    {
      id: "TSK002",
      title: "Concrete pouring Level 12",
      description: "Pour concrete for 12th floor slab",
      status: "Completed",
      priority: "Medium",
      assignee: "Robert Brown",
      dueDate: "2024-02-10",
      progress: 100,
      phase: "Structure",
    },
    {
      id: "TSK003",
      title: "Electrical rough-in Level 8-10",
      description: "Install electrical conduits and wiring for floors 8-10",
      status: "Planning",
      priority: "Medium",
      assignee: "Jennifer Lee",
      dueDate: "2024-02-20",
      progress: 10,
      phase: "MEP Systems",
    },
  ]

  const equipment = [
    {
      id: "EQ001",
      name: "Tower Crane TC-150",
      status: "Active",
      operator: "Mike Johnson",
      hoursToday: 8,
      location: "Main Site",
    },
    {
      id: "EQ002",
      name: "Concrete Pump",
      status: "Active",
      operator: "David Brown",
      hoursToday: 6,
      location: "Level 12",
    },
    {
      id: "EQ003",
      name: "Excavator CAT 320",
      status: "Maintenance",
      operator: "Kevin Park",
      hoursToday: 0,
      location: "Equipment Yard",
    },
  ]

  const safetyIncidents = [
    {
      id: "INC001",
      type: "Near Miss",
      description: "Worker almost struck by falling debris from crane operation",
      date: "2024-01-22",
      severity: "Medium",
      status: "Investigated",
      reportedBy: "Maria Garcia",
      actions: "Additional safety barriers installed around crane operation area",
    },
    {
      id: "INC002",
      type: "Minor Injury",
      description: "Cut on hand from sharp metal edge during steel installation",
      date: "2024-01-20",
      severity: "Low",
      status: "Closed",
      reportedBy: "John Smith",
      actions: "First aid provided, safety briefing conducted for steel crew",
    },
  ]

  const dailyReports = [
    {
      id: "RPT001",
      date: "2024-01-23",
      weather: "Sunny, 65°F",
      workersPresent: 42,
      workersAbsent: 3,
      hoursWorked: 336,
      tasksCompleted: 3,
      issues: "Minor delay due to material delivery",
      reportedBy: "John Smith",
    },
    {
      id: "RPT002",
      date: "2024-01-22",
      weather: "Cloudy, 58°F",
      workersPresent: 45,
      workersAbsent: 0,
      hoursWorked: 360,
      tasksCompleted: 2,
      issues: "None",
      reportedBy: "John Smith",
    },
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case "Planning":
        return "bg-gray-100 text-gray-800"
      case "In Progress":
        return "bg-blue-100 text-blue-800"
      case "Completed":
        return "bg-green-100 text-green-800"
      case "On Hold":
        return "bg-yellow-100 text-yellow-800"
      case "Delayed":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "Critical":
        return "bg-red-100 text-red-800"
      case "High":
        return "bg-orange-100 text-orange-800"
      case "Medium":
        return "bg-yellow-100 text-yellow-800"
      case "Low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getSeverityColor = (severity) => {
    switch (severity) {
      case "Critical":
        return "bg-red-100 text-red-800"
      case "High":
        return "bg-orange-100 text-orange-800"
      case "Medium":
        return "bg-yellow-100 text-yellow-800"
      case "Low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/construction">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Projects
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{project.name}</h1>
              <p className="text-gray-600 mt-1">Detailed project overview and management</p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              Edit Project
            </Button>
            <Dialog open={isAddTaskOpen} onOpenChange={setIsAddTaskOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Task
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[525px]">
                <DialogHeader>
                  <DialogTitle>Create New Task</DialogTitle>
                  <DialogDescription>Add a new task to the construction project.</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="taskTitle" className="text-right">
                      Title
                    </Label>
                    <Input id="taskTitle" placeholder="Task title" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-start gap-4">
                    <Label htmlFor="taskDescription" className="text-right mt-2">
                      Description
                    </Label>
                    <Textarea id="taskDescription" placeholder="Task description..." className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="assignee" className="text-right">
                      Assignee
                    </Label>
                    <Select>
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select team member" />
                      </SelectTrigger>
                      <SelectContent>
                        {project.team.map((member) => (
                          <SelectItem key={member.id} value={member.name}>
                            {member.name} - {member.role}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="phase" className="text-right">
                      Phase
                    </Label>
                    <Select>
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select phase" />
                      </SelectTrigger>
                      <SelectContent>
                        {project.phases.map((phase) => (
                          <SelectItem key={phase.name} value={phase.name}>
                            {phase.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="priority" className="text-right">
                      Priority
                    </Label>
                    <Select>
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select priority" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="critical">Critical</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="dueDate" className="text-right">
                      Due Date
                    </Label>
                    <Input id="dueDate" type="date" className="col-span-3" />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" className="bg-gradient-to-r from-orange-600 to-red-600">
                    Create Task
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Project Overview Card */}
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <div className="flex items-center space-x-3 mb-4">
                  <Badge className={getStatusColor(project.status)}>{project.status}</Badge>
                  <Badge className={getPriorityColor(project.priority)}>{project.priority}</Badge>
                  <span className="text-sm text-gray-500">PM: {project.projectManager}</span>
                </div>
                <p className="text-gray-600 mb-4">{project.description}</p>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Location</span>
                    <div className="font-semibold flex items-center">
                      <MapPin className="h-3 w-3 mr-1" />
                      {project.location}
                    </div>
                  </div>
                  <div>
                    <span className="text-gray-600">Contractor</span>
                    <div className="font-semibold">{project.contractor}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Architect</span>
                    <div className="font-semibold">{project.architect}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Client</span>
                    <div className="font-semibold">{project.client}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Workforce</span>
                    <div className="font-semibold flex items-center">
                      <Users className="h-3 w-3 mr-1" />
                      {project.workforce} workers
                    </div>
                  </div>
                  <div>
                    <span className="text-gray-600">Safety Score</span>
                    <div className="font-semibold text-green-600">{project.safetyScore}%</div>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">Overall Progress</span>
                    <span className="text-sm font-medium">{project.progress}%</span>
                  </div>
                  <Progress value={project.progress} className="h-3" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">Budget Used</span>
                    <span className="text-sm font-medium">{Math.round((project.spent / project.budget) * 100)}%</span>
                  </div>
                  <Progress value={(project.spent / project.budget) * 100} className="h-3" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">Timeline Progress</span>
                    <span className="text-sm font-medium">45%</span>
                  </div>
                  <Progress value={45} className="h-3" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Budget</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(project.budget)}</p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Amount Spent</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(project.spent)}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Workers</p>
                  <p className="text-2xl font-bold text-gray-900">{project.workforce}</p>
                </div>
                <HardHat className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Days Remaining</p>
                  <p className="text-2xl font-bold text-gray-900">312</p>
                </div>
                <Calendar className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="tasks">Tasks</TabsTrigger>
            <TabsTrigger value="team">Team</TabsTrigger>
            <TabsTrigger value="equipment">Equipment</TabsTrigger>
            <TabsTrigger value="safety">Safety</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Project Phases</CardTitle>
                  <CardDescription>Current status of all project phases</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {project.phases.map((phase, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <h4 className="font-medium text-sm">{phase.name}</h4>
                            <Badge className={getStatusColor(phase.status)}>{phase.status}</Badge>
                          </div>
                          <span className="text-sm font-medium">{phase.progress}%</span>
                        </div>
                        <Progress value={phase.progress} className="h-2" />
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>{formatDate(phase.startDate)}</span>
                          <span>{formatDate(phase.endDate)}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Daily Progress Report</CardTitle>
                  <CardDescription>Latest daily report summary</CardDescription>
                </CardHeader>
                <CardContent>
                  {dailyReports.length > 0 && (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Date</span>
                        <span className="text-sm">{formatDate(dailyReports[0].date)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Weather</span>
                        <span className="text-sm">{dailyReports[0].weather}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Workers Present</span>
                        <span className="text-sm">
                          {dailyReports[0].workersPresent}/{project.workforce}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Hours Worked</span>
                        <span className="text-sm">{dailyReports[0].hoursWorked}h</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Tasks Completed</span>
                        <span className="text-sm">{dailyReports[0].tasksCompleted}</span>
                      </div>
                      <div className="pt-2 border-t">
                        <span className="text-sm font-medium">Issues:</span>
                        <p className="text-sm text-gray-600 mt-1">{dailyReports[0].issues}</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Tasks Tab */}
          <TabsContent value="tasks" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Project Tasks</CardTitle>
                <CardDescription>Track and manage construction tasks</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {tasks.map((task) => (
                    <Card key={task.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h4 className="font-semibold text-gray-900">{task.title}</h4>
                              <Badge className={getStatusColor(task.status)}>{task.status}</Badge>
                              <Badge className={getPriorityColor(task.priority)}>{task.priority}</Badge>
                            </div>
                            <p className="text-sm text-gray-600 mb-3">{task.description}</p>
                            <div className="flex items-center space-x-6 text-sm text-gray-500">
                              <span>Assigned to: {task.assignee}</span>
                              <span>Phase: {task.phase}</span>
                              <span>Due: {formatDate(task.dueDate)}</span>
                            </div>
                          </div>
                          <Button size="sm" variant="outline">
                            <Edit className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                        </div>
                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm text-gray-600">Progress</span>
                            <span className="text-sm font-medium">{task.progress}%</span>
                          </div>
                          <Progress value={task.progress} className="h-2" />
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Team Tab */}
          <TabsContent value="team" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Project Team</CardTitle>
                <CardDescription>Team members and their roles</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {project.team.map((member) => (
                    <Card key={member.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-4">
                          <Avatar className="h-12 w-12">
                            <AvatarImage src={member.avatar || "/placeholder.svg"} alt={member.name} />
                            <AvatarFallback>
                              {member.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900">{member.name}</h4>
                            <p className="text-sm text-gray-600">{member.role}</p>
                            <p className="text-sm text-gray-500">{member.phone}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Equipment Tab */}
          <TabsContent value="equipment" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Truck className="h-5 w-5 mr-2 text-orange-600" />
                  Project Equipment
                </CardTitle>
                <CardDescription>Equipment currently assigned to this project</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {equipment.map((item) => (
                    <Card key={item.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className="p-3 bg-orange-100 rounded-lg">
                              <Truck className="h-6 w-6 text-orange-600" />
                            </div>
                            <div>
                              <h4 className="font-semibold text-gray-900">{item.name}</h4>
                              <p className="text-sm text-gray-600">Operator: {item.operator}</p>
                              <p className="text-sm text-gray-500">Location: {item.location}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <Badge className={getStatusColor(item.status)}>{item.status}</Badge>
                            <div className="text-sm text-gray-500 mt-2">
                              <div>Hours Today: {item.hoursToday}h</div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Safety Tab */}
          <TabsContent value="safety" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center">
                        <Shield className="h-5 w-5 mr-2 text-red-600" />
                        Safety Incidents
                      </CardTitle>
                      <Dialog open={isAddIncidentOpen} onOpenChange={setIsAddIncidentOpen}>
                        <DialogTrigger asChild>
                          <Button size="sm" variant="outline">
                            <Plus className="h-4 w-4 mr-2" />
                            Report Incident
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-[525px]">
                          <DialogHeader>
                            <DialogTitle>Report Safety Incident</DialogTitle>
                            <DialogDescription>Report a safety incident or near miss.</DialogDescription>
                          </DialogHeader>
                          <div className="grid gap-4 py-4">
                            <div className="grid grid-cols-4 items-center gap-4">
                              <Label htmlFor="incidentType" className="text-right">
                                Type
                              </Label>
                              <Select>
                                <SelectTrigger className="col-span-3">
                                  <SelectValue placeholder="Select incident type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="injury">Injury</SelectItem>
                                  <SelectItem value="near-miss">Near Miss</SelectItem>
                                  <SelectItem value="equipment-damage">Equipment Damage</SelectItem>
                                  <SelectItem value="property-damage">Property Damage</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="grid grid-cols-4 items-start gap-4">
                              <Label htmlFor="incidentDescription" className="text-right mt-2">
                                Description
                              </Label>
                              <Textarea
                                id="incidentDescription"
                                placeholder="Describe the incident..."
                                className="col-span-3"
                              />
                            </div>
                            <div className="grid grid-cols-4 items-center gap-4">
                              <Label htmlFor="severity" className="text-right">
                                Severity
                              </Label>
                              <Select>
                                <SelectTrigger className="col-span-3">
                                  <SelectValue placeholder="Select severity" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="critical">Critical</SelectItem>
                                  <SelectItem value="high">High</SelectItem>
                                  <SelectItem value="medium">Medium</SelectItem>
                                  <SelectItem value="low">Low</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="grid grid-cols-4 items-center gap-4">
                              <Label htmlFor="incidentDate" className="text-right">
                                Date
                              </Label>
                              <Input id="incidentDate" type="date" className="col-span-3" />
                            </div>
                          </div>
                          <DialogFooter>
                            <Button type="submit" className="bg-gradient-to-r from-red-600 to-orange-600">
                              Report Incident
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {safetyIncidents.map((incident) => (
                        <Card key={incident.id} className="hover:shadow-md transition-shadow">
                          <CardContent className="p-4">
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex-1">
                                <div className="flex items-center space-x-3 mb-2">
                                  <h4 className="font-semibold text-gray-900">{incident.type}</h4>
                                  <Badge className={getSeverityColor(incident.severity)}>{incident.severity}</Badge>
                                  <Badge className={getStatusColor(incident.status)}>{incident.status}</Badge>
                                </div>
                                <p className="text-sm text-gray-600 mb-2">{incident.description}</p>
                                <div className="flex items-center space-x-4 text-sm text-gray-500">
                                  <span>Date: {formatDate(incident.date)}</span>
                                  <span>Reported by: {incident.reportedBy}</span>
                                </div>
                              </div>
                            </div>
                            <div className="bg-gray-50 p-3 rounded-lg">
                              <h5 className="text-sm font-medium text-gray-900 mb-1">Actions Taken:</h5>
                              <p className="text-sm text-gray-600">{incident.actions}</p>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <AlertTriangle className="h-5 w-5 mr-2 text-yellow-600" />
                      Safety Metrics
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-gray-600">Safety Score</span>
                          <span className="text-sm font-medium">{project.safetyScore}%</span>
                        </div>
                        <Progress value={project.safetyScore} className="h-2" />
                      </div>
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-gray-600">Days Without Incident</span>
                          <span className="text-2xl font-bold text-green-600">12</span>
                        </div>
                      </div>
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-gray-600">Safety Training</span>
                          <span className="text-sm font-medium">89%</span>
                        </div>
                        <Progress value={89} className="h-2" />
                      </div>
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-gray-600">Equipment Inspections</span>
                          <span className="text-sm font-medium">96%</span>
                        </div>
                        <Progress value={96} className="h-2" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Reports Tab */}
          <TabsContent value="reports" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Daily Reports</CardTitle>
                <CardDescription>Daily progress and activity reports</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {dailyReports.map((report) => (
                    <Card key={report.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900">Daily Report - {formatDate(report.date)}</h4>
                            <p className="text-sm text-gray-600">Reported by: {report.reportedBy}</p>
                          </div>
                          <Button size="sm" variant="outline">
                            <FileText className="h-3 w-3 mr-1" />
                            View Full Report
                          </Button>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Weather</span>
                            <div className="font-semibold">{report.weather}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Workers Present</span>
                            <div className="font-semibold">{report.workersPresent}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Hours Worked</span>
                            <div className="font-semibold">{report.hoursWorked}h</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Tasks Completed</span>
                            <div className="font-semibold">{report.tasksCompleted}</div>
                          </div>
                        </div>
                        {report.issues !== "None" && (
                          <div className="mt-3 p-3 bg-yellow-50 rounded-lg">
                            <h5 className="text-sm font-medium text-yellow-800 mb-1">Issues:</h5>
                            <p className="text-sm text-yellow-700">{report.issues}</p>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
