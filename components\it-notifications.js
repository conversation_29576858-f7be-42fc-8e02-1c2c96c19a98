"use client"

import { useState } from "react"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Bell,
  Key,
  AlertTriangle,
  Calendar,
  Shield,
  CheckCircle,
  X,
  Clock,
  Server,
  Cloud,
  DollarSign,
  Users,
  Zap,
} from "lucide-react"

export default function ITNotifications() {
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      type: "license_expiring",
      title: "Software License Expiring",
      message: "JetBrains IntelliJ IDEA license expires in 30 days",
      licenseId: "LIC-004",
      licenseName: "JetBrains IntelliJ IDEA",
      priority: "high",
      expiryDate: "2024-03-15",
      timestamp: "2024-02-10T10:30:00Z",
      read: false,
    },
    {
      id: 2,
      type: "license_over_allocated",
      title: "License Over-allocation",
      message: "Adobe Creative Suite is over-allocated by 3 licenses",
      licenseId: "LIC-002",
      licenseName: "Adobe Creative Suite",
      priority: "critical",
      overAllocation: 3,
      timestamp: "2024-02-09T14:20:00Z",
      read: false,
    },
    {
      id: 3,
      type: "saas_high_risk",
      title: "High-Risk SaaS Application",
      message: "Dropbox Business flagged as high-risk due to data classification",
      saasId: "SAAS-004",
      saasName: "Dropbox Business",
      priority: "high",
      riskLevel: "High",
      timestamp: "2024-02-08T09:15:00Z",
      read: true,
    },
    {
      id: 4,
      type: "infrastructure_critical",
      title: "Infrastructure Critical Alert",
      message: "Backup Storage Array health dropped to 45%",
      infrastructureId: "INF-004",
      infrastructureName: "Backup Storage Array",
      priority: "critical",
      healthScore: 45,
      timestamp: "2024-02-07T16:45:00Z",
      read: false,
    },
    {
      id: 5,
      type: "security_vulnerability",
      title: "Security Vulnerability Detected",
      message: "Critical vulnerability CVE-2024-1234 found in production server",
      severity: "Critical",
      cveId: "CVE-2024-1234",
      priority: "critical",
      timestamp: "2024-02-06T11:30:00Z",
      read: false,
    },
    {
      id: 6,
      type: "saas_spend_increase",
      title: "SaaS Spending Increase",
      message: "Monthly SaaS spending increased by 15% this month",
      previousSpend: 11826,
      currentSpend: 13600,
      increasePercent: 15,
      priority: "medium",
      timestamp: "2024-02-05T08:00:00Z",
      read: true,
    },
    {
      id: 7,
      type: "compliance_violation",
      title: "Compliance Policy Violation",
      message: "Password policy compliance check failed for 12 users",
      policyName: "Password Policy",
      violationCount: 12,
      priority: "high",
      timestamp: "2024-02-04T13:15:00Z",
      read: true,
    },
  ])

  const [filter, setFilter] = useState("all")

  const getNotificationIcon = (type) => {
    switch (type) {
      case "license_expiring":
      case "license_over_allocated":
        return <Key className="h-4 w-4" />
      case "saas_high_risk":
      case "saas_spend_increase":
        return <Cloud className="h-4 w-4" />
      case "infrastructure_critical":
        return <Server className="h-4 w-4" />
      case "security_vulnerability":
      case "compliance_violation":
        return <Shield className="h-4 w-4" />
      default:
        return <Bell className="h-4 w-4" />
    }
  }

  const getNotificationColor = (priority) => {
    switch (priority) {
      case "critical":
        return "border-l-red-500 bg-red-50"
      case "high":
        return "border-l-orange-500 bg-orange-50"
      case "medium":
        return "border-l-yellow-500 bg-yellow-50"
      case "low":
        return "border-l-blue-500 bg-blue-50"
      case "info":
        return "border-l-green-500 bg-green-50"
      default:
        return "border-l-gray-500 bg-gray-50"
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "critical":
        return "bg-red-100 text-red-800"
      case "high":
        return "bg-orange-100 text-orange-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "low":
        return "bg-blue-100 text-blue-800"
      case "info":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const markAsRead = (notificationId) => {
    setNotifications((prev) =>
      prev.map((notification) => (notification.id === notificationId ? { ...notification, read: true } : notification)),
    )
  }

  const dismissNotification = (notificationId) => {
    setNotifications((prev) => prev.filter((notification) => notification.id !== notificationId))
  }

  const filteredNotifications = notifications.filter((notification) => {
    if (filter === "all") return true
    if (filter === "unread") return !notification.read
    return notification.priority === filter
  })

  const unreadCount = notifications.filter((n) => !n.read).length
  const criticalCount = notifications.filter((n) => n.priority === "critical" && !n.read).length

  const formatTimeAgo = (timestamp) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diffInHours = Math.floor((now - time) / (1000 * 60 * 60))

    if (diffInHours < 1) return "Just now"
    if (diffInHours < 24) return `${diffInHours}h ago`
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays}d ago`
  }

  const getNotificationDetails = (notification) => {
    switch (notification.type) {
      case "license_expiring":
        return (
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            <span className="flex items-center">
              <Key className="h-3 w-3 mr-1" />
              {notification.licenseName}
            </span>
            <span className="flex items-center">
              <Calendar className="h-3 w-3 mr-1" />
              Expires: {notification.expiryDate}
            </span>
          </div>
        )
      case "license_over_allocated":
        return (
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            <span className="flex items-center">
              <Key className="h-3 w-3 mr-1" />
              {notification.licenseName}
            </span>
            <span className="flex items-center text-red-600">
              <AlertTriangle className="h-3 w-3 mr-1" />
              Over by {notification.overAllocation} licenses
            </span>
          </div>
        )
      case "saas_high_risk":
        return (
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            <span className="flex items-center">
              <Cloud className="h-3 w-3 mr-1" />
              {notification.saasName}
            </span>
            <span className="flex items-center text-red-600">
              <AlertTriangle className="h-3 w-3 mr-1" />
              Risk Level: {notification.riskLevel}
            </span>
          </div>
        )
      case "infrastructure_critical":
        return (
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            <span className="flex items-center">
              <Server className="h-3 w-3 mr-1" />
              {notification.infrastructureName}
            </span>
            <span className="flex items-center text-red-600">
              <Zap className="h-3 w-3 mr-1" />
              Health: {notification.healthScore}%
            </span>
          </div>
        )
      case "security_vulnerability":
        return (
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            <span className="flex items-center">
              <Shield className="h-3 w-3 mr-1" />
              {notification.cveId}
            </span>
            <span className="flex items-center text-red-600">
              <AlertTriangle className="h-3 w-3 mr-1" />
              Severity: {notification.severity}
            </span>
          </div>
        )
      case "saas_spend_increase":
        return (
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            <span className="flex items-center">
              <DollarSign className="h-3 w-3 mr-1" />${notification.previousSpend.toLocaleString()} → $
              {notification.currentSpend.toLocaleString()}
            </span>
            <span className="flex items-center text-orange-600">
              <AlertTriangle className="h-3 w-3 mr-1" />+{notification.increasePercent}% increase
            </span>
          </div>
        )
      case "compliance_violation":
        return (
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            <span className="flex items-center">
              <Shield className="h-3 w-3 mr-1" />
              {notification.policyName}
            </span>
            <span className="flex items-center text-red-600">
              <Users className="h-3 w-3 mr-1" />
              {notification.violationCount} violations
            </span>
          </div>
        )
      default:
        return (
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            <span className="flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              {formatTimeAgo(notification.timestamp)}
            </span>
          </div>
        )
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Bell className="h-5 w-5 mr-2 text-indigo-600" />
              IT Management Notifications
              {unreadCount > 0 && <Badge className="ml-2 bg-red-500 hover:bg-red-500">{unreadCount}</Badge>}
              {criticalCount > 0 && (
                <Badge className="ml-2 bg-red-600 hover:bg-red-600 animate-pulse">{criticalCount} Critical</Badge>
              )}
            </CardTitle>
            <CardDescription>
              Stay updated on software licenses, infrastructure health, and security compliance
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant={filter === "all" ? "default" : "outline"} size="sm" onClick={() => setFilter("all")}>
              All
            </Button>
            <Button variant={filter === "unread" ? "default" : "outline"} size="sm" onClick={() => setFilter("unread")}>
              Unread ({unreadCount})
            </Button>
            <Button
              variant={filter === "critical" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilter("critical")}
            >
              Critical
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {filteredNotifications.length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">All caught up!</h3>
              <p className="text-gray-600">No IT notifications match your current filter.</p>
            </div>
          ) : (
            filteredNotifications.map((notification) => (
              <Alert
                key={notification.id}
                className={`border-l-4 ${getNotificationColor(notification.priority)} ${
                  !notification.read ? "ring-2 ring-blue-100" : ""
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    <div className="p-2 rounded-lg bg-white">{getNotificationIcon(notification.type)}</div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-gray-900">{notification.title}</h4>
                        <Badge className={getPriorityColor(notification.priority)}>{notification.priority}</Badge>
                        {!notification.read && <div className="w-2 h-2 bg-blue-500 rounded-full"></div>}
                      </div>
                      <AlertDescription className="text-gray-700 mb-2">{notification.message}</AlertDescription>
                      <div className="flex items-center justify-between">
                        {getNotificationDetails(notification)}
                        <span className="flex items-center text-xs text-gray-500">
                          <Clock className="h-3 w-3 mr-1" />
                          {formatTimeAgo(notification.timestamp)}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    {!notification.read && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => markAsRead(notification.id)}
                        className="text-blue-600 hover:text-blue-700"
                      >
                        Mark as read
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => dismissNotification(notification.id)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </Alert>
            ))
          )}
        </div>

        {/* Quick Actions */}
        {filteredNotifications.some(
          (n) =>
            n.type === "license_expiring" ||
            n.type === "license_over_allocated" ||
            n.type === "infrastructure_critical" ||
            n.type === "security_vulnerability",
        ) && (
          <div className="mt-6 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">Quick Actions</h3>
            <div className="flex flex-wrap gap-2">
              {filteredNotifications.some((n) => n.type === "license_expiring") && (
                <Button size="sm" className="bg-gradient-to-r from-blue-600 to-indigo-600">
                  <Key className="h-4 w-4 mr-2" />
                  Renew Licenses
                </Button>
              )}
              {filteredNotifications.some((n) => n.type === "infrastructure_critical") && (
                <Button size="sm" variant="outline">
                  <Server className="h-4 w-4 mr-2" />
                  Check Infrastructure
                </Button>
              )}
              {filteredNotifications.some((n) => n.type === "security_vulnerability") && (
                <Button size="sm" variant="outline">
                  <Shield className="h-4 w-4 mr-2" />
                  Security Dashboard
                </Button>
              )}
              <Button size="sm" variant="outline">
                <Bell className="h-4 w-4 mr-2" />
                IT Management
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
