class ApiClient {
  constructor(baseURL = "/api") {
    this.baseURL = baseURL
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`
    const config = {
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    }

    if (config.body && typeof config.body === "object") {
      config.body = JSON.stringify(config.body)
    }

    try {
      const response = await fetch(url, config)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`)
      }

      return data
    } catch (error) {
      console.error("API request failed:", error)
      throw error
    }
  }

  // Generic CRUD operations
  async get(endpoint, params = {}) {
    const searchParams = new URLSearchParams(params)
    const queryString = searchParams.toString()
    return this.request(`${endpoint}${queryString ? `?${queryString}` : ""}`)
  }

  async create(endpoint, data) {
    return this.request(`/${endpoint}`, {
      method: "POST",
      body: data,
    })
  }

  async update(endpoint, id, data) {
    return this.request(`/${endpoint}/${id}`, {
      method: "PUT",
      body: data,
    })
  }

  async delete(endpoint, id) {
    return this.request(`/${endpoint}/${id}`, {
      method: "DELETE",
    })
  }

  // Specific methods for master data
  async getCountries(params = {}) {
    return this.get("/countries", params)
  }

  async getStates(params = {}) {
    return this.get("/states", params)
  }

  async getCities(params = {}) {
    return this.get("/cities", params)
  }

  async getLocations(params = {}) {
    return this.get("/locations", params)
  }

  async getDepartments(params = {}) {
    return this.get("/departments", params)
  }

  async getDesignations(params = {}) {
    return this.get("/designations", params)
  }

  async getShifts(params = {}) {
    return this.get("/shifts", params)
  }

  async getCurrencies(params = {}) {
    return this.get("/currencies", params)
  }

  async getTimezones(params = {}) {
    return this.get("/timezones", params)
  }

  async getTaxCategories(params = {}) {
    return this.get("/tax-categories", params)
  }

  async getUOMs(params = {}) {
    return this.get("/uoms", params)
  }

  async getPaymentTerms(params = {}) {
    return this.get("/payment-terms", params)
  }

  async getBankDetails(params = {}) {
    return this.get("/bank-details", params)
  }

  async getDocumentTypes(params = {}) {
    return this.get("/document-types", params)
  }

  async getUserRoles(params = {}) {
    return this.get("/user-roles", params)
  }

  // Module-specific methods
  async getEmployees(params = {}) {
    return this.get("/employees", params)
  }

  async getLeads(params = {}) {
    return this.get("/leads", params)
  }

  async getSalesOrders(params = {}) {
    return this.get("/sales-orders", params)
  }

  async getItems(params = {}) {
    return this.get("/items", params)
  }

  async getProjects(params = {}) {
    return this.get("/projects", params)
  }
}

export const apiClient = new ApiClient()
