import { NextResponse } from "next/server"
import {
  PROJECT_TYPE_TEMPLATES,
  ROLE_COMBINATION_TEMPLATES,
  getRecommendedTemplates,
  applyProjectTypeTemplate,
  applyRoleCombinationTemplate,
} from "@/lib/permission-templates"

// Mock applied templates storage
const appliedTemplates = [
  {
    id: "1",
    projectId: "proj-1",
    templateType: "project_type",
    templateKey: "software_development",
    templateName: "Software Development Project",
    appliedBy: "admin",
    appliedAt: "2024-01-15T10:30:00Z",
    configuration: {
      phases: ["CREATE", "SELECT", "PLAN", "MANAGE"],
      customSettings: {
        allowDeveloperApproval: false,
        requireTechnicalReview: true,
        enableAgileWorkflow: true,
        requireCodeReview: true,
      },
    },
  },
  {
    id: "2",
    projectId: "proj-2",
    templateType: "role_combination",
    templateKey: "project_core_team",
    templateName: "Project Core Team",
    appliedBy: "admin",
    appliedAt: "2024-01-14T16:45:00Z",
    configuration: {
      userIds: ["user-1", "user-2", "user-3"],
      roles: ["project_manager", "team_lead", "business_analyst"],
      permissions: [
        "view_project",
        "edit_project",
        "manage_team",
        "view_reports",
        "plan_create_business_case",
        "plan_edit_business_case",
        "manage_start_project",
        "manage_assign_team",
      ],
      applicablePhases: ["CREATE", "SELECT", "PLAN", "MANAGE"],
    },
  },
]

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get("type") // 'project_type' or 'role_combination' or 'applied'
    const projectId = searchParams.get("projectId")
    const projectType = searchParams.get("projectType")
    const currentPhase = searchParams.get("currentPhase")
    const teamSize = Number.parseInt(searchParams.get("teamSize")) || 5

    if (type === "applied") {
      // Return applied templates
      let filtered = appliedTemplates
      if (projectId) {
        filtered = appliedTemplates.filter((template) => template.projectId === projectId)
      }
      return NextResponse.json({
        success: true,
        data: filtered,
        total: filtered.length,
      })
    }

    if (type === "recommendations") {
      // Return recommended templates
      if (!projectType || !currentPhase) {
        return NextResponse.json({ success: false, error: "Missing required parameters" }, { status: 400 })
      }

      const recommendations = getRecommendedTemplates(projectType, currentPhase, teamSize)
      return NextResponse.json({
        success: true,
        data: recommendations,
        total: recommendations.length,
      })
    }

    // Return all available templates
    const data = {
      projectTypes: PROJECT_TYPE_TEMPLATES,
      roleCombinations: ROLE_COMBINATION_TEMPLATES,
    }

    if (type === "project_type") {
      return NextResponse.json({
        success: true,
        data: PROJECT_TYPE_TEMPLATES,
        total: Object.keys(PROJECT_TYPE_TEMPLATES).length,
      })
    }

    if (type === "role_combination") {
      return NextResponse.json({
        success: true,
        data: ROLE_COMBINATION_TEMPLATES,
        total: Object.keys(ROLE_COMBINATION_TEMPLATES).length,
      })
    }

    return NextResponse.json({
      success: true,
      data,
      total: Object.keys(PROJECT_TYPE_TEMPLATES).length + Object.keys(ROLE_COMBINATION_TEMPLATES).length,
    })
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to fetch templates" }, { status: 500 })
  }
}

export async function POST(request) {
  try {
    const body = await request.json()
    const { templateType, templateKey, projectId, userIds, appliedBy, configuration } = body

    // Validate required fields
    if (!templateType || !templateKey || !projectId || !appliedBy) {
      return NextResponse.json({ success: false, error: "Missing required fields" }, { status: 400 })
    }

    let templateResult
    let templateName

    if (templateType === "project_type") {
      templateResult = applyProjectTypeTemplate(templateKey, projectId)
      templateName = PROJECT_TYPE_TEMPLATES[templateKey]?.name
    } else if (templateType === "role_combination") {
      if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
        return NextResponse.json(
          { success: false, error: "User IDs required for role combination template" },
          { status: 400 },
        )
      }
      templateResult = applyRoleCombinationTemplate(templateKey, userIds, projectId)
      templateName = ROLE_COMBINATION_TEMPLATES[templateKey]?.name
    } else {
      return NextResponse.json({ success: false, error: "Invalid template type" }, { status: 400 })
    }

    if (!templateResult) {
      return NextResponse.json({ success: false, error: "Failed to apply template" }, { status: 400 })
    }

    // Create applied template record
    const appliedTemplate = {
      id: (appliedTemplates.length + 1).toString(),
      projectId,
      templateType,
      templateKey,
      templateName,
      appliedBy,
      appliedAt: new Date().toISOString(),
      configuration: configuration || templateResult,
    }

    appliedTemplates.push(appliedTemplate)

    return NextResponse.json(
      {
        success: true,
        data: appliedTemplate,
        message: "Template applied successfully",
      },
      { status: 201 },
    )
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to apply template" }, { status: 500 })
  }
}
