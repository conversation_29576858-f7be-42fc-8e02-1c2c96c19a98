"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  ShoppingCart,
  Users,
  DollarSign,
  FileText,
  Plus,
  Search,
  MoreHorizontal,
  TrendingUp,
  Clock,
  Settings,
  Download,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"

export default function PurchaseManagementPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [isCreateRequisitionOpen, setIsCreateRequisitionOpen] = useState(false)
  const [isCreatePOOpen, setIsCreatePOOpen] = useState(false)

  const purchaseStats = [
    {
      title: "Active Purchase Orders",
      value: "47",
      change: "+12 this month",
      icon: ShoppingCart,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Total Purchase Value",
      value: "$2.8M",
      change: "+25% vs last month",
      icon: DollarSign,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Vendor Performance",
      value: "92%",
      change: "On-time delivery",
      icon: TrendingUp,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      title: "Pending Approvals",
      value: "8",
      change: "Requires attention",
      icon: Clock,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
  ]

  const materialRequisitions = [
    {
      id: "MR001",
      project: "Metro Tower Complex",
      requestedBy: "John Smith",
      department: "Construction",
      date: "2024-01-23",
      status: "Pending Approval",
      priority: "High",
      totalValue: 125000,
      items: [
        { name: "Steel Beams (Grade A)", quantity: 50, unit: "tons", unitPrice: 1200, totalPrice: 60000 },
        { name: "Concrete Mix (M25)", quantity: 200, unit: "cubic meters", unitPrice: 150, totalPrice: 30000 },
        { name: "Reinforcement Bars", quantity: 100, unit: "tons", unitPrice: 350, totalPrice: 35000 },
      ],
      approvalWorkflow: [
        { step: "Site Supervisor", status: "Approved", approver: "John Smith", date: "2024-01-23" },
        { step: "Project Manager", status: "Approved", approver: "Sarah Wilson", date: "2024-01-23" },
        { step: "Purchase Manager", status: "Pending", approver: "Mike Johnson", date: null },
        { step: "Finance Head", status: "Pending", approver: "Lisa Chen", date: null },
      ],
    },
    {
      id: "MR002",
      project: "Green Valley Residential",
      requestedBy: "Robert Brown",
      department: "Construction",
      date: "2024-01-22",
      status: "Approved",
      priority: "Medium",
      totalValue: 85000,
      items: [
        { name: "Cement (OPC 53)", quantity: 500, unit: "bags", unitPrice: 45, totalPrice: 22500 },
        { name: "Bricks (Class A)", quantity: 50000, unit: "pieces", unitPrice: 0.8, totalPrice: 40000 },
        { name: "Sand (River Sand)", quantity: 150, unit: "cubic meters", unitPrice: 150, totalPrice: 22500 },
      ],
      approvalWorkflow: [
        { step: "Site Supervisor", status: "Approved", approver: "Robert Brown", date: "2024-01-22" },
        { step: "Project Manager", status: "Approved", approver: "Mike Anderson", date: "2024-01-22" },
        { step: "Purchase Manager", status: "Approved", approver: "Mike Johnson", date: "2024-01-22" },
        { step: "Finance Head", status: "Approved", approver: "Lisa Chen", date: "2024-01-22" },
      ],
    },
  ]

  const purchaseOrders = [
    {
      id: "PO001",
      vendor: "Steel Dynamics Corp",
      project: "Metro Tower Complex",
      date: "2024-01-24",
      deliveryDate: "2024-02-15",
      status: "Confirmed",
      totalValue: 185000,
      items: [
        { name: "Steel Beams (Grade A)", quantity: 75, unit: "tons", unitPrice: 1180, totalPrice: 88500 },
        { name: "Steel Columns", quantity: 40, unit: "pieces", unitPrice: 2412.5, totalPrice: 96500 },
      ],
      terms: "Net 30 days, FOB Destination",
      contactPerson: "David Steel",
      phone: "(*************",
    },
    {
      id: "PO002",
      vendor: "Concrete Solutions Ltd",
      project: "Green Valley Residential",
      date: "2024-01-23",
      deliveryDate: "2024-02-10",
      status: "In Transit",
      totalValue: 95000,
      items: [
        { name: "Ready Mix Concrete (M25)", quantity: 300, unit: "cubic meters", unitPrice: 180, totalPrice: 54000 },
        { name: "Concrete Blocks", quantity: 2000, unit: "pieces", unitPrice: 20.5, totalPrice: 41000 },
      ],
      terms: "Net 15 days, Ex-Works",
      contactPerson: "Maria Concrete",
      phone: "(*************",
    },
  ]

  const vendors = [
    {
      id: "VEN001",
      name: "Steel Dynamics Corp",
      category: "Steel & Metal",
      rating: 4.8,
      totalOrders: 45,
      totalValue: 2850000,
      onTimeDelivery: 96,
      qualityScore: 94,
      contactPerson: "David Steel",
      phone: "(*************",
      email: "<EMAIL>",
      address: "123 Industrial Ave, Steel City, SC 12345",
      paymentTerms: "Net 30 days",
      certifications: ["ISO 9001", "ISO 14001", "OHSAS 18001"],
    },
    {
      id: "VEN002",
      name: "Concrete Solutions Ltd",
      category: "Concrete & Cement",
      rating: 4.6,
      totalOrders: 38,
      totalValue: 1950000,
      onTimeDelivery: 92,
      qualityScore: 91,
      contactPerson: "Maria Concrete",
      phone: "(*************",
      email: "<EMAIL>",
      address: "456 Concrete Blvd, Mix City, MC 23456",
      paymentTerms: "Net 15 days",
      certifications: ["ISO 9001", "Green Building Council"],
    },
    {
      id: "VEN003",
      name: "BuildMart Supplies",
      category: "General Construction",
      rating: 4.3,
      totalOrders: 62,
      totalValue: 1250000,
      onTimeDelivery: 88,
      qualityScore: 87,
      contactPerson: "John Builder",
      phone: "(*************",
      email: "<EMAIL>",
      address: "789 Supply St, Build Town, BT 34567",
      paymentTerms: "Net 30 days",
      certifications: ["ISO 9001"],
    },
  ]

  const quotationComparisons = [
    {
      id: "QC001",
      item: "Steel Beams (Grade A) - 50 tons",
      project: "Metro Tower Complex",
      requestDate: "2024-01-20",
      quotations: [
        { vendor: "Steel Dynamics Corp", price: 59000, deliveryDays: 21, rating: 4.8, selected: true },
        { vendor: "MetalWorks Inc", price: 62500, deliveryDays: 18, rating: 4.5, selected: false },
        { vendor: "Iron & Steel Co", price: 58000, deliveryDays: 28, rating: 4.2, selected: false },
      ],
      criteria: {
        price: 40,
        delivery: 30,
        quality: 20,
        relationship: 10,
      },
    },
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case "Pending Approval":
        return "bg-yellow-100 text-yellow-800"
      case "Approved":
        return "bg-green-100 text-green-800"
      case "Rejected":
        return "bg-red-100 text-red-800"
      case "Confirmed":
        return "bg-blue-100 text-blue-800"
      case "In Transit":
        return "bg-purple-100 text-purple-800"
      case "Delivered":
        return "bg-green-100 text-green-800"
      case "Cancelled":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "Critical":
        return "bg-red-100 text-red-800"
      case "High":
        return "bg-orange-100 text-orange-800"
      case "Medium":
        return "bg-yellow-100 text-yellow-800"
      case "Low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Purchase Management</h1>
            <p className="text-gray-600 mt-1">Material requisition, vendor management, and procurement workflows</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Reports
            </Button>
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Dialog open={isCreateRequisitionOpen} onOpenChange={setIsCreateRequisitionOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  New Requisition
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Create Material Requisition</DialogTitle>
                  <DialogDescription>Request materials for your construction project.</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="project">Project</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select project" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="metro">Metro Tower Complex</SelectItem>
                          <SelectItem value="green">Green Valley Residential</SelectItem>
                          <SelectItem value="industrial">Industrial Park Phase 1</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="priority">Priority</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="critical">Critical</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="low">Low</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea id="description" placeholder="Describe the materials needed..." />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="requiredDate">Required Date</Label>
                    <Input id="requiredDate" type="date" />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" className="bg-gradient-to-r from-green-600 to-blue-600">
                    Create Requisition
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {purchaseStats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-green-600">{stat.change}</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="requisitions" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="requisitions">Requisitions</TabsTrigger>
            <TabsTrigger value="purchase-orders">Purchase Orders</TabsTrigger>
            <TabsTrigger value="vendors">Vendors</TabsTrigger>
            <TabsTrigger value="quotations">Quotations</TabsTrigger>
            <TabsTrigger value="contracts">Contracts</TabsTrigger>
          </TabsList>

          {/* Material Requisitions Tab */}
          <TabsContent value="requisitions" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Material Requisitions</CardTitle>
                  <div className="flex space-x-2">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search requisitions..."
                        className="pl-10 w-64"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                      <SelectTrigger className="w-40">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="pending">Pending Approval</SelectItem>
                        <SelectItem value="approved">Approved</SelectItem>
                        <SelectItem value="rejected">Rejected</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {materialRequisitions.map((requisition) => (
                    <Card key={requisition.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h3 className="font-semibold text-gray-900">{requisition.id}</h3>
                              <Badge className={getStatusColor(requisition.status)}>{requisition.status}</Badge>
                              <Badge className={getPriorityColor(requisition.priority)}>{requisition.priority}</Badge>
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500 mb-4">
                              <span>Project: {requisition.project}</span>
                              <span>Requested by: {requisition.requestedBy}</span>
                              <span>Department: {requisition.department}</span>
                              <span>Date: {formatDate(requisition.date)}</span>
                            </div>
                            <div className="text-lg font-semibold text-gray-900 mb-4">
                              Total Value: {formatCurrency(requisition.totalValue)}
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>View Details</DropdownMenuItem>
                              <DropdownMenuItem>Edit Requisition</DropdownMenuItem>
                              <DropdownMenuItem>Approve</DropdownMenuItem>
                              <DropdownMenuItem>Generate PO</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Cancel Requisition</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        {/* Items List */}
                        <div className="mb-4">
                          <h4 className="text-sm font-medium text-gray-900 mb-2">Requested Items</h4>
                          <div className="space-y-2">
                            {requisition.items.map((item, index) => (
                              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div className="flex-1">
                                  <span className="font-medium text-gray-900">{item.name}</span>
                                  <div className="text-sm text-gray-500">
                                    {item.quantity} {item.unit} × {formatCurrency(item.unitPrice)}
                                  </div>
                                </div>
                                <div className="font-semibold text-gray-900">{formatCurrency(item.totalPrice)}</div>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Approval Workflow */}
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">Approval Workflow</h4>
                          <div className="flex items-center space-x-4">
                            {requisition.approvalWorkflow.map((step, index) => (
                              <div key={index} className="flex items-center space-x-2">
                                <div
                                  className={`w-3 h-3 rounded-full ${
                                    step.status === "Approved"
                                      ? "bg-green-500"
                                      : step.status === "Pending"
                                        ? "bg-yellow-500"
                                        : "bg-gray-300"
                                  }`}
                                />
                                <div className="text-sm">
                                  <div className="font-medium text-gray-900">{step.step}</div>
                                  <div className="text-gray-500">{step.approver}</div>
                                  {step.date && <div className="text-xs text-gray-400">{formatDate(step.date)}</div>}
                                </div>
                                {index < requisition.approvalWorkflow.length - 1 && (
                                  <div className="w-8 h-px bg-gray-300" />
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Purchase Orders Tab */}
          <TabsContent value="purchase-orders" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <ShoppingCart className="h-5 w-5 mr-2 text-blue-600" />
                    Purchase Orders
                  </CardTitle>
                  <Dialog open={isCreatePOOpen} onOpenChange={setIsCreatePOOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline">
                        <Plus className="h-4 w-4 mr-2" />
                        Create PO
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[600px]">
                      <DialogHeader>
                        <DialogTitle>Create Purchase Order</DialogTitle>
                        <DialogDescription>Generate a new purchase order from approved requisitions.</DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="vendor">Vendor</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select vendor" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="steel">Steel Dynamics Corp</SelectItem>
                                <SelectItem value="concrete">Concrete Solutions Ltd</SelectItem>
                                <SelectItem value="buildmart">BuildMart Supplies</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="project">Project</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select project" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="metro">Metro Tower Complex</SelectItem>
                                <SelectItem value="green">Green Valley Residential</SelectItem>
                                <SelectItem value="industrial">Industrial Park Phase 1</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="deliveryDate">Delivery Date</Label>
                            <Input id="deliveryDate" type="date" />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="paymentTerms">Payment Terms</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select terms" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="net15">Net 15 days</SelectItem>
                                <SelectItem value="net30">Net 30 days</SelectItem>
                                <SelectItem value="net45">Net 45 days</SelectItem>
                                <SelectItem value="advance">Advance Payment</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="terms">Terms & Conditions</Label>
                          <Textarea id="terms" placeholder="Enter terms and conditions..." />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button type="submit" className="bg-gradient-to-r from-green-600 to-blue-600">
                          Create Purchase Order
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {purchaseOrders.map((po) => (
                    <Card key={po.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h3 className="font-semibold text-gray-900">{po.id}</h3>
                              <Badge className={getStatusColor(po.status)}>{po.status}</Badge>
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500 mb-4">
                              <span>Vendor: {po.vendor}</span>
                              <span>Project: {po.project}</span>
                              <span>Order Date: {formatDate(po.date)}</span>
                              <span>Delivery: {formatDate(po.deliveryDate)}</span>
                            </div>
                            <div className="text-lg font-semibold text-gray-900 mb-4">
                              Total Value: {formatCurrency(po.totalValue)}
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>View Details</DropdownMenuItem>
                              <DropdownMenuItem>Edit PO</DropdownMenuItem>
                              <DropdownMenuItem>Track Delivery</DropdownMenuItem>
                              <DropdownMenuItem>Generate Invoice</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Cancel PO</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        {/* Items List */}
                        <div className="mb-4">
                          <h4 className="text-sm font-medium text-gray-900 mb-2">Order Items</h4>
                          <div className="space-y-2">
                            {po.items.map((item, index) => (
                              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div className="flex-1">
                                  <span className="font-medium text-gray-900">{item.name}</span>
                                  <div className="text-sm text-gray-500">
                                    {item.quantity} {item.unit} × {formatCurrency(item.unitPrice)}
                                  </div>
                                </div>
                                <div className="font-semibold text-gray-900">{formatCurrency(item.totalPrice)}</div>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Vendor Contact & Terms */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <h5 className="font-medium text-gray-900 mb-1">Vendor Contact</h5>
                            <div className="text-gray-600">
                              <div>{po.contactPerson}</div>
                              <div>{po.phone}</div>
                            </div>
                          </div>
                          <div>
                            <h5 className="font-medium text-gray-900 mb-1">Terms</h5>
                            <div className="text-gray-600">{po.terms}</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Vendors Tab */}
          <TabsContent value="vendors" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <Users className="h-5 w-5 mr-2 text-purple-600" />
                    Vendor Management
                  </CardTitle>
                  <Button variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Vendor
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {vendors.map((vendor) => (
                    <Card key={vendor.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h3 className="font-semibold text-gray-900">{vendor.name}</h3>
                              <Badge variant="outline">{vendor.category}</Badge>
                              <div className="flex items-center space-x-1">
                                <div className="text-yellow-500">★</div>
                                <span className="text-sm font-medium">{vendor.rating}</span>
                              </div>
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500 mb-4">
                              <span>Total Orders: {vendor.totalOrders}</span>
                              <span>Total Value: {formatCurrency(vendor.totalValue)}</span>
                              <span>On-time: {vendor.onTimeDelivery}%</span>
                              <span>Quality: {vendor.qualityScore}%</span>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>View Profile</DropdownMenuItem>
                              <DropdownMenuItem>Edit Vendor</DropdownMenuItem>
                              <DropdownMenuItem>Performance Report</DropdownMenuItem>
                              <DropdownMenuItem>Create PO</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Deactivate</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        {/* Performance Metrics */}
                        <div className="grid grid-cols-2 gap-4 mb-4">
                          <div>
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-sm text-gray-600">On-time Delivery</span>
                              <span className="text-sm font-medium">{vendor.onTimeDelivery}%</span>
                            </div>
                            <Progress value={vendor.onTimeDelivery} className="h-2" />
                          </div>
                          <div>
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-sm text-gray-600">Quality Score</span>
                              <span className="text-sm font-medium">{vendor.qualityScore}%</span>
                            </div>
                            <Progress value={vendor.qualityScore} className="h-2" />
                          </div>
                        </div>

                        {/* Contact Information */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <h5 className="font-medium text-gray-900 mb-1">Contact Information</h5>
                            <div className="text-gray-600">
                              <div>{vendor.contactPerson}</div>
                              <div>{vendor.phone}</div>
                              <div>{vendor.email}</div>
                              <div className="mt-1">{vendor.address}</div>
                            </div>
                          </div>
                          <div>
                            <h5 className="font-medium text-gray-900 mb-1">Business Details</h5>
                            <div className="text-gray-600">
                              <div>Payment Terms: {vendor.paymentTerms}</div>
                              <div className="mt-2">
                                <span className="font-medium">Certifications:</span>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {vendor.certifications.map((cert, index) => (
                                    <Badge key={index} variant="outline" className="text-xs">
                                      {cert}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Quotations Tab */}
          <TabsContent value="quotations" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <FileText className="h-5 w-5 mr-2 text-orange-600" />
                    Quotation Comparison
                  </CardTitle>
                  <Button variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Request Quotation
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {quotationComparisons.map((comparison) => (
                    <Card key={comparison.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <h3 className="font-semibold text-gray-900 mb-2">{comparison.item}</h3>
                            <div className="grid grid-cols-2 gap-4 text-sm text-gray-500 mb-4">
                              <span>Project: {comparison.project}</span>
                              <span>Request Date: {formatDate(comparison.requestDate)}</span>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>View Details</DropdownMenuItem>
                              <DropdownMenuItem>Generate PO</DropdownMenuItem>
                              <DropdownMenuItem>Request New Quotes</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Archive</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        {/* Quotation Comparison Table */}
                        <div className="mb-4">
                          <h4 className="text-sm font-medium text-gray-900 mb-2">Vendor Quotations</h4>
                          <div className="space-y-2">
                            {comparison.quotations.map((quote, index) => (
                              <div
                                key={index}
                                className={`p-4 rounded-lg border-2 ${
                                  quote.selected ? "border-green-500 bg-green-50" : "border-gray-200 bg-gray-50"
                                }`}
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex-1">
                                    <div className="flex items-center space-x-3 mb-2">
                                      <span className="font-medium text-gray-900">{quote.vendor}</span>
                                      <div className="flex items-center space-x-1">
                                        <div className="text-yellow-500">★</div>
                                        <span className="text-sm">{quote.rating}</span>
                                      </div>
                                      {quote.selected && (
                                        <Badge className="bg-green-100 text-green-800">Selected</Badge>
                                      )}
                                    </div>
                                    <div className="grid grid-cols-3 gap-4 text-sm">
                                      <div>
                                        <span className="text-gray-600">Price:</span>
                                        <div className="font-semibold">{formatCurrency(quote.price)}</div>
                                      </div>
                                      <div>
                                        <span className="text-gray-600">Delivery:</span>
                                        <div className="font-semibold">{quote.deliveryDays} days</div>
                                      </div>
                                      <div>
                                        <span className="text-gray-600">Rating:</span>
                                        <div className="font-semibold">{quote.rating}/5.0</div>
                                      </div>
                                    </div>
                                  </div>
                                  {!quote.selected && (
                                    <Button size="sm" variant="outline">
                                      Select
                                    </Button>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Selection Criteria */}
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">Selection Criteria</h4>
                          <div className="grid grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-gray-600">Price Weight:</span>
                              <div className="font-semibold">{comparison.criteria.price}%</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Delivery Weight:</span>
                              <div className="font-semibold">{comparison.criteria.delivery}%</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Quality Weight:</span>
                              <div className="font-semibold">{comparison.criteria.quality}%</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Relationship Weight:</span>
                              <div className="font-semibold">{comparison.criteria.relationship}%</div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Contracts Tab */}
          <TabsContent value="contracts" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-indigo-600" />
                  Contract Management
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-gray-500">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Contract management features coming soon</p>
                  <p className="text-sm">Track contracts, renewals, and compliance</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
