"""
URL configuration for enterprise_user_management project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi

# API Documentation Schema
schema_view = get_schema_view(
    openapi.Info(
        title="Enterprise User Management API",
        default_version='v1',
        description="Comprehensive API for enterprise user management system",
        terms_of_service="https://www.google.com/policies/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="MIT License"),
    ),
    public=True,
    permission_classes=[permissions.AllowAny],
)

urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),
    
    # API Documentation
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
    path('swagger.json', schema_view.without_ui(cache_timeout=0), name='schema-json'),
    
    # API v1 Routes
    path('api/v1/auth/', include('apps.users.urls.auth_urls')),
    path('api/v1/companies/', include('apps.companies.urls')),
    path('api/v1/users/', include('apps.users.urls.user_urls')),
    path('api/v1/roles/', include('apps.roles.urls')),
    path('api/v1/permissions/', include('apps.permissions.urls')),
    path('api/v1/locations/', include('apps.locations.urls')),
    path('api/v1/departments/', include('apps.departments.urls')),
    path('api/v1/teams/', include('apps.teams.urls')),
    path('api/v1/projects/', include('apps.projects.urls')),
    path('api/v1/audit/', include('apps.audit.urls')),
    path('api/v1/reports/', include('apps.reports.urls')),
    path('api/v1/workflows/', include('apps.workflows.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
