"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  ArrowRightLeft,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  User,
  MapPin,
  Package,
  FileText,
  Send,
  MoreHorizontal,
  Calendar,
  Shield,
  History,
  FileIcon as FileTemplate,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import Link from "next/link"

export default function AssetTransfersPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [isNewTransferOpen, setIsNewTransferOpen] = useState(false)
  const [selectedAsset, setSelectedAsset] = useState("")
  const [transferType, setTransferType] = useState("user")

  // Mock data for asset transfers
  const [transfers] = useState([
    {
      id: "TRF-001",
      assetId: "AST-001",
      assetName: "MacBook Pro 16-inch",
      transferType: "user",
      fromUser: "John Doe",
      toUser: "Sarah Wilson",
      fromLocation: "Office Floor 2",
      toLocation: "Office Floor 3",
      status: "pending_approval",
      priority: "normal",
      requestedBy: "John Doe",
      requestedDate: "2024-01-15",
      approver: "IT Manager",
      reason: "Employee relocation to new department",
      estimatedCompletion: "2024-01-20",
      comments: "Asset in excellent condition, includes original packaging",
      approvalSteps: [
        { step: 1, name: "Direct Manager", status: "approved", approver: "Mike Johnson", date: "2024-01-16" },
        { step: 2, name: "IT Manager", status: "pending", approver: "Lisa Chen", date: null },
        { step: 3, name: "Asset Manager", status: "pending", approver: "David Brown", date: null },
      ],
    },
    {
      id: "TRF-002",
      assetId: "AST-002",
      assetName: "Office Printer HP LaserJet",
      transferType: "location",
      fromUser: "IT Department",
      toUser: "IT Department",
      fromLocation: "Office Floor 1",
      toLocation: "Office Floor 4",
      status: "approved",
      priority: "high",
      requestedBy: "Lisa Chen",
      requestedDate: "2024-01-10",
      approver: "Asset Manager",
      reason: "New office setup requirements",
      estimatedCompletion: "2024-01-18",
      comments: "Requires technical setup after relocation",
      approvalSteps: [
        { step: 1, name: "IT Manager", status: "approved", approver: "Lisa Chen", date: "2024-01-11" },
        { step: 2, name: "Asset Manager", status: "approved", approver: "David Brown", date: "2024-01-12" },
      ],
    },
    {
      id: "TRF-003",
      assetId: "AST-003",
      assetName: "Conference Room Table",
      transferType: "location",
      fromUser: "Conference Room A",
      toUser: "Conference Room B",
      fromLocation: "Office Floor 3",
      toLocation: "Office Floor 2",
      status: "in_progress",
      priority: "low",
      requestedBy: "Facilities Team",
      requestedDate: "2024-01-08",
      approver: "Facilities Manager",
      reason: "Room renovation and space optimization",
      estimatedCompletion: "2024-01-25",
      comments: "Coordinate with facilities team for safe transport",
      approvalSteps: [
        { step: 1, name: "Facilities Manager", status: "approved", approver: "Tom Wilson", date: "2024-01-09" },
      ],
    },
    {
      id: "TRF-004",
      assetId: "AST-004",
      assetName: "Company Vehicle - Toyota Camry",
      transferType: "user",
      fromUser: "Sales Team",
      toUser: "Marketing Team",
      fromLocation: "Parking Lot",
      toLocation: "Parking Lot",
      status: "completed",
      priority: "normal",
      requestedBy: "Marketing Manager",
      requestedDate: "2024-01-05",
      approver: "Fleet Manager",
      reason: "Marketing team requires vehicle for client visits",
      estimatedCompletion: "2024-01-12",
      completedDate: "2024-01-12",
      comments: "Vehicle inspection completed, keys transferred",
      approvalSteps: [
        { step: 1, name: "Sales Manager", status: "approved", approver: "Jane Smith", date: "2024-01-06" },
        { step: 2, name: "Fleet Manager", status: "approved", approver: "Robert Lee", date: "2024-01-07" },
      ],
    },
    {
      id: "TRF-005",
      assetId: "AST-005",
      assetName: "Industrial 3D Printer",
      transferType: "user",
      fromUser: "Production Floor",
      toUser: "R&D Department",
      fromLocation: "Warehouse",
      toLocation: "Lab Floor 1",
      status: "rejected",
      priority: "high",
      requestedBy: "R&D Manager",
      requestedDate: "2024-01-03",
      approver: "Production Manager",
      reason: "Research project requirements",
      rejectionReason: "Asset currently critical for production schedule",
      comments: "Alternative equipment suggested for R&D use",
      approvalSteps: [
        { step: 1, name: "Production Manager", status: "rejected", approver: "Mark Davis", date: "2024-01-04" },
      ],
    },
  ])

  // Mock data for available assets
  const [availableAssets] = useState([
    { id: "AST-001", name: "MacBook Pro 16-inch", currentUser: "John Doe", location: "Office Floor 2" },
    { id: "AST-002", name: "Office Printer HP LaserJet", currentUser: "IT Department", location: "Office Floor 1" },
    { id: "AST-003", name: "Conference Room Table", currentUser: "Conference Room A", location: "Office Floor 3" },
    { id: "AST-004", name: "Company Vehicle - Toyota Camry", currentUser: "Sales Team", location: "Parking Lot" },
    { id: "AST-006", name: "Dell XPS 13", currentUser: "Sarah Wilson", location: "Office Floor 3" },
  ])

  const stats = [
    {
      title: "Active Transfers",
      value: "12",
      change: "+3",
      icon: ArrowRightLeft,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Pending Approvals",
      value: "8",
      change: "+2",
      icon: Clock,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
    {
      title: "Completed This Month",
      value: "45",
      change: "+12%",
      icon: CheckCircle,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Average Processing Time",
      value: "3.2 days",
      change: "-0.5",
      icon: Calendar,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
  ]

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case "pending_approval":
        return "bg-yellow-100 text-yellow-800"
      case "approved":
        return "bg-green-100 text-green-800"
      case "in_progress":
        return "bg-blue-100 text-blue-800"
      case "completed":
        return "bg-green-100 text-green-800"
      case "rejected":
        return "bg-red-100 text-red-800"
      case "cancelled":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority.toLowerCase()) {
      case "high":
        return "bg-red-100 text-red-800"
      case "normal":
        return "bg-blue-100 text-blue-800"
      case "low":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStepStatusIcon = (status) => {
    switch (status) {
      case "approved":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "rejected":
        return <XCircle className="h-4 w-4 text-red-600" />
      case "pending":
        return <Clock className="h-4 w-4 text-orange-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const filteredTransfers = transfers.filter((transfer) => {
    const matchesSearch =
      transfer.assetName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transfer.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transfer.fromUser.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transfer.toUser.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || transfer.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const handleNewTransfer = (e) => {
    e.preventDefault()
    // Handle transfer creation logic here
    setIsNewTransferOpen(false)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Asset Transfers</h1>
            <p className="text-gray-600 mt-1">Manage secure asset transfers with approval workflows</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" className="flex items-center bg-transparent">
              <History className="h-4 w-4 mr-2" />
              Transfer History
            </Button>
            <Link href="/dashboard/assets/templates">
              <Button variant="outline" className="flex items-center bg-transparent">
                <FileTemplate className="h-4 w-4 mr-2" />
                Manage Templates
              </Button>
            </Link>
            <Dialog open={isNewTransferOpen} onOpenChange={setIsNewTransferOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700">
                  <Plus className="h-4 w-4 mr-2" />
                  New Transfer
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Initiate Asset Transfer</DialogTitle>
                  <DialogDescription>Create a new asset transfer request with approval workflow</DialogDescription>
                </DialogHeader>
                <form onSubmit={handleNewTransfer}>
                  <div className="grid gap-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="asset-select">Select Asset</Label>
                      <Select value={selectedAsset} onValueChange={setSelectedAsset} required>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose asset to transfer" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableAssets.map((asset) => (
                            <SelectItem key={asset.id} value={asset.id}>
                              {asset.name} - {asset.currentUser}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="transfer-type">Transfer Type</Label>
                      <Select value={transferType} onValueChange={setTransferType} required>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="user">User Transfer</SelectItem>
                          <SelectItem value="location">Location Transfer</SelectItem>
                          <SelectItem value="both">User & Location Transfer</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    {(transferType === "user" || transferType === "both") && (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="from-user">From User</Label>
                          <Input id="from-user" placeholder="Current user" required />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="to-user">To User</Label>
                          <Input id="to-user" placeholder="New user" required />
                        </div>
                      </div>
                    )}
                    {(transferType === "location" || transferType === "both") && (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="from-location">From Location</Label>
                          <Input id="from-location" placeholder="Current location" required />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="to-location">To Location</Label>
                          <Input id="to-location" placeholder="New location" required />
                        </div>
                      </div>
                    )}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="priority">Priority</Label>
                        <Select required>
                          <SelectTrigger>
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="low">Low</SelectItem>
                            <SelectItem value="normal">Normal</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="completion-date">Expected Completion</Label>
                        <Input id="completion-date" type="date" required />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="reason">Transfer Reason</Label>
                      <Textarea id="reason" placeholder="Explain the reason for this transfer" required />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="comments">Additional Comments</Label>
                      <Textarea id="comments" placeholder="Any additional notes or special instructions" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="template-select">Use Template (Optional)</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a template to pre-fill form" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="TPL-001">Employee Onboarding - IT Equipment</SelectItem>
                        <SelectItem value="TPL-002">Department Relocation</SelectItem>
                        <SelectItem value="TPL-003">Equipment Upgrade Program</SelectItem>
                        <SelectItem value="TPL-004">Employee Offboarding</SelectItem>
                        <SelectItem value="TPL-005">Maintenance Transfer</SelectItem>
                        <SelectItem value="TPL-006">Emergency Asset Transfer</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setIsNewTransferOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" className="bg-gradient-to-r from-indigo-600 to-purple-600">
                      <Send className="h-4 w-4 mr-2" />
                      Submit Transfer Request
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className={`text-sm ${stat.change.startsWith("+") ? "text-green-600" : "text-red-600"}`}>
                      {stat.change} from last month
                    </p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content */}
        <Tabs defaultValue="active" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="active">Active Transfers</TabsTrigger>
            <TabsTrigger value="approvals">Pending Approvals</TabsTrigger>
            <TabsTrigger value="completed">Completed</TabsTrigger>
            <TabsTrigger value="audit">Audit Trail</TabsTrigger>
          </TabsList>

          <TabsContent value="active" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Active Asset Transfers</CardTitle>
                    <CardDescription>Monitor ongoing asset transfer requests and their status</CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                    <Button variant="outline" size="sm">
                      <Filter className="h-4 w-4 mr-2" />
                      Filter
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search transfers..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="pending_approval">Pending Approval</SelectItem>
                      <SelectItem value="approved">Approved</SelectItem>
                      <SelectItem value="in_progress">In Progress</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Transfer ID</TableHead>
                        <TableHead>Asset</TableHead>
                        <TableHead>Transfer Details</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Priority</TableHead>
                        <TableHead>Requested By</TableHead>
                        <TableHead>Expected Completion</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredTransfers.map((transfer) => (
                        <TableRow key={transfer.id}>
                          <TableCell>
                            <div className="font-medium text-indigo-600">{transfer.id}</div>
                            <div className="text-sm text-gray-500">{transfer.requestedDate}</div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <div className="bg-gradient-to-r from-indigo-100 to-purple-100 p-2 rounded-lg">
                                <Package className="h-4 w-4 text-indigo-600" />
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">{transfer.assetName}</div>
                                <div className="text-sm text-gray-500">{transfer.assetId}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="flex items-center space-x-2 text-sm">
                                <User className="h-3 w-3 text-gray-400" />
                                <span>
                                  {transfer.fromUser} → {transfer.toUser}
                                </span>
                              </div>
                              <div className="flex items-center space-x-2 text-sm">
                                <MapPin className="h-3 w-3 text-gray-400" />
                                <span>
                                  {transfer.fromLocation} → {transfer.toLocation}
                                </span>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(transfer.status)}>
                              {transfer.status.replace("_", " ").toUpperCase()}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={getPriorityColor(transfer.priority)} variant="outline">
                              {transfer.priority.toUpperCase()}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">{transfer.requestedBy}</div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">{transfer.estimatedCompletion}</div>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem asChild>
                                  <Link href={`/dashboard/assets/transfers/${transfer.id}`}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                  </Link>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <CheckCircle className="mr-2 h-4 w-4" />
                                  Approve Transfer
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <FileText className="mr-2 h-4 w-4" />
                                  View Audit Trail
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-red-600">
                                  <XCircle className="mr-2 h-4 w-4" />
                                  Cancel Transfer
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="approvals" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="h-5 w-5 mr-2 text-orange-600" />
                  Pending Approvals
                </CardTitle>
                <CardDescription>Review and approve asset transfer requests</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {transfers
                    .filter((transfer) => transfer.status === "pending_approval")
                    .map((transfer) => (
                      <div key={transfer.id} className="border rounded-lg p-6 space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className="bg-gradient-to-r from-indigo-100 to-purple-100 p-3 rounded-lg">
                              <ArrowRightLeft className="h-6 w-6 text-indigo-600" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900">{transfer.assetName}</h3>
                              <p className="text-sm text-gray-600">
                                {transfer.id} • Requested by {transfer.requestedBy}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge className={getPriorityColor(transfer.priority)} variant="outline">
                              {transfer.priority.toUpperCase()}
                            </Badge>
                            <Badge className={getStatusColor(transfer.status)}>PENDING APPROVAL</Badge>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <h4 className="font-medium text-gray-900">Transfer Details</h4>
                            <div className="space-y-1 text-sm text-gray-600">
                              <div className="flex items-center space-x-2">
                                <User className="h-4 w-4" />
                                <span>
                                  {transfer.fromUser} → {transfer.toUser}
                                </span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <MapPin className="h-4 w-4" />
                                <span>
                                  {transfer.fromLocation} → {transfer.toLocation}
                                </span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Calendar className="h-4 w-4" />
                                <span>Expected: {transfer.estimatedCompletion}</span>
                              </div>
                            </div>
                          </div>
                          <div className="space-y-2">
                            <h4 className="font-medium text-gray-900">Reason</h4>
                            <p className="text-sm text-gray-600">{transfer.reason}</p>
                            {transfer.comments && (
                              <div>
                                <h5 className="font-medium text-gray-900 text-sm">Comments</h5>
                                <p className="text-sm text-gray-600">{transfer.comments}</p>
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="space-y-3">
                          <h4 className="font-medium text-gray-900">Approval Workflow</h4>
                          <div className="flex items-center space-x-4">
                            {transfer.approvalSteps.map((step, index) => (
                              <div key={step.step} className="flex items-center space-x-2">
                                <div className="flex items-center space-x-2">
                                  {getStepStatusIcon(step.status)}
                                  <div className="text-sm">
                                    <div className="font-medium">{step.name}</div>
                                    <div className="text-gray-500">{step.approver}</div>
                                    {step.date && <div className="text-xs text-gray-400">{step.date}</div>}
                                  </div>
                                </div>
                                {index < transfer.approvalSteps.length - 1 && (
                                  <ArrowRightLeft className="h-4 w-4 text-gray-300" />
                                )}
                              </div>
                            ))}
                          </div>
                        </div>

                        <div className="flex items-center justify-end space-x-3 pt-4 border-t">
                          <Button
                            variant="outline"
                            className="text-red-600 border-red-200 hover:bg-red-50 bg-transparent"
                          >
                            <XCircle className="h-4 w-4 mr-2" />
                            Reject
                          </Button>
                          <Button className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800">
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Approve Transfer
                          </Button>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="completed" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                  Completed Transfers
                </CardTitle>
                <CardDescription>View successfully completed asset transfers</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {transfers
                    .filter((transfer) => transfer.status === "completed")
                    .map((transfer) => (
                      <div key={transfer.id} className="border rounded-lg p-4 bg-green-50 border-green-200">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <CheckCircle className="h-5 w-5 text-green-600" />
                            <div>
                              <div className="font-medium text-gray-900">{transfer.assetName}</div>
                              <div className="text-sm text-gray-600">
                                {transfer.id} • Completed on {transfer.completedDate}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm text-gray-600">
                              {transfer.fromUser} → {transfer.toUser}
                            </div>
                            <div className="text-sm text-gray-500">
                              {transfer.fromLocation} → {transfer.toLocation}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="audit" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-purple-600" />
                  Transfer Audit Trail
                </CardTitle>
                <CardDescription>Complete audit log of all transfer activities and changes</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3 p-4 border-l-4 border-l-blue-500 bg-blue-50">
                    <ArrowRightLeft className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">Transfer Request Created</div>
                      <div className="text-sm text-gray-600">
                        Transfer TRF-001 initiated for MacBook Pro 16-inch from John Doe to Sarah Wilson
                      </div>
                      <div className="text-xs text-gray-500 mt-1">January 15, 2024 10:30 AM • John Doe</div>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-4 border-l-4 border-l-green-500 bg-green-50">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">Approval Step 1 Completed</div>
                      <div className="text-sm text-gray-600">
                        Direct Manager (Mike Johnson) approved the transfer request
                      </div>
                      <div className="text-xs text-gray-500 mt-1">January 16, 2024 2:15 PM • Mike Johnson</div>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-4 border-l-4 border-l-orange-500 bg-orange-50">
                    <Clock className="h-5 w-5 text-orange-600 mt-0.5" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">Pending IT Manager Approval</div>
                      <div className="text-sm text-gray-600">
                        Transfer awaiting approval from IT Manager (Lisa Chen)
                      </div>
                      <div className="text-xs text-gray-500 mt-1">January 16, 2024 2:16 PM • System</div>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-4 border-l-4 border-l-red-500 bg-red-50">
                    <XCircle className="h-5 w-5 text-red-600 mt-0.5" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">Transfer Rejected</div>
                      <div className="text-sm text-gray-600">
                        Transfer TRF-005 rejected by Production Manager - Asset critical for production
                      </div>
                      <div className="text-xs text-gray-500 mt-1">January 4, 2024 11:45 AM • Mark Davis</div>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-4 border-l-4 border-l-purple-500 bg-purple-50">
                    <Package className="h-5 w-5 text-purple-600 mt-0.5" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">Asset Transfer Completed</div>
                      <div className="text-sm text-gray-600">
                        Company Vehicle successfully transferred from Sales Team to Marketing Team
                      </div>
                      <div className="text-xs text-gray-500 mt-1">January 12, 2024 4:30 PM • Robert Lee</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
