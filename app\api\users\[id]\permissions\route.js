import { NextResponse } from "next/server"
import { ROLE_PERMISSIONS } from "@/lib/permissions"

// Mock user permissions storage
const userPermissions = {
  1: {
    userId: "1",
    role: "project_manager",
    customPermissions: ["plan_approve_business_case"],
    appliedTemplates: ["template-1", "template-2"],
    lastUpdated: "2024-01-15T10:30:00Z",
  },
  2: {
    userId: "2",
    role: "portfolio_manager",
    customPermissions: [],
    appliedTemplates: ["template-3"],
    lastUpdated: "2024-01-14T16:45:00Z",
  },
}

export async function GET(request, { params }) {
  try {
    const userId = params.id
    const userPerms = userPermissions[userId]

    if (!userPerms) {
      return NextResponse.json({ success: false, error: "User permissions not found" }, { status: 404 })
    }

    // Get role-based permissions
    const rolePermissions = ROLE_PERMISSIONS[userPerms.role] || []

    // Combine role permissions with custom permissions
    const allPermissions = [...new Set([...rolePermissions, ...userPerms.customPermissions])]

    return NextResponse.json({
      success: true,
      data: {
        userId,
        role: userPerms.role,
        permissions: rolePermissions,
        customPermissions: userPerms.customPermissions,
        allPermissions,
        appliedTemplates: userPerms.appliedTemplates,
        lastUpdated: userPerms.lastUpdated,
      },
    })
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to fetch user permissions" }, { status: 500 })
  }
}

export async function PUT(request, { params }) {
  try {
    const userId = params.id
    const body = await request.json()
    const { customPermissions, role } = body

    if (!userPermissions[userId]) {
      userPermissions[userId] = {
        userId,
        role: role || "viewer",
        customPermissions: [],
        appliedTemplates: [],
        lastUpdated: new Date().toISOString(),
      }
    }

    // Update user permissions
    if (customPermissions !== undefined) {
      userPermissions[userId].customPermissions = customPermissions
    }

    if (role !== undefined) {
      userPermissions[userId].role = role
    }

    userPermissions[userId].lastUpdated = new Date().toISOString()

    // Get updated permissions
    const rolePermissions = ROLE_PERMISSIONS[userPermissions[userId].role] || []
    const allPermissions = [...new Set([...rolePermissions, ...userPermissions[userId].customPermissions])]

    return NextResponse.json({
      success: true,
      data: {
        userId,
        role: userPermissions[userId].role,
        permissions: rolePermissions,
        customPermissions: userPermissions[userId].customPermissions,
        allPermissions,
        appliedTemplates: userPermissions[userId].appliedTemplates,
        lastUpdated: userPermissions[userId].lastUpdated,
      },
      message: "User permissions updated successfully",
    })
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to update user permissions" }, { status: 500 })
  }
}

export async function POST(request, { params }) {
  try {
    const userId = params.id
    const body = await request.json()
    const { templateId, permissions } = body

    if (!userPermissions[userId]) {
      return NextResponse.json({ success: false, error: "User not found" }, { status: 404 })
    }

    // Add template to applied templates
    if (templateId) {
      if (!userPermissions[userId].appliedTemplates.includes(templateId)) {
        userPermissions[userId].appliedTemplates.push(templateId)
      }
    }

    // Add permissions from template
    if (permissions && Array.isArray(permissions)) {
      const newCustomPermissions = [...new Set([...userPermissions[userId].customPermissions, ...permissions])]
      userPermissions[userId].customPermissions = newCustomPermissions
    }

    userPermissions[userId].lastUpdated = new Date().toISOString()

    return NextResponse.json({
      success: true,
      data: userPermissions[userId],
      message: "Template applied to user successfully",
    })
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to apply template to user" }, { status: 500 })
  }
}
