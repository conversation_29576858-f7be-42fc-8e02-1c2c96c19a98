from django.db import models
from django.core.validators import RegexValidator
from django.utils import timezone
import uuid


class BaseModel(models.Model):
    """Base model with common fields for all models"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        'users.User', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='%(class)s_created'
    )
    updated_by = models.ForeignKey(
        'users.User', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='%(class)s_updated'
    )

    class Meta:
        abstract = True


class Company(BaseModel):
    """Company master model for multi-tenant support"""
    
    COMPANY_TYPES = [
        ('parent', 'Parent Company'),
        ('subsidiary', 'Subsidiary'),
        ('branch', 'Branch Office'),
        ('division', 'Division'),
    ]
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('suspended', 'Suspended'),
    ]
    
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=50, unique=True)
    legal_name = models.CharField(max_length=255)
    tax_id = models.CharField(max_length=50, unique=True)
    registration_number = models.CharField(max_length=100, blank=True)
    company_type = models.CharField(max_length=20, choices=COMPANY_TYPES, default='parent')
    parent_company = models.ForeignKey(
        'self', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        related_name='subsidiaries'
    )
    industry_type = models.CharField(max_length=100, blank=True)
    incorporation_date = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    
    # Address Information
    registered_address = models.TextField()
    operational_address = models.TextField(blank=True)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    country = models.CharField(max_length=100)
    postal_code = models.CharField(max_length=20)
    
    # Contact Information
    phone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    website = models.URLField(blank=True)
    
    # Business Information
    time_zone = models.CharField(max_length=50, default='UTC')
    currency = models.CharField(max_length=3, default='USD')
    fiscal_year_start = models.DateField(null=True, blank=True)
    
    # Branding
    logo = models.ImageField(upload_to='company_logos/', null=True, blank=True)
    
    class Meta:
        verbose_name_plural = 'Companies'
        ordering = ['name']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['status']),
            models.Index(fields=['company_type']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.code})"
    
    def get_all_subsidiaries(self):
        """Get all subsidiaries recursively"""
        subsidiaries = []
        for subsidiary in self.subsidiaries.all():
            subsidiaries.append(subsidiary)
            subsidiaries.extend(subsidiary.get_all_subsidiaries())
        return subsidiaries
    
    def is_parent_of(self, company):
        """Check if this company is a parent of another company"""
        if company.parent_company == self:
            return True
        if company.parent_company:
            return self.is_parent_of(company.parent_company)
        return False


class CompanySettings(BaseModel):
    """Company-specific settings and configurations"""
    
    company = models.OneToOneField(
        Company, 
        on_delete=models.CASCADE,
        related_name='settings'
    )
    
    # Security Settings
    password_min_length = models.IntegerField(default=8)
    password_require_uppercase = models.BooleanField(default=True)
    password_require_lowercase = models.BooleanField(default=True)
    password_require_numbers = models.BooleanField(default=True)
    password_require_special_chars = models.BooleanField(default=True)
    password_expiry_days = models.IntegerField(default=90)
    password_history_count = models.IntegerField(default=5)
    max_failed_login_attempts = models.IntegerField(default=5)
    account_lockout_duration = models.IntegerField(default=30)  # minutes
    
    # Session Settings
    session_timeout_minutes = models.IntegerField(default=60)
    max_concurrent_sessions = models.IntegerField(default=3)
    
    # User Settings
    user_auto_approval = models.BooleanField(default=False)
    role_assignment_approval_required = models.BooleanField(default=True)
    
    # Notification Settings
    email_notifications_enabled = models.BooleanField(default=True)
    sms_notifications_enabled = models.BooleanField(default=False)
    
    # Audit Settings
    audit_log_retention_days = models.IntegerField(default=365)
    detailed_audit_logging = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = 'Company Settings'
        verbose_name_plural = 'Company Settings'
    
    def __str__(self):
        return f"Settings for {self.company.name}"


class CostCenter(BaseModel):
    """Cost center model for budget and expense tracking"""
    
    COST_CENTER_TYPES = [
        ('revenue', 'Revenue Center'),
        ('cost', 'Cost Center'),
        ('profit', 'Profit Center'),
        ('investment', 'Investment Center'),
    ]
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    ]
    
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='cost_centers')
    code = models.CharField(max_length=50)
    name = models.CharField(max_length=255)
    cost_center_type = models.CharField(max_length=20, choices=COST_CENTER_TYPES)
    parent_cost_center = models.ForeignKey(
        'self', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        related_name='sub_cost_centers'
    )
    manager = models.ForeignKey(
        'users.User', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='managed_cost_centers'
    )
    budget_allocation = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    fiscal_year = models.IntegerField(default=timezone.now().year)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    description = models.TextField(blank=True)
    
    class Meta:
        unique_together = ['company', 'code']
        ordering = ['company', 'code']
        indexes = [
            models.Index(fields=['company', 'code']),
            models.Index(fields=['status']),
        ]
    
    def __str__(self):
        return f"{self.code} - {self.name}"


class Grade(BaseModel):
    """Employee grade/band model"""
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    ]
    
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='grades')
    code = models.CharField(max_length=20)
    name = models.CharField(max_length=100)
    level = models.IntegerField()  # Numeric level for hierarchy
    salary_min = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    salary_max = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    benefits_package = models.TextField(blank=True)
    promotion_criteria = models.TextField(blank=True)
    performance_rating_required = models.CharField(max_length=50, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    description = models.TextField(blank=True)
    
    class Meta:
        unique_together = ['company', 'code']
        ordering = ['company', 'level']
        indexes = [
            models.Index(fields=['company', 'level']),
            models.Index(fields=['status']),
        ]
    
    def __str__(self):
        return f"{self.code} - {self.name} (Level {self.level})"
