import { NextResponse } from "next/server"

const states = [
  { id: "1", country_id: "1", name: "California", code: "CA", is_active: true },
  { id: "2", country_id: "1", name: "New York", code: "NY", is_active: true },
  { id: "3", country_id: "1", name: "Texas", code: "TX", is_active: true },
  { id: "4", country_id: "2", name: "Maharashtra", code: "MH", is_active: true },
  { id: "5", country_id: "2", name: "Karnataka", code: "KA", is_active: true },
  { id: "6", country_id: "2", name: "Tamil Nadu", code: "TN", is_active: true },
]

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url)
    const countryId = searchParams.get("country_id")
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "10")

    let filteredStates = states

    if (countryId) {
      filteredStates = states.filter((state) => state.country_id === countryId)
    }

    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedStates = filteredStates.slice(startIndex, endIndex)

    return NextResponse.json({
      data: paginatedStates,
      pagination: {
        page,
        limit,
        total: filteredStates.length,
        totalPages: Math.ceil(filteredStates.length / limit),
      },
    })
  } catch (error) {
    return NextResponse.json({ error: "Failed to fetch states" }, { status: 500 })
  }
}

export async function POST(request) {
  try {
    const body = await request.json()
    const newState = {
      id: Date.now().toString(),
      ...body,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    states.push(newState)

    return NextResponse.json({ data: newState }, { status: 201 })
  } catch (error) {
    return NextResponse.json({ error: "Failed to create state" }, { status: 500 })
  }
}
