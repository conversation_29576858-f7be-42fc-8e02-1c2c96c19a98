"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { DollarSign, Bell, X, Settings, Volume2, VolumeX, Calculator, Receipt, Users, Calendar } from "lucide-react"

export default function PayrollNotifications() {
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      type: "payroll",
      title: "Payroll Processing Complete",
      message: "Monthly payroll for 247 employees has been successfully processed. Total amount: $485,230.",
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      read: false,
      priority: "high",
      category: "processing",
    },
    {
      id: 2,
      type: "payroll",
      title: "Tax Compliance Alert",
      message: "W-2 forms are due in 15 days. Please ensure all employee information is up to date.",
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      read: false,
      priority: "medium",
      category: "compliance",
    },
    {
      id: 3,
      type: "payroll",
      title: "Direct Deposit Confirmation",
      message: "Direct deposits for 234 employees have been successfully submitted to the bank.",
      timestamp: new Date(Date.now() - 60 * 60 * 1000),
      read: true,
      priority: "low",
      category: "payment",
    },
    {
      id: 4,
      type: "payroll",
      title: "Overtime Hours Alert",
      message: "15 employees have exceeded 40 hours this week. Review overtime approvals.",
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      read: false,
      priority: "medium",
      category: "hours",
    },
    {
      id: 5,
      type: "payroll",
      title: "Benefits Enrollment Reminder",
      message: "Open enrollment period ends in 7 days. 23 employees haven't completed enrollment.",
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
      read: false,
      priority: "medium",
      category: "benefits",
    },
  ])

  const [soundEnabled, setSoundEnabled] = useState(true)
  const [showToast, setShowToast] = useState(false)

  useEffect(() => {
    // Simulate real-time payroll notifications
    const interval = setInterval(() => {
      const payrollNotifications = [
        {
          title: "Payroll Calculation Updated",
          message: "Employee salary adjustments have been applied for next payroll cycle.",
          category: "processing",
        },
        {
          title: "Tax Rate Update",
          message: "Federal tax rates have been updated for the new quarter.",
          category: "compliance",
        },
        {
          title: "Timesheet Submission",
          message: "New timesheet submitted by employee requiring approval.",
          category: "hours",
        },
      ]

      const randomNotification = payrollNotifications[Math.floor(Math.random() * payrollNotifications.length)]

      const newNotification = {
        id: Date.now(),
        type: "payroll",
        title: randomNotification.title,
        message: randomNotification.message,
        timestamp: new Date(),
        read: false,
        priority: "medium",
        category: randomNotification.category,
      }

      setNotifications((prev) => [newNotification, ...prev])
      setShowToast(true)

      if (soundEnabled) {
        console.log("💰 Payroll notification sound played")
      }

      setTimeout(() => setShowToast(false), 5000)
    }, 45000) // New notification every 45 seconds

    return () => clearInterval(interval)
  }, [soundEnabled])

  const markAsRead = (id) => {
    setNotifications((prev) => prev.map((notif) => (notif.id === id ? { ...notif, read: true } : notif)))
  }

  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((notif) => ({ ...notif, read: true })))
  }

  const removeNotification = (id) => {
    setNotifications((prev) => prev.filter((notif) => notif.id !== id))
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "critical":
        return "bg-red-100 text-red-800 border-red-200"
      case "high":
        return "bg-orange-100 text-orange-800 border-orange-200"
      case "medium":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "low":
        return "bg-green-100 text-green-800 border-green-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getCategoryIcon = (category) => {
    switch (category) {
      case "processing":
        return <Calculator className="h-4 w-4 text-blue-600" />
      case "compliance":
        return <Receipt className="h-4 w-4 text-orange-600" />
      case "payment":
        return <DollarSign className="h-4 w-4 text-green-600" />
      case "hours":
        return <Calendar className="h-4 w-4 text-purple-600" />
      case "benefits":
        return <Users className="h-4 w-4 text-indigo-600" />
      default:
        return <Bell className="h-4 w-4 text-gray-600" />
    }
  }

  const formatTimestamp = (timestamp) => {
    const now = new Date()
    const diff = now - timestamp
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)

    if (minutes < 1) return "Just now"
    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    return `${days}d ago`
  }

  const unreadCount = notifications.filter((n) => !n.read).length

  return (
    <div className="space-y-4">
      {/* Toast Notification */}
      {showToast && (
        <div className="fixed top-4 right-4 z-50 animate-in slide-in-from-top-2">
          <Alert className="w-80 border-green-200 bg-green-50">
            <DollarSign className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">New payroll notification received!</AlertDescription>
          </Alert>
        </div>
      )}

      {/* Notification Panel */}
      <Card className="w-full max-w-md">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-green-600" />
              <CardTitle className="text-lg">Payroll Notifications</CardTitle>
              {unreadCount > 0 && <Badge className="bg-red-500 hover:bg-red-500 text-white">{unreadCount}</Badge>}
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" onClick={() => setSoundEnabled(!soundEnabled)} className="h-8 w-8 p-0">
                {soundEnabled ? (
                  <Volume2 className="h-4 w-4 text-green-600" />
                ) : (
                  <VolumeX className="h-4 w-4 text-gray-400" />
                )}
              </Button>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <CardDescription>Stay updated with payroll processing, compliance, and payment alerts</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">{unreadCount} unread notifications</span>
            {unreadCount > 0 && (
              <Button variant="ghost" size="sm" onClick={markAllAsRead}>
                Mark all as read
              </Button>
            )}
          </div>

          <div className="space-y-2 max-h-96 overflow-y-auto">
            {notifications.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <DollarSign className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                <p>No payroll notifications yet</p>
              </div>
            ) : (
              notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-3 rounded-lg border transition-all hover:shadow-sm ${
                    notification.read ? "bg-gray-50 border-gray-200" : "bg-white border-green-200 shadow-sm"
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className="mt-0.5">{getCategoryIcon(notification.category)}</div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4
                            className={`text-sm font-medium truncate ${
                              notification.read ? "text-gray-700" : "text-gray-900"
                            }`}
                          >
                            {notification.title}
                          </h4>
                          <Badge variant="outline" className={`text-xs ${getPriorityColor(notification.priority)}`}>
                            {notification.priority}
                          </Badge>
                        </div>
                        <p className={`text-sm mb-2 ${notification.read ? "text-gray-500" : "text-gray-700"}`}>
                          {notification.message}
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">{formatTimestamp(notification.timestamp)}</span>
                          <div className="flex items-center space-x-1">
                            {!notification.read && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => markAsRead(notification.id)}
                                className="h-6 px-2 text-xs text-green-600 hover:text-green-700"
                              >
                                Mark read
                              </Button>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeNotification(notification.id)}
                              className="h-6 w-6 p-0 text-gray-400 hover:text-red-600"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {notifications.length > 0 && (
            <div className="pt-3 border-t">
              <Button variant="outline" className="w-full bg-transparent" size="sm">
                <DollarSign className="h-4 w-4 mr-2" />
                View All Payroll Notifications
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
