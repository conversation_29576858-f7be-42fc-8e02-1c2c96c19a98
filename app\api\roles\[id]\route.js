import { NextResponse } from "next/server"

// Mock data - replace with actual database
const roles = [
  {
    id: "1",
    name: "admin",
    displayName: "Administrator",
    description: "Full system access with all permissions",
    permissions: ["*"],
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
]

export async function GET(request, { params }) {
  try {
    const role = roles.find((r) => r.id === params.id)

    if (!role) {
      return NextResponse.json({ success: false, error: "Role not found" }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: role,
    })
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to fetch role" }, { status: 500 })
  }
}

export async function PUT(request, { params }) {
  try {
    const body = await request.json()
    const roleIndex = roles.findIndex((r) => r.id === params.id)

    if (roleIndex === -1) {
      return NextResponse.json({ success: false, error: "Role not found" }, { status: 404 })
    }

    const updatedRole = {
      ...roles[roleIndex],
      ...body,
      updatedAt: new Date().toISOString(),
    }

    roles[roleIndex] = updatedRole

    return NextResponse.json({
      success: true,
      data: updatedRole,
      message: "Role updated successfully",
    })
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to update role" }, { status: 500 })
  }
}

export async function DELETE(request, { params }) {
  try {
    const roleIndex = roles.findIndex((r) => r.id === params.id)

    if (roleIndex === -1) {
      return NextResponse.json({ success: false, error: "Role not found" }, { status: 404 })
    }

    roles.splice(roleIndex, 1)

    return NextResponse.json({
      success: true,
      message: "Role deleted successfully",
    })
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to delete role" }, { status: 500 })
  }
}
