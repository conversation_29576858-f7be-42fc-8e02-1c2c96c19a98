import { NextResponse } from "next/server"

const leads = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "******-0201",
    company: "Tech Solutions Inc",
    source: "website",
    status: "new",
    assigned_to: "1",
    created_at: "2024-01-15T00:00:00Z",
    updated_at: "2024-01-15T00:00:00Z",
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "******-0202",
    company: "Enterprise Corp",
    source: "referral",
    status: "qualified",
    assigned_to: "2",
    created_at: "2024-01-16T00:00:00Z",
    updated_at: "2024-01-20T00:00:00Z",
  },
  {
    id: "3",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "******-0203",
    company: "Startup Ventures",
    source: "social_media",
    status: "contacted",
    assigned_to: "1",
    created_at: "2024-01-17T00:00:00Z",
    updated_at: "2024-01-18T00:00:00Z",
  },
  {
    id: "4",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "******-0204",
    company: "Manufacturing Ltd",
    source: "trade_show",
    status: "proposal",
    assigned_to: "3",
    created_at: "2024-01-18T00:00:00Z",
    updated_at: "2024-01-22T00:00:00Z",
  },
]

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url)
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "10")
    const search = searchParams.get("search") || ""
    const status = searchParams.get("status")
    const source = searchParams.get("source")
    const assigned_to = searchParams.get("assigned_to")

    let filteredLeads = leads

    if (search) {
      filteredLeads = leads.filter(
        (lead) =>
          lead.name.toLowerCase().includes(search.toLowerCase()) ||
          lead.email.toLowerCase().includes(search.toLowerCase()) ||
          lead.company.toLowerCase().includes(search.toLowerCase()),
      )
    }

    if (status) {
      filteredLeads = filteredLeads.filter((lead) => lead.status === status)
    }

    if (source) {
      filteredLeads = filteredLeads.filter((lead) => lead.source === source)
    }

    if (assigned_to) {
      filteredLeads = filteredLeads.filter((lead) => lead.assigned_to === assigned_to)
    }

    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedLeads = filteredLeads.slice(startIndex, endIndex)

    return NextResponse.json({
      data: paginatedLeads,
      pagination: {
        page,
        limit,
        total: filteredLeads.length,
        totalPages: Math.ceil(filteredLeads.length / limit),
      },
    })
  } catch (error) {
    return NextResponse.json({ error: "Failed to fetch leads" }, { status: 500 })
  }
}

export async function POST(request) {
  try {
    const body = await request.json()
    const newLead = {
      id: Date.now().toString(),
      ...body,
      status: body.status || "new",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    leads.push(newLead)

    return NextResponse.json({ data: newLead }, { status: 201 })
  } catch (error) {
    return NextResponse.json({ error: "Failed to create lead" }, { status: 500 })
  }
}
