"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  DollarSign,
  CreditCard,
  Receipt,
  TrendingUp,
  TrendingDown,
  Plus,
  Search,
  MoreHorizontal,
  FileText,
  Calculator,
  PieChart,
  BarChart3,
  Settings,
  Download,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"

export default function FinanceAccountsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedPeriod, setSelectedPeriod] = useState("current-month")
  const [isCreateVoucherOpen, setIsCreateVoucherOpen] = useState(false)
  const [isCreateBudgetOpen, setIsCreateBudgetOpen] = useState(false)

  const financeStats = [
    {
      title: "Total Revenue",
      value: "$4.2M",
      change: "+18% vs last month",
      icon: TrendingUp,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Total Expenses",
      value: "$3.1M",
      change: "+12% vs last month",
      icon: TrendingDown,
      color: "text-red-600",
      bgColor: "bg-red-100",
    },
    {
      title: "Net Profit",
      value: "$1.1M",
      change: "+25% vs last month",
      icon: DollarSign,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Outstanding Receivables",
      value: "$850K",
      change: "-8% vs last month",
      icon: Receipt,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
  ]

  const ledgerEntries = [
    {
      id: "LED001",
      date: "2024-01-24",
      voucherNo: "VOU001",
      account: "Steel Dynamics Corp",
      type: "Purchase",
      debit: 125000,
      credit: 0,
      balance: -125000,
      project: "Metro Tower Complex",
      description: "Steel beams purchase",
      gstAmount: 22500,
      status: "Posted",
    },
    {
      id: "LED002",
      date: "2024-01-23",
      voucherNo: "VOU002",
      account: "Metro Development Corp",
      type: "Sales",
      debit: 0,
      credit: 500000,
      balance: 500000,
      project: "Metro Tower Complex",
      description: "Progress billing - Phase 1",
      gstAmount: 90000,
      status: "Posted",
    },
    {
      id: "LED003",
      date: "2024-01-22",
      voucherNo: "VOU003",
      account: "Bank - HDFC Current",
      type: "Payment",
      debit: 0,
      credit: 85000,
      balance: -85000,
      project: "Green Valley Residential",
      description: "Contractor payment",
      gstAmount: 0,
      status: "Posted",
    },
  ]

  const budgets = [
    {
      id: "BUD001",
      project: "Metro Tower Complex",
      category: "Materials",
      budgeted: 2500000,
      actual: 1850000,
      variance: -650000,
      variancePercent: -26,
      period: "2024 Q1",
      status: "Under Budget",
    },
    {
      id: "BUD002",
      project: "Metro Tower Complex",
      category: "Labor",
      budgeted: 1200000,
      actual: 1350000,
      variance: 150000,
      variancePercent: 12.5,
      period: "2024 Q1",
      status: "Over Budget",
    },
    {
      id: "BUD003",
      project: "Green Valley Residential",
      category: "Equipment",
      budgeted: 800000,
      actual: 720000,
      variance: -80000,
      variancePercent: -10,
      period: "2024 Q1",
      status: "Under Budget",
    },
  ]

  const bankStatements = [
    {
      id: "BANK001",
      bank: "HDFC Bank - Current Account",
      accountNo: "****1234",
      date: "2024-01-24",
      description: "Metro Development Corp - Progress Payment",
      debit: 0,
      credit: 450000,
      balance: 2850000,
      reconciled: true,
      reference: "NEFT/********",
    },
    {
      id: "BANK002",
      bank: "HDFC Bank - Current Account",
      accountNo: "****1234",
      date: "2024-01-23",
      description: "Steel Dynamics Corp - Material Payment",
      debit: 125000,
      credit: 0,
      balance: 2400000,
      reconciled: true,
      reference: "RTGS/********",
    },
    {
      id: "BANK003",
      bank: "ICICI Bank - Savings Account",
      accountNo: "****5678",
      date: "2024-01-22",
      description: "Salary Transfer",
      debit: 185000,
      credit: 0,
      balance: 850000,
      reconciled: false,
      reference: "IMPS/********",
    },
  ]

  const gstReturns = [
    {
      id: "GST001",
      period: "January 2024",
      type: "GSTR-1",
      totalSales: 2500000,
      taxableValue: 2118644,
      igst: 190000,
      cgst: 95000,
      sgst: 95000,
      totalTax: 380000,
      status: "Filed",
      filedDate: "2024-02-10",
      dueDate: "2024-02-11",
    },
    {
      id: "GST002",
      period: "January 2024",
      type: "GSTR-3B",
      totalPurchases: 1800000,
      inputTaxCredit: 324000,
      outputTax: 380000,
      netTaxLiability: 56000,
      status: "Filed",
      filedDate: "2024-02-20",
      dueDate: "2024-02-20",
    },
  ]

  const vouchers = [
    {
      id: "VOU001",
      date: "2024-01-24",
      type: "Purchase Voucher",
      amount: 147500,
      party: "Steel Dynamics Corp",
      project: "Metro Tower Complex",
      description: "Steel beams and columns purchase",
      status: "Posted",
      createdBy: "John Accounts",
    },
    {
      id: "VOU002",
      date: "2024-01-23",
      type: "Sales Voucher",
      amount: 590000,
      party: "Metro Development Corp",
      project: "Metro Tower Complex",
      description: "Progress billing - Foundation complete",
      status: "Posted",
      createdBy: "Sarah Finance",
    },
    {
      id: "VOU003",
      date: "2024-01-22",
      type: "Payment Voucher",
      amount: 85000,
      party: "EcoConstruct Solutions",
      project: "Green Valley Residential",
      description: "Contractor payment for site preparation",
      status: "Posted",
      createdBy: "Mike Accounts",
    },
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case "Posted":
        return "bg-green-100 text-green-800"
      case "Draft":
        return "bg-yellow-100 text-yellow-800"
      case "Cancelled":
        return "bg-red-100 text-red-800"
      case "Filed":
        return "bg-blue-100 text-blue-800"
      case "Under Budget":
        return "bg-green-100 text-green-800"
      case "Over Budget":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getVarianceColor = (variance) => {
    if (variance > 0) return "text-red-600"
    if (variance < 0) return "text-green-600"
    return "text-gray-600"
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Finance & Accounts</h1>
            <p className="text-gray-600 mt-1">
              Comprehensive financial management with GST compliance and multi-site reporting
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Reports
            </Button>
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Dialog open={isCreateVoucherOpen} onOpenChange={setIsCreateVoucherOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700">
                  <Plus className="h-4 w-4 mr-2" />
                  New Voucher
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Create New Voucher</DialogTitle>
                  <DialogDescription>Create a new accounting voucher entry.</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="voucherType">Voucher Type</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="purchase">Purchase Voucher</SelectItem>
                          <SelectItem value="sales">Sales Voucher</SelectItem>
                          <SelectItem value="payment">Payment Voucher</SelectItem>
                          <SelectItem value="receipt">Receipt Voucher</SelectItem>
                          <SelectItem value="journal">Journal Voucher</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="voucherDate">Date</Label>
                      <Input id="voucherDate" type="date" />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="party">Party</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select party" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="steel-dynamics">Steel Dynamics Corp</SelectItem>
                          <SelectItem value="metro-dev">Metro Development Corp</SelectItem>
                          <SelectItem value="concrete-solutions">Concrete Solutions Ltd</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="project">Project</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select project" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="metro">Metro Tower Complex</SelectItem>
                          <SelectItem value="green">Green Valley Residential</SelectItem>
                          <SelectItem value="industrial">Industrial Park Phase 1</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="amount">Amount</Label>
                      <Input id="amount" type="number" placeholder="Enter amount" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="gstRate">GST Rate (%)</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select GST rate" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="0">0%</SelectItem>
                          <SelectItem value="5">5%</SelectItem>
                          <SelectItem value="12">12%</SelectItem>
                          <SelectItem value="18">18%</SelectItem>
                          <SelectItem value="28">28%</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea id="description" placeholder="Enter voucher description..." />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" className="bg-gradient-to-r from-blue-600 to-indigo-600">
                    Create Voucher
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {financeStats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-green-600">{stat.change}</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="ledger" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="ledger">General Ledger</TabsTrigger>
            <TabsTrigger value="vouchers">Vouchers</TabsTrigger>
            <TabsTrigger value="budget">Budget</TabsTrigger>
            <TabsTrigger value="bank">Bank Statements</TabsTrigger>
            <TabsTrigger value="gst">GST Returns</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>

          {/* General Ledger Tab */}
          <TabsContent value="ledger" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>General Ledger</CardTitle>
                  <div className="flex space-x-2">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search entries..."
                        className="pl-10 w-64"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                      <SelectTrigger className="w-40">
                        <SelectValue placeholder="Period" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="current-month">Current Month</SelectItem>
                        <SelectItem value="last-month">Last Month</SelectItem>
                        <SelectItem value="current-quarter">Current Quarter</SelectItem>
                        <SelectItem value="current-year">Current Year</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {ledgerEntries.map((entry) => (
                    <Card key={entry.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h4 className="font-semibold text-gray-900">{entry.account}</h4>
                              <Badge className={getStatusColor(entry.status)}>{entry.status}</Badge>
                              <Badge variant="outline">{entry.type}</Badge>
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500 mb-3">
                              <span>Date: {formatDate(entry.date)}</span>
                              <span>Voucher: {entry.voucherNo}</span>
                              <span>Project: {entry.project}</span>
                              <span>GST: {formatCurrency(entry.gstAmount)}</span>
                            </div>
                            <p className="text-sm text-gray-600 mb-3">{entry.description}</p>
                            <div className="grid grid-cols-4 gap-4 text-sm">
                              <div>
                                <span className="text-gray-600">Debit</span>
                                <div className="font-semibold text-red-600">
                                  {entry.debit > 0 ? formatCurrency(entry.debit) : "-"}
                                </div>
                              </div>
                              <div>
                                <span className="text-gray-600">Credit</span>
                                <div className="font-semibold text-green-600">
                                  {entry.credit > 0 ? formatCurrency(entry.credit) : "-"}
                                </div>
                              </div>
                              <div>
                                <span className="text-gray-600">Balance</span>
                                <div
                                  className={`font-semibold ${entry.balance > 0 ? "text-green-600" : "text-red-600"}`}
                                >
                                  {formatCurrency(Math.abs(entry.balance))}
                                </div>
                              </div>
                              <div>
                                <span className="text-gray-600">GST Amount</span>
                                <div className="font-semibold">{formatCurrency(entry.gstAmount)}</div>
                              </div>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>View Details</DropdownMenuItem>
                              <DropdownMenuItem>Edit Entry</DropdownMenuItem>
                              <DropdownMenuItem>Print Voucher</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Reverse Entry</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Vouchers Tab */}
          <TabsContent value="vouchers" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <FileText className="h-5 w-5 mr-2 text-blue-600" />
                    Voucher Management
                  </CardTitle>
                  <Button variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Voucher
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {vouchers.map((voucher) => (
                    <Card key={voucher.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h4 className="font-semibold text-gray-900">{voucher.id}</h4>
                              <Badge className={getStatusColor(voucher.status)}>{voucher.status}</Badge>
                              <Badge variant="outline">{voucher.type}</Badge>
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500 mb-3">
                              <span>Date: {formatDate(voucher.date)}</span>
                              <span>Party: {voucher.party}</span>
                              <span>Project: {voucher.project}</span>
                              <span>Created by: {voucher.createdBy}</span>
                            </div>
                            <p className="text-sm text-gray-600 mb-3">{voucher.description}</p>
                            <div className="text-lg font-semibold text-gray-900">
                              Amount: {formatCurrency(voucher.amount)}
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>View Voucher</DropdownMenuItem>
                              <DropdownMenuItem>Edit Voucher</DropdownMenuItem>
                              <DropdownMenuItem>Print Voucher</DropdownMenuItem>
                              <DropdownMenuItem>Duplicate</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Cancel Voucher</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Budget Tab */}
          <TabsContent value="budget" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <Calculator className="h-5 w-5 mr-2 text-green-600" />
                    Budget Management
                  </CardTitle>
                  <Dialog open={isCreateBudgetOpen} onOpenChange={setIsCreateBudgetOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline">
                        <Plus className="h-4 w-4 mr-2" />
                        Create Budget
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[525px]">
                      <DialogHeader>
                        <DialogTitle>Create Budget</DialogTitle>
                        <DialogDescription>Set up a new budget for project or category.</DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="budgetProject">Project</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select project" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="metro">Metro Tower Complex</SelectItem>
                                <SelectItem value="green">Green Valley Residential</SelectItem>
                                <SelectItem value="industrial">Industrial Park Phase 1</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="budgetCategory">Category</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select category" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="materials">Materials</SelectItem>
                                <SelectItem value="labor">Labor</SelectItem>
                                <SelectItem value="equipment">Equipment</SelectItem>
                                <SelectItem value="overhead">Overhead</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="budgetAmount">Budget Amount</Label>
                            <Input id="budgetAmount" type="number" placeholder="Enter budget amount" />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="budgetPeriod">Period</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select period" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="2024-q1">2024 Q1</SelectItem>
                                <SelectItem value="2024-q2">2024 Q2</SelectItem>
                                <SelectItem value="2024-q3">2024 Q3</SelectItem>
                                <SelectItem value="2024-q4">2024 Q4</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </div>
                      <DialogFooter>
                        <Button type="submit" className="bg-gradient-to-r from-green-600 to-teal-600">
                          Create Budget
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {budgets.map((budget) => (
                    <Card key={budget.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h4 className="font-semibold text-gray-900">
                                {budget.project} - {budget.category}
                              </h4>
                              <Badge className={getStatusColor(budget.status)}>{budget.status}</Badge>
                            </div>
                            <div className="text-sm text-gray-500 mb-4">Period: {budget.period}</div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>View Details</DropdownMenuItem>
                              <DropdownMenuItem>Edit Budget</DropdownMenuItem>
                              <DropdownMenuItem>Budget Report</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Archive Budget</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        {/* Budget Progress */}
                        <div className="mb-4">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-gray-600">Budget Utilization</span>
                            <span className="text-sm font-medium">
                              {((budget.actual / budget.budgeted) * 100).toFixed(1)}%
                            </span>
                          </div>
                          <Progress value={(budget.actual / budget.budgeted) * 100} className="h-2" />
                        </div>

                        {/* Budget Details */}
                        <div className="grid grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Budgeted</span>
                            <div className="font-semibold text-blue-600">{formatCurrency(budget.budgeted)}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Actual</span>
                            <div className="font-semibold text-gray-900">{formatCurrency(budget.actual)}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Variance</span>
                            <div className={`font-semibold ${getVarianceColor(budget.variance)}`}>
                              {formatCurrency(Math.abs(budget.variance))}
                            </div>
                          </div>
                          <div>
                            <span className="text-gray-600">Variance %</span>
                            <div className={`font-semibold ${getVarianceColor(budget.variance)}`}>
                              {budget.variancePercent > 0 ? "+" : ""}
                              {budget.variancePercent}%
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Bank Statements Tab */}
          <TabsContent value="bank" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <CreditCard className="h-5 w-5 mr-2 text-purple-600" />
                    Bank Statements
                  </CardTitle>
                  <Button variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Import Statement
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {bankStatements.map((statement) => (
                    <Card key={statement.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h4 className="font-semibold text-gray-900">{statement.bank}</h4>
                              <Badge
                                className={
                                  statement.reconciled ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"
                                }
                              >
                                {statement.reconciled ? "Reconciled" : "Pending"}
                              </Badge>
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500 mb-3">
                              <span>Account: {statement.accountNo}</span>
                              <span>Date: {formatDate(statement.date)}</span>
                              <span>Reference: {statement.reference}</span>
                              <span>Balance: {formatCurrency(statement.balance)}</span>
                            </div>
                            <p className="text-sm text-gray-600 mb-3">{statement.description}</p>
                            <div className="grid grid-cols-3 gap-4 text-sm">
                              <div>
                                <span className="text-gray-600">Debit</span>
                                <div className="font-semibold text-red-600">
                                  {statement.debit > 0 ? formatCurrency(statement.debit) : "-"}
                                </div>
                              </div>
                              <div>
                                <span className="text-gray-600">Credit</span>
                                <div className="font-semibold text-green-600">
                                  {statement.credit > 0 ? formatCurrency(statement.credit) : "-"}
                                </div>
                              </div>
                              <div>
                                <span className="text-gray-600">Balance</span>
                                <div className="font-semibold text-blue-600">{formatCurrency(statement.balance)}</div>
                              </div>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>View Details</DropdownMenuItem>
                              <DropdownMenuItem>Reconcile</DropdownMenuItem>
                              <DropdownMenuItem>Match Transaction</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Mark as Reconciled</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* GST Returns Tab */}
          <TabsContent value="gst" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <Receipt className="h-5 w-5 mr-2 text-orange-600" />
                    GST Returns & Compliance
                  </CardTitle>
                  <Button variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    File Return
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {gstReturns.map((gstReturn) => (
                    <Card key={gstReturn.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h4 className="font-semibold text-gray-900">
                                {gstReturn.type} - {gstReturn.period}
                              </h4>
                              <Badge className={getStatusColor(gstReturn.status)}>{gstReturn.status}</Badge>
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500 mb-4">
                              <span>Filed: {formatDate(gstReturn.filedDate)}</span>
                              <span>Due: {formatDate(gstReturn.dueDate)}</span>
                              <span>Type: {gstReturn.type}</span>
                              <span>Period: {gstReturn.period}</span>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>View Return</DropdownMenuItem>
                              <DropdownMenuItem>Download PDF</DropdownMenuItem>
                              <DropdownMenuItem>Amend Return</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>File Revised Return</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        {/* GST Details */}
                        {gstReturn.type === "GSTR-1" ? (
                          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                            <div>
                              <span className="text-gray-600">Total Sales</span>
                              <div className="font-semibold">{formatCurrency(gstReturn.totalSales)}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Taxable Value</span>
                              <div className="font-semibold">{formatCurrency(gstReturn.taxableValue)}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">IGST</span>
                              <div className="font-semibold text-blue-600">{formatCurrency(gstReturn.igst)}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">CGST</span>
                              <div className="font-semibold text-green-600">{formatCurrency(gstReturn.cgst)}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">SGST</span>
                              <div className="font-semibold text-purple-600">{formatCurrency(gstReturn.sgst)}</div>
                            </div>
                          </div>
                        ) : (
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-gray-600">Total Purchases</span>
                              <div className="font-semibold">{formatCurrency(gstReturn.totalPurchases)}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Input Tax Credit</span>
                              <div className="font-semibold text-green-600">
                                {formatCurrency(gstReturn.inputTaxCredit)}
                              </div>
                            </div>
                            <div>
                              <span className="text-gray-600">Output Tax</span>
                              <div className="font-semibold text-red-600">{formatCurrency(gstReturn.outputTax)}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Net Tax Liability</span>
                              <div className="font-semibold text-orange-600">
                                {formatCurrency(gstReturn.netTaxLiability)}
                              </div>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Reports Tab */}
          <TabsContent value="reports" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <BarChart3 className="h-5 w-5 mr-2 text-blue-600" />
                    Profit & Loss
                  </CardTitle>
                  <CardDescription>Comprehensive P&L statement with project-wise breakdown</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Revenue</span>
                      <span className="font-semibold text-green-600">{formatCurrency(4200000)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Expenses</span>
                      <span className="font-semibold text-red-600">{formatCurrency(3100000)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Net Profit</span>
                      <span className="font-semibold text-blue-600">{formatCurrency(1100000)}</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <PieChart className="h-5 w-5 mr-2 text-green-600" />
                    Balance Sheet
                  </CardTitle>
                  <CardDescription>Assets, liabilities, and equity statement</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Assets</span>
                      <span className="font-semibold">{formatCurrency(8500000)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Liabilities</span>
                      <span className="font-semibold text-red-600">{formatCurrency(3200000)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Equity</span>
                      <span className="font-semibold text-green-600">{formatCurrency(5300000)}</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <TrendingUp className="h-5 w-5 mr-2 text-purple-600" />
                    Cash Flow
                  </CardTitle>
                  <CardDescription>Operating, investing, and financing activities</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Operating Cash Flow</span>
                      <span className="font-semibold text-green-600">{formatCurrency(950000)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Investing Cash Flow</span>
                      <span className="font-semibold text-red-600">{formatCurrency(-450000)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Net Cash Flow</span>
                      <span className="font-semibold text-blue-600">{formatCurrency(500000)}</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
