"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  UserCheck,
  Mail,
  Phone,
  Calendar,
  Star,
  MapPin,
  Building,
  Globe,
  ArrowLeft,
  Edit,
  Plus,
  Activity,
  DollarSign,
  Clock,
  Target,
  FileText,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import Link from "next/link"

export default function LeadDetailPage({ params }) {
  const [isEditOpen, setIsEditOpen] = useState(false)
  const [isAddNoteOpen, setIsAddNoteOpen] = useState(false)
  const [isScheduleMeetingOpen, setIsScheduleMeetingOpen] = useState(false)

  // Mock lead data - in real app, fetch based on params.id
  const lead = {
    id: "LD001",
    name: "Sarah Johnson",
    email: "<EMAIL>",
    phone: "+****************",
    company: "TechCorp Solutions",
    position: "VP of Technology",
    website: "https://techcorp.com",
    address: "123 Tech Street, San Francisco, CA 94105",
    source: "Website",
    stage: "Qualified",
    score: 85,
    value: 25000,
    status: "Hot",
    assignedTo: "John Smith",
    createdDate: "2024-01-15",
    lastActivity: "2 hours ago",
    avatar: "/placeholder.svg?height=80&width=80",
  }

  const activities = [
    {
      id: 1,
      type: "email",
      title: "Sent product demo email",
      description: "Shared comprehensive product demo video and pricing information",
      timestamp: "2024-01-20 14:30",
      user: "John Smith",
    },
    {
      id: 2,
      type: "call",
      title: "Discovery call completed",
      description: "45-minute call to understand their current tech stack and pain points",
      timestamp: "2024-01-19 10:00",
      user: "John Smith",
    },
    {
      id: 3,
      type: "meeting",
      title: "Initial meeting scheduled",
      description: "Scheduled follow-up meeting for next week to discuss implementation",
      timestamp: "2024-01-18 16:15",
      user: "John Smith",
    },
    {
      id: 4,
      type: "note",
      title: "Lead qualification completed",
      description: "Confirmed budget authority and timeline. Strong fit for our enterprise solution",
      timestamp: "2024-01-17 11:20",
      user: "John Smith",
    },
  ]

  const notes = [
    {
      id: 1,
      content: "Very interested in our enterprise solution. Budget approved for Q1 implementation.",
      author: "John Smith",
      timestamp: "2024-01-20 09:15",
    },
    {
      id: 2,
      content: "Technical team is evaluating multiple solutions. Need to emphasize our integration capabilities.",
      author: "John Smith",
      timestamp: "2024-01-19 14:30",
    },
    {
      id: 3,
      content: "Decision maker confirmed. Sarah has full authority to make purchasing decisions.",
      author: "John Smith",
      timestamp: "2024-01-18 16:45",
    },
  ]

  const deals = [
    {
      id: "DL001",
      name: "TechCorp Enterprise License",
      value: 25000,
      stage: "Proposal",
      probability: 75,
      closeDate: "2024-02-15",
      status: "Active",
    },
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case "Hot":
        return "bg-red-100 text-red-800"
      case "Warm":
        return "bg-orange-100 text-orange-800"
      case "Cold":
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStageColor = (stage) => {
    switch (stage) {
      case "Lead":
        return "bg-gray-100 text-gray-800"
      case "Qualified":
        return "bg-blue-100 text-blue-800"
      case "Proposal":
        return "bg-yellow-100 text-yellow-800"
      case "Negotiation":
        return "bg-orange-100 text-orange-800"
      case "Closed Won":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getActivityIcon = (type) => {
    switch (type) {
      case "email":
        return <Mail className="h-4 w-4 text-blue-600" />
      case "call":
        return <Phone className="h-4 w-4 text-green-600" />
      case "meeting":
        return <Calendar className="h-4 w-4 text-purple-600" />
      case "note":
        return <FileText className="h-4 w-4 text-orange-600" />
      default:
        return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/customers">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to CRM
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Lead Profile</h1>
              <p className="text-gray-600 mt-1">Detailed information and activity history for {lead.name}</p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Mail className="h-4 w-4 mr-2" />
              Send Email
            </Button>
            <Button variant="outline">
              <Phone className="h-4 w-4 mr-2" />
              Call Lead
            </Button>
            <Dialog open={isScheduleMeetingOpen} onOpenChange={setIsScheduleMeetingOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                  <Calendar className="h-4 w-4 mr-2" />
                  Schedule Meeting
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Schedule Meeting</DialogTitle>
                  <DialogDescription>Schedule a meeting with {lead.name}</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="title" className="text-right">
                      Title
                    </Label>
                    <Input id="title" placeholder="Meeting title" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="date" className="text-right">
                      Date
                    </Label>
                    <Input id="date" type="date" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="time" className="text-right">
                      Time
                    </Label>
                    <Input id="time" type="time" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-start gap-4">
                    <Label htmlFor="agenda" className="text-right mt-2">
                      Agenda
                    </Label>
                    <Textarea id="agenda" placeholder="Meeting agenda..." className="col-span-3" />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" className="bg-gradient-to-r from-blue-600 to-purple-600">
                    Schedule Meeting
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Lead Overview Card */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-6">
              <Avatar className="h-20 w-20">
                <AvatarImage src={lead.avatar || "/placeholder.svg"} alt={lead.name} />
                <AvatarFallback className="text-lg">
                  {lead.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h2 className="text-2xl font-bold text-gray-900">{lead.name}</h2>
                  <Badge className={getStatusColor(lead.status)}>{lead.status}</Badge>
                  <Badge className={getStageColor(lead.stage)}>{lead.stage}</Badge>
                </div>
                <p className="text-lg text-gray-600 mb-2">
                  {lead.position} at {lead.company}
                </p>
                <div className="flex items-center space-x-6 text-sm text-gray-500">
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 mr-1" />
                    {lead.email}
                  </div>
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 mr-1" />
                    {lead.phone}
                  </div>
                  <div className="flex items-center">
                    <Globe className="h-4 w-4 mr-1" />
                    {lead.website}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center justify-end space-x-2 mb-2">
                  <span className="text-sm text-gray-600">Lead Score</span>
                  <div className="flex items-center space-x-1">
                    <span className="text-2xl font-bold text-green-600">{lead.score}</span>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${
                            i < Math.floor(lead.score / 20) ? "text-yellow-400 fill-current" : "text-gray-300"
                          }`}
                        />
                      ))}
                    </div>
                  </div>
                </div>
                <div className="text-sm text-gray-600">Potential Value</div>
                <div className="text-2xl font-bold text-purple-600">{formatCurrency(lead.value)}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Days in Pipeline</p>
                  <p className="text-2xl font-bold text-gray-900">12</p>
                </div>
                <Clock className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Activities</p>
                  <p className="text-2xl font-bold text-gray-900">{activities.length}</p>
                </div>
                <Activity className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Open Deals</p>
                  <p className="text-2xl font-bold text-gray-900">{deals.length}</p>
                </div>
                <Target className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Assigned To</p>
                  <p className="text-lg font-bold text-gray-900">{lead.assignedTo}</p>
                </div>
                <UserCheck className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="activities">Activities</TabsTrigger>
            <TabsTrigger value="notes">Notes</TabsTrigger>
            <TabsTrigger value="deals">Deals</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center">
                      <Building className="h-5 w-5 mr-2 text-blue-600" />
                      Contact Information
                    </CardTitle>
                    <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
                      <DialogTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[425px]">
                        <DialogHeader>
                          <DialogTitle>Edit Lead Information</DialogTitle>
                          <DialogDescription>Update the lead's contact and company information.</DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="editName" className="text-right">
                              Name
                            </Label>
                            <Input id="editName" defaultValue={lead.name} className="col-span-3" />
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="editEmail" className="text-right">
                              Email
                            </Label>
                            <Input id="editEmail" defaultValue={lead.email} className="col-span-3" />
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="editPhone" className="text-right">
                              Phone
                            </Label>
                            <Input id="editPhone" defaultValue={lead.phone} className="col-span-3" />
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="editCompany" className="text-right">
                              Company
                            </Label>
                            <Input id="editCompany" defaultValue={lead.company} className="col-span-3" />
                          </div>
                        </div>
                        <DialogFooter>
                          <Button type="submit">Save Changes</Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <span>{lead.email}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <span>{lead.phone}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Building className="h-4 w-4 text-gray-400" />
                      <span>{lead.company}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Globe className="h-4 w-4 text-gray-400" />
                      <a href={lead.website} className="text-blue-600 hover:underline">
                        {lead.website}
                      </a>
                    </div>
                    <div className="flex items-start space-x-3">
                      <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                      <span className="text-sm">{lead.address}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Target className="h-5 w-5 mr-2 text-purple-600" />
                    Lead Details
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Source</span>
                      <Badge variant="outline">{lead.source}</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Stage</span>
                      <Badge className={getStageColor(lead.stage)}>{lead.stage}</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Status</span>
                      <Badge className={getStatusColor(lead.status)}>{lead.status}</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Potential Value</span>
                      <span className="font-semibold">{formatCurrency(lead.value)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Created Date</span>
                      <span>{formatDate(lead.createdDate)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Last Activity</span>
                      <span>{lead.lastActivity}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Activities Tab */}
          <TabsContent value="activities" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <Activity className="h-5 w-5 mr-2 text-orange-600" />
                    Activity Timeline
                  </CardTitle>
                  <Button variant="outline" size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Log Activity
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {activities.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-4 p-4 border rounded-lg">
                      <div className="p-2 bg-gray-100 rounded-lg">{getActivityIcon(activity.type)}</div>
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{activity.title}</div>
                        <div className="text-sm text-gray-600 mt-1">{activity.description}</div>
                        <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                          <span>{activity.timestamp}</span>
                          <span>By: {activity.user}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Notes Tab */}
          <TabsContent value="notes" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <FileText className="h-5 w-5 mr-2 text-blue-600" />
                    Lead Notes
                  </CardTitle>
                  <Dialog open={isAddNoteOpen} onOpenChange={setIsAddNoteOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Plus className="h-4 w-4 mr-2" />
                        Add Note
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[525px]">
                      <DialogHeader>
                        <DialogTitle>Add Note</DialogTitle>
                        <DialogDescription>Add a note about this lead for future reference.</DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="space-y-2">
                          <Label htmlFor="note">Note</Label>
                          <Textarea
                            id="note"
                            placeholder="Enter your note about this lead..."
                            className="min-h-[120px]"
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button type="submit" className="bg-gradient-to-r from-blue-600 to-purple-600">
                          Save Note
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {notes.map((note) => (
                    <div key={note.id} className="p-4 border rounded-lg bg-gray-50">
                      <div className="text-sm text-gray-900 mb-2">{note.content}</div>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>By: {note.author}</span>
                        <span>{note.timestamp}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Deals Tab */}
          <TabsContent value="deals" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <DollarSign className="h-5 w-5 mr-2 text-green-600" />
                    Associated Deals
                  </CardTitle>
                  <Button className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Deal
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {deals.map((deal) => (
                    <div key={deal.id} className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-900">{deal.name}</h4>
                        <Badge className={getStageColor(deal.stage)}>{deal.stage}</Badge>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Value:</span>
                          <div className="font-semibold text-green-600">{formatCurrency(deal.value)}</div>
                        </div>
                        <div>
                          <span className="text-gray-600">Probability:</span>
                          <div className="font-semibold">{deal.probability}%</div>
                        </div>
                        <div>
                          <span className="text-gray-600">Close Date:</span>
                          <div className="font-semibold">{formatDate(deal.closeDate)}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Documents Tab */}
          <TabsContent value="documents" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <FileText className="h-5 w-5 mr-2 text-purple-600" />
                    Documents & Files
                  </CardTitle>
                  <Button variant="outline" size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Upload Document
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-gray-500">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No documents uploaded yet</p>
                  <p className="text-sm">Upload proposals, contracts, and other files related to this lead</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
