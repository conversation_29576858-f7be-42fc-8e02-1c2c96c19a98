"use client"

import { useState } from "react"
import MobileLayout from "@/components/mobile-layout"
import PushNotifications from "@/components/push-notifications"
import AnalyticsNotifications from "@/components/analytics-notifications"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Bell, Settings, Filter, BookMarkedIcon as MarkAsRead } from "lucide-react"

export default function NotificationsPage() {
  const [allNotifications] = useState([
    {
      id: 1,
      type: "system",
      title: "System Maintenance Scheduled",
      message: "Scheduled maintenance tonight from 2:00 AM - 4:00 AM EST",
      time: "5 minutes ago",
      read: false,
      priority: "high",
    },
    {
      id: 2,
      type: "user",
      title: "New User Registration",
      message: "<PERSON> has registered and requires approval",
      time: "15 minutes ago",
      read: false,
      priority: "medium",
    },
    {
      id: 3,
      type: "ticket",
      title: "High Priority Ticket",
      message: "Critical database issue reported by multiple users",
      time: "1 hour ago",
      read: true,
      priority: "critical",
    },
    {
      id: 4,
      type: "payroll",
      title: "Payroll Processing Complete",
      message: "Monthly payroll processed for 45 employees - $125,430",
      time: "2 hours ago",
      read: true,
      priority: "low",
    },
  ])

  const unreadCount = allNotifications.filter((n) => !n.read).length

  return (
    <MobileLayout>
      <div className="space-y-6 pb-20">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Notifications</h1>
            <p className="text-gray-600">Stay updated with real-time alerts</p>
          </div>
          {unreadCount > 0 && <Badge className="bg-red-500 hover:bg-red-500">{unreadCount} unread</Badge>}
        </div>

        {/* Notification Tabs */}
        <Tabs defaultValue="all" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="push">Push Settings</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <Bell className="h-5 w-5 mr-2" />
                    All Notifications
                  </CardTitle>
                  <div className="flex items-center space-x-2">
                    <Button size="sm" variant="outline">
                      <Filter className="h-4 w-4 mr-1" />
                      Filter
                    </Button>
                    <Button size="sm" variant="outline">
                      <MarkAsRead className="h-4 w-4 mr-1" />
                      Mark All Read
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {allNotifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-4 rounded-lg border-l-4 ${
                        notification.priority === "critical"
                          ? "border-l-red-500 bg-red-50"
                          : notification.priority === "high"
                            ? "border-l-orange-500 bg-orange-50"
                            : notification.priority === "medium"
                              ? "border-l-yellow-500 bg-yellow-50"
                              : "border-l-blue-500 bg-blue-50"
                      } ${!notification.read ? "ring-2 ring-blue-100" : ""}`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="font-medium text-gray-900">{notification.title}</h4>
                            <Badge
                              className={`text-xs ${
                                notification.priority === "critical"
                                  ? "bg-red-100 text-red-800"
                                  : notification.priority === "high"
                                    ? "bg-orange-100 text-orange-800"
                                    : notification.priority === "medium"
                                      ? "bg-yellow-100 text-yellow-800"
                                      : "bg-blue-100 text-blue-800"
                              }`}
                            >
                              {notification.priority}
                            </Badge>
                            {!notification.read && <div className="w-2 h-2 bg-blue-500 rounded-full"></div>}
                          </div>
                          <p className="text-sm text-gray-700 mb-2">{notification.message}</p>
                          <p className="text-xs text-gray-500">{notification.time}</p>
                        </div>
                        <Button size="sm" variant="ghost" className="text-gray-400">
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="push">
            <PushNotifications />
          </TabsContent>

          <TabsContent value="analytics">
            <AnalyticsNotifications />
          </TabsContent>
        </Tabs>
      </div>
    </MobileLayout>
  )
}
