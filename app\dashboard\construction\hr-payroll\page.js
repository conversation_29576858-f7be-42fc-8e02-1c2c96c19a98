"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Users,
  Clock,
  DollarSign,
  Calendar,
  UserCheck,
  Plus,
  Search,
  MoreHorizontal,
  Shield,
  Settings,
  Download,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"

export default function HRPayrollPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedDepartment, setSelectedDepartment] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [isAddEmployeeOpen, setIsAddEmployeeOpen] = useState(false)
  const [isProcessPayrollOpen, setIsProcessPayrollOpen] = useState(false)

  const hrStats = [
    {
      title: "Total Employees",
      value: "247",
      change: "+12 this month",
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Active Projects",
      value: "18",
      change: "Across 3 sites",
      icon: Calendar,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Monthly Payroll",
      value: "$485K",
      change: "+8% vs last month",
      icon: DollarSign,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      title: "Attendance Rate",
      value: "94.2%",
      change: "****% improvement",
      icon: UserCheck,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
  ]

  const employees = [
    {
      id: "EMP001",
      name: "John Smith",
      employeeId: "CS001",
      designation: "Site Supervisor",
      department: "Construction",
      project: "Metro Tower Complex",
      joiningDate: "2023-06-15",
      salary: 65000,
      status: "Active",
      phone: "(*************",
      email: "<EMAIL>",
      address: "123 Main St, Construction City",
      emergencyContact: "Jane Smith - (*************",
      skills: ["Site Management", "Safety Compliance", "Team Leadership"],
      certifications: ["OSHA 30", "First Aid", "Crane Operation"],
      avatar: "/placeholder.svg?height=40&width=40",
      attendance: {
        present: 22,
        absent: 2,
        late: 1,
        overtime: 15,
      },
      payroll: {
        basicSalary: 65000,
        allowances: 8000,
        overtime: 4500,
        deductions: 12500,
        netSalary: 65000,
      },
    },
    {
      id: "EMP002",
      name: "Maria Garcia",
      employeeId: "CS002",
      designation: "Safety Officer",
      department: "Safety",
      project: "Metro Tower Complex",
      joiningDate: "2023-08-20",
      salary: 58000,
      status: "Active",
      phone: "(*************",
      email: "<EMAIL>",
      address: "456 Safety Ave, Construction City",
      emergencyContact: "Carlos Garcia - (*************",
      skills: ["Safety Auditing", "Risk Assessment", "Training"],
      certifications: ["CSP", "OSHA 30", "Hazmat"],
      avatar: "/placeholder.svg?height=40&width=40",
      attendance: {
        present: 24,
        absent: 0,
        late: 0,
        overtime: 8,
      },
      payroll: {
        basicSalary: 58000,
        allowances: 7000,
        overtime: 2400,
        deductions: 11200,
        netSalary: 56200,
      },
    },
    {
      id: "EMP003",
      name: "David Chen",
      employeeId: "CS003",
      designation: "Quality Inspector",
      department: "Quality Control",
      project: "Green Valley Residential",
      joiningDate: "2023-04-10",
      salary: 52000,
      status: "Active",
      phone: "(*************",
      email: "<EMAIL>",
      address: "789 Quality Rd, Construction City",
      emergencyContact: "Lisa Chen - (*************",
      skills: ["Quality Testing", "Documentation", "Standards Compliance"],
      certifications: ["QC Level II", "ISO 9001", "Materials Testing"],
      avatar: "/placeholder.svg?height=40&width=40",
      attendance: {
        present: 23,
        absent: 1,
        late: 2,
        overtime: 12,
      },
      payroll: {
        basicSalary: 52000,
        allowances: 6500,
        overtime: 3600,
        deductions: 10800,
        netSalary: 51300,
      },
    },
  ]

  const attendance = [
    {
      date: "2024-01-24",
      employee: "John Smith",
      checkIn: "08:00 AM",
      checkOut: "06:30 PM",
      totalHours: 10.5,
      overtime: 2.5,
      status: "Present",
      project: "Metro Tower Complex",
      location: "Site Office",
    },
    {
      date: "2024-01-24",
      employee: "Maria Garcia",
      checkIn: "07:45 AM",
      checkOut: "05:15 PM",
      totalHours: 9.5,
      overtime: 1.5,
      status: "Present",
      project: "Metro Tower Complex",
      location: "Construction Site",
    },
    {
      date: "2024-01-24",
      employee: "David Chen",
      checkIn: "09:15 AM",
      checkOut: "06:00 PM",
      totalHours: 8.75,
      overtime: 0.75,
      status: "Late",
      project: "Green Valley Residential",
      location: "Quality Lab",
    },
  ]

  const leaveRequests = [
    {
      id: "LR001",
      employee: "John Smith",
      type: "Sick Leave",
      startDate: "2024-01-26",
      endDate: "2024-01-27",
      days: 2,
      reason: "Medical appointment and recovery",
      status: "Pending",
      appliedDate: "2024-01-24",
      approver: "Sarah Wilson",
    },
    {
      id: "LR002",
      employee: "Maria Garcia",
      type: "Annual Leave",
      startDate: "2024-02-05",
      endDate: "2024-02-09",
      days: 5,
      reason: "Family vacation",
      status: "Approved",
      appliedDate: "2024-01-20",
      approver: "Sarah Wilson",
    },
    {
      id: "LR003",
      employee: "David Chen",
      type: "Personal Leave",
      startDate: "2024-01-30",
      endDate: "2024-01-30",
      days: 1,
      reason: "Personal work",
      status: "Rejected",
      appliedDate: "2024-01-23",
      approver: "Mike Anderson",
    },
  ]

  const payrollSummary = [
    {
      month: "January 2024",
      totalEmployees: 247,
      grossPayroll: 1285000,
      totalDeductions: 257000,
      netPayroll: 1028000,
      overtime: 125000,
      bonuses: 85000,
      status: "Processed",
      processedDate: "2024-01-31",
    },
    {
      month: "December 2023",
      totalEmployees: 242,
      grossPayroll: 1245000,
      totalDeductions: 249000,
      netPayroll: 996000,
      overtime: 115000,
      bonuses: 150000,
      status: "Processed",
      processedDate: "2023-12-31",
    },
  ]

  const compliance = [
    {
      type: "Labor Law Compliance",
      status: "Compliant",
      lastAudit: "2024-01-15",
      nextAudit: "2024-04-15",
      score: 95,
      issues: 0,
    },
    {
      type: "Safety Compliance",
      status: "Compliant",
      lastAudit: "2024-01-10",
      nextAudit: "2024-04-10",
      score: 92,
      issues: 2,
    },
    {
      type: "Contractor Compliance",
      status: "Review Required",
      lastAudit: "2024-01-05",
      nextAudit: "2024-02-05",
      score: 78,
      issues: 5,
    },
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case "Active":
      case "Present":
      case "Approved":
      case "Processed":
      case "Compliant":
        return "bg-green-100 text-green-800"
      case "Inactive":
      case "Absent":
      case "Rejected":
        return "bg-red-100 text-red-800"
      case "Pending":
      case "Late":
      case "Review Required":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">HR & Payroll Management</h1>
            <p className="text-gray-600 mt-1">
              Comprehensive workforce management with attendance, payroll, and compliance tracking
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Reports
            </Button>
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Dialog open={isAddEmployeeOpen} onOpenChange={setIsAddEmployeeOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Employee
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Add New Employee</DialogTitle>
                  <DialogDescription>Register a new employee in the system.</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="employeeName">Full Name</Label>
                      <Input id="employeeName" placeholder="Enter full name" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="employeeId">Employee ID</Label>
                      <Input id="employeeId" placeholder="Enter employee ID" />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="designation">Designation</Label>
                      <Input id="designation" placeholder="Enter designation" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="department">Department</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select department" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="construction">Construction</SelectItem>
                          <SelectItem value="safety">Safety</SelectItem>
                          <SelectItem value="quality">Quality Control</SelectItem>
                          <SelectItem value="admin">Administration</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="salary">Basic Salary</Label>
                      <Input id="salary" type="number" placeholder="Enter basic salary" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="joiningDate">Joining Date</Label>
                      <Input id="joiningDate" type="date" />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone</Label>
                      <Input id="phone" placeholder="Enter phone number" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input id="email" type="email" placeholder="Enter email address" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="address">Address</Label>
                    <Textarea id="address" placeholder="Enter complete address..." />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" className="bg-gradient-to-r from-purple-600 to-pink-600">
                    Add Employee
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {hrStats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-green-600">{stat.change}</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="employees" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="employees">Employees</TabsTrigger>
            <TabsTrigger value="attendance">Attendance</TabsTrigger>
            <TabsTrigger value="leave">Leave</TabsTrigger>
            <TabsTrigger value="payroll">Payroll</TabsTrigger>
            <TabsTrigger value="compliance">Compliance</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>

          {/* Employees Tab */}
          <TabsContent value="employees" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Employee Management</CardTitle>
                  <div className="flex space-x-2">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search employees..."
                        className="pl-10 w-64"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
                      <SelectTrigger className="w-40">
                        <SelectValue placeholder="Department" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Departments</SelectItem>
                        <SelectItem value="construction">Construction</SelectItem>
                        <SelectItem value="safety">Safety</SelectItem>
                        <SelectItem value="quality">Quality Control</SelectItem>
                        <SelectItem value="admin">Administration</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {employees.map((employee) => (
                    <Card key={employee.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center space-x-4">
                            <Avatar className="h-12 w-12">
                              <AvatarImage src={employee.avatar || "/placeholder.svg"} alt={employee.name} />
                              <AvatarFallback>
                                {employee.name
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1">
                              <div className="flex items-center space-x-3 mb-2">
                                <h3 className="font-semibold text-gray-900">{employee.name}</h3>
                                <Badge className={getStatusColor(employee.status)}>{employee.status}</Badge>
                                <Badge variant="outline">{employee.department}</Badge>
                              </div>
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500">
                                <span>ID: {employee.employeeId}</span>
                                <span>Designation: {employee.designation}</span>
                                <span>Project: {employee.project}</span>
                                <span>Joined: {formatDate(employee.joiningDate)}</span>
                              </div>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>View Profile</DropdownMenuItem>
                              <DropdownMenuItem>Edit Employee</DropdownMenuItem>
                              <DropdownMenuItem>View Attendance</DropdownMenuItem>
                              <DropdownMenuItem>Generate Payslip</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Deactivate</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        {/* Contact Information */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 text-sm">
                          <div>
                            <span className="text-gray-600">Phone:</span>
                            <div className="font-medium">{employee.phone}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Email:</span>
                            <div className="font-medium">{employee.email}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Address:</span>
                            <div className="font-medium">{employee.address}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Emergency Contact:</span>
                            <div className="font-medium">{employee.emergencyContact}</div>
                          </div>
                        </div>

                        {/* Skills and Certifications */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                          <div>
                            <span className="text-sm text-gray-600 mb-2 block">Skills</span>
                            <div className="flex flex-wrap gap-1">
                              {employee.skills.map((skill, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {skill}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <div>
                            <span className="text-sm text-gray-600 mb-2 block">Certifications</span>
                            <div className="flex flex-wrap gap-1">
                              {employee.certifications.map((cert, index) => (
                                <Badge key={index} variant="outline" className="text-xs bg-green-50 text-green-700">
                                  {cert}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>

                        {/* Attendance Summary */}
                        <div className="bg-gray-50 p-4 rounded-lg mb-4">
                          <h4 className="text-sm font-medium text-gray-900 mb-2">This Month Attendance</h4>
                          <div className="grid grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-gray-600">Present</span>
                              <div className="font-semibold text-green-600">{employee.attendance.present} days</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Absent</span>
                              <div className="font-semibold text-red-600">{employee.attendance.absent} days</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Late</span>
                              <div className="font-semibold text-yellow-600">{employee.attendance.late} days</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Overtime</span>
                              <div className="font-semibold text-blue-600">{employee.attendance.overtime} hrs</div>
                            </div>
                          </div>
                        </div>

                        {/* Payroll Summary */}
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-900 mb-2">Current Payroll</h4>
                          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                            <div>
                              <span className="text-gray-600">Basic Salary</span>
                              <div className="font-semibold">{formatCurrency(employee.payroll.basicSalary)}</div>
                            </div>
                            <div>
                              <span className="text-gray-600">Allowances</span>
                              <div className="font-semibold text-green-600">
                                +{formatCurrency(employee.payroll.allowances)}
                              </div>
                            </div>
                            <div>
                              <span className="text-gray-600">Overtime</span>
                              <div className="font-semibold text-blue-600">
                                +{formatCurrency(employee.payroll.overtime)}
                              </div>
                            </div>
                            <div>
                              <span className="text-gray-600">Deductions</span>
                              <div className="font-semibold text-red-600">
                                -{formatCurrency(employee.payroll.deductions)}
                              </div>
                            </div>
                            <div>
                              <span className="text-gray-600">Net Salary</span>
                              <div className="font-semibold text-purple-600">
                                {formatCurrency(employee.payroll.netSalary)}
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Attendance Tab */}
          <TabsContent value="attendance" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <Clock className="h-5 w-5 mr-2 text-blue-600" />
                    Attendance Tracking
                  </CardTitle>
                  <Button variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Mark Attendance
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {attendance.map((record, index) => (
                    <Card key={index} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h4 className="font-semibold text-gray-900">{record.employee}</h4>
                              <Badge className={getStatusColor(record.status)}>{record.status}</Badge>
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500 mb-3">
                              <span>Date: {formatDate(record.date)}</span>
                              <span>Project: {record.project}</span>
                              <span>Location: {record.location}</span>
                              <span>Total Hours: {record.totalHours}h</span>
                            </div>
                            <div className="grid grid-cols-4 gap-4 text-sm">
                              <div>
                                <span className="text-gray-600">Check In</span>
                                <div className="font-semibold text-green-600">{record.checkIn}</div>
                              </div>
                              <div>
                                <span className="text-gray-600">Check Out</span>
                                <div className="font-semibold text-red-600">{record.checkOut}</div>
                              </div>
                              <div>
                                <span className="text-gray-600">Regular Hours</span>
                                <div className="font-semibold">{record.totalHours - record.overtime}h</div>
                              </div>
                              <div>
                                <span className="text-gray-600">Overtime</span>
                                <div className="font-semibold text-orange-600">{record.overtime}h</div>
                              </div>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>Edit Attendance</DropdownMenuItem>
                              <DropdownMenuItem>View Details</DropdownMenuItem>
                              <DropdownMenuItem>Generate Report</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Mark as Overtime</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Leave Tab */}
          <TabsContent value="leave" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <Calendar className="h-5 w-5 mr-2 text-green-600" />
                    Leave Management
                  </CardTitle>
                  <Button variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Apply Leave
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {leaveRequests.map((leave) => (
                    <Card key={leave.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h4 className="font-semibold text-gray-900">{leave.employee}</h4>
                              <Badge className={getStatusColor(leave.status)}>{leave.status}</Badge>
                              <Badge variant="outline">{leave.type}</Badge>
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500 mb-3">
                              <span>Applied: {formatDate(leave.appliedDate)}</span>
                              <span>Duration: {leave.days} days</span>
                              <span>From: {formatDate(leave.startDate)}</span>
                              <span>To: {formatDate(leave.endDate)}</span>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">
                              <span className="font-medium">Reason:</span> {leave.reason}
                            </p>
                            <div className="text-sm text-gray-500">
                              <span className="font-medium">Approver:</span> {leave.approver}
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>View Details</DropdownMenuItem>
                              <DropdownMenuItem>Approve</DropdownMenuItem>
                              <DropdownMenuItem>Reject</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Cancel Request</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Payroll Tab */}
          <TabsContent value="payroll" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <DollarSign className="h-5 w-5 mr-2 text-purple-600" />
                    Payroll Processing
                  </CardTitle>
                  <Dialog open={isProcessPayrollOpen} onOpenChange={setIsProcessPayrollOpen}>
                    <DialogTrigger asChild>
                      <Button className="bg-gradient-to-r from-purple-600 to-pink-600">
                        <Plus className="h-4 w-4 mr-2" />
                        Process Payroll
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[525px]">
                      <DialogHeader>
                        <DialogTitle>Process Monthly Payroll</DialogTitle>
                        <DialogDescription>Process payroll for all employees for the selected month.</DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="payrollMonth">Month</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select month" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="january-2024">January 2024</SelectItem>
                                <SelectItem value="february-2024">February 2024</SelectItem>
                                <SelectItem value="march-2024">March 2024</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="payrollType">Type</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="regular">Regular Payroll</SelectItem>
                                <SelectItem value="bonus">Bonus Payroll</SelectItem>
                                <SelectItem value="overtime">Overtime Payroll</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label>Employees to Include</Label>
                          <div className="text-sm text-gray-600">247 active employees will be processed</div>
                        </div>
                        <div className="space-y-2">
                          <Label>Estimated Total</Label>
                          <div className="text-lg font-semibold text-purple-600">{formatCurrency(1285000)}</div>
                        </div>
                      </div>
                      <DialogFooter>
                        <Button type="submit" className="bg-gradient-to-r from-purple-600 to-pink-600">
                          Process Payroll
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {payrollSummary.map((payroll, index) => (
                    <Card key={index} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h4 className="font-semibold text-gray-900">{payroll.month}</h4>
                              <Badge className={getStatusColor(payroll.status)}>{payroll.status}</Badge>
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500 mb-4">
                              <span>Employees: {payroll.totalEmployees}</span>
                              <span>Processed: {formatDate(payroll.processedDate)}</span>
                              <span>Overtime: {formatCurrency(payroll.overtime)}</span>
                              <span>Bonuses: {formatCurrency(payroll.bonuses)}</span>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>View Details</DropdownMenuItem>
                              <DropdownMenuItem>Download Report</DropdownMenuItem>
                              <DropdownMenuItem>Generate Payslips</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Reprocess</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        {/* Payroll Breakdown */}
                        <div className="grid grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Gross Payroll</span>
                            <div className="font-semibold text-blue-600">{formatCurrency(payroll.grossPayroll)}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Total Deductions</span>
                            <div className="font-semibold text-red-600">{formatCurrency(payroll.totalDeductions)}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Net Payroll</span>
                            <div className="font-semibold text-green-600">{formatCurrency(payroll.netPayroll)}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Processing Status</span>
                            <div className="font-semibold text-purple-600">{payroll.status}</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Compliance Tab */}
          <TabsContent value="compliance" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <Shield className="h-5 w-5 mr-2 text-red-600" />
                    Labor Compliance
                  </CardTitle>
                  <Button variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Schedule Audit
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {compliance.map((item, index) => (
                    <Card key={index} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h4 className="font-semibold text-gray-900">{item.type}</h4>
                              <Badge className={getStatusColor(item.status)}>{item.status}</Badge>
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500 mb-4">
                              <span>Last Audit: {formatDate(item.lastAudit)}</span>
                              <span>Next Audit: {formatDate(item.nextAudit)}</span>
                              <span>Score: {item.score}%</span>
                              <span>Issues: {item.issues}</span>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>View Audit Report</DropdownMenuItem>
                              <DropdownMenuItem>Schedule Review</DropdownMenuItem>
                              <DropdownMenuItem>Update Compliance</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Generate Certificate</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        {/* Compliance Score */}
                        <div className="mb-4">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-gray-600">Compliance Score</span>
                            <span className="text-sm font-medium">{item.score}%</span>
                          </div>
                          <Progress value={item.score} className="h-2" />
                        </div>

                        {/* Issues Summary */}
                        {item.issues > 0 && (
                          <div className="bg-red-50 p-3 rounded-lg">
                            <div className="flex items-center space-x-2">
                              <Shield className="h-4 w-4 text-red-600" />
                              <span className="text-sm font-medium text-red-900">
                                {item.issues} compliance issues require attention
                              </span>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Reports Tab */}
          <TabsContent value="reports" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <Users className="h-5 w-5 mr-2 text-blue-600" />
                    Employee Report
                  </CardTitle>
                  <CardDescription>Comprehensive employee data and analytics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Employees</span>
                      <span className="font-semibold">247</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Active Projects</span>
                      <span className="font-semibold text-green-600">18</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Avg. Tenure</span>
                      <span className="font-semibold text-blue-600">2.3 years</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <Clock className="h-5 w-5 mr-2 text-green-600" />
                    Attendance Report
                  </CardTitle>
                  <CardDescription>Attendance patterns and productivity metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Attendance Rate</span>
                      <span className="font-semibold text-green-600">94.2%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Overtime Hours</span>
                      <span className="font-semibold">1,247 hrs</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Late Arrivals</span>
                      <span className="font-semibold text-orange-600">3.2%</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <DollarSign className="h-5 w-5 mr-2 text-purple-600" />
                    Payroll Report
                  </CardTitle>
                  <CardDescription>Salary analysis and cost breakdown</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Monthly Payroll</span>
                      <span className="font-semibold">{formatCurrency(1285000)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Avg. Salary</span>
                      <span className="font-semibold text-green-600">{formatCurrency(5203)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Overtime Cost</span>
                      <span className="font-semibold text-orange-600">{formatCurrency(125000)}</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
