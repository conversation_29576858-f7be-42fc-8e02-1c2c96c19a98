"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
} from "recharts"
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChartIcon,
  LineChartIcon,
  Download,
  Users,
  DollarSign,
  Target,
  Activity,
  AlertTriangle,
  CheckCircle,
  Zap,
  Brain,
  Eye,
  Settings,
  Plus,
  Share,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"

export default function AnalyticsPage() {
  const [selectedTimeRange, setSelectedTimeRange] = useState("30d")
  const [selectedModules, setSelectedModules] = useState(["all"])
  const [isCustomReportOpen, setIsCustomReportOpen] = useState(false)
  const [insights, setInsights] = useState([])

  // Mock data for cross-module analytics
  const overallMetrics = {
    totalUsers: 2847,
    totalRevenue: 485230,
    activeProjects: 24,
    completionRate: 94.2,
    customerSatisfaction: 4.7,
    systemUptime: 99.9,
  }

  const modulePerformance = [
    { module: "User Management", users: 2847, growth: 12.5, efficiency: 95 },
    { module: "Payroll", employees: 247, cost: 485230, accuracy: 99.2 },
    { module: "Tickets", tickets: 1247, resolved: 1189, satisfaction: 4.6 },
    { module: "CRM", leads: 1247, converted: 342, revenue: 2400000 },
    { module: "Projects", projects: 24, completed: 18, onTime: 87 },
    { module: "Assets", assets: 456, value: 2400000, depreciation: 180000 },
    { module: "IT Management", licenses: 324, spend: 13600, compliance: 78 },
    { module: "Consultations", appointments: 156, revenue: 18450, rate: 94.2 },
  ]

  const revenueData = [
    { month: "Jan", payroll: 45000, consultations: 15000, projects: 25000, total: 85000 },
    { month: "Feb", payroll: 47000, consultations: 16500, projects: 28000, total: 91500 },
    { month: "Mar", payroll: 48500, consultations: 18000, projects: 30000, total: 96500 },
    { month: "Apr", payroll: 49200, consultations: 17800, projects: 32000, total: 99000 },
    { month: "May", payroll: 50100, consultations: 19200, projects: 35000, total: 104300 },
    { month: "Jun", payroll: 51000, consultations: 18450, projects: 38000, total: 107450 },
  ]

  const userActivityData = [
    { day: "Mon", users: 2340, sessions: 3420, engagement: 78 },
    { day: "Tue", users: 2180, sessions: 3180, engagement: 82 },
    { day: "Wed", users: 2450, sessions: 3650, engagement: 85 },
    { day: "Thu", users: 2380, sessions: 3520, engagement: 79 },
    { day: "Fri", users: 2520, sessions: 3780, engagement: 88 },
    { day: "Sat", users: 1890, sessions: 2340, engagement: 65 },
    { day: "Sun", users: 1650, sessions: 2100, engagement: 62 },
  ]

  const departmentData = [
    { name: "Engineering", value: 35, color: "#3B82F6" },
    { name: "Sales", value: 25, color: "#10B981" },
    { name: "Marketing", value: 20, color: "#F59E0B" },
    { name: "HR", value: 12, color: "#EF4444" },
    { name: "Operations", value: 8, color: "#8B5CF6" },
  ]

  const performanceMetrics = [
    { metric: "System Performance", current: 95, target: 98, trend: "up" },
    { metric: "User Satisfaction", current: 4.7, target: 4.8, trend: "up" },
    { metric: "Task Completion", current: 94.2, target: 95, trend: "up" },
    { metric: "Response Time", current: 1.2, target: 1.0, trend: "down" },
    { metric: "Error Rate", current: 0.8, target: 0.5, trend: "down" },
    { metric: "Uptime", current: 99.9, target: 99.95, trend: "stable" },
  ]

  const automatedInsights = [
    {
      id: 1,
      type: "opportunity",
      title: "Revenue Growth Opportunity",
      description: "Consultation bookings increased 23% this month. Consider expanding consultant availability.",
      impact: "High",
      module: "Consultations",
      action: "Increase capacity",
      confidence: 92,
    },
    {
      id: 2,
      type: "warning",
      title: "IT License Over-allocation",
      description: "Adobe Creative Suite licenses are 112% utilized. Risk of compliance issues.",
      impact: "Medium",
      module: "IT Management",
      action: "Purchase additional licenses",
      confidence: 88,
    },
    {
      id: 3,
      type: "success",
      title: "Project Delivery Excellence",
      description: "87% of projects delivered on time this quarter, exceeding target by 7%.",
      impact: "Low",
      module: "Projects",
      action: "Maintain current processes",
      confidence: 95,
    },
    {
      id: 4,
      type: "trend",
      title: "Payroll Cost Optimization",
      description: "Overtime costs decreased 15% while maintaining productivity levels.",
      impact: "Medium",
      module: "Payroll",
      action: "Document best practices",
      confidence: 85,
    },
  ]

  const customReports = [
    {
      id: 1,
      name: "Executive Summary",
      description: "High-level overview of all modules for leadership",
      modules: ["all"],
      frequency: "weekly",
      lastRun: "2024-01-15",
    },
    {
      id: 2,
      name: "HR Analytics",
      description: "Comprehensive HR metrics including payroll and user management",
      modules: ["users", "payroll"],
      frequency: "monthly",
      lastRun: "2024-01-10",
    },
    {
      id: 3,
      name: "Customer Success Report",
      description: "CRM and consultation performance analysis",
      modules: ["crm", "consultations"],
      frequency: "weekly",
      lastRun: "2024-01-12",
    },
  ]

  useEffect(() => {
    // Simulate automated insights generation
    setInsights(automatedInsights)
  }, [selectedTimeRange, selectedModules])

  const getInsightIcon = (type) => {
    switch (type) {
      case "opportunity":
        return <TrendingUp className="h-5 w-5 text-green-600" />
      case "warning":
        return <AlertTriangle className="h-5 w-5 text-orange-600" />
      case "success":
        return <CheckCircle className="h-5 w-5 text-blue-600" />
      case "trend":
        return <Activity className="h-5 w-5 text-purple-600" />
      default:
        return <Brain className="h-5 w-5 text-gray-600" />
    }
  }

  const getInsightColor = (type) => {
    switch (type) {
      case "opportunity":
        return "border-l-green-500 bg-green-50"
      case "warning":
        return "border-l-orange-500 bg-orange-50"
      case "success":
        return "border-l-blue-500 bg-blue-50"
      case "trend":
        return "border-l-purple-500 bg-purple-50"
      default:
        return "border-l-gray-500 bg-gray-50"
    }
  }

  const getTrendIcon = (trend) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="h-4 w-4 text-green-600" />
      case "down":
        return <TrendingDown className="h-4 w-4 text-red-600" />
      default:
        return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
            <p className="text-gray-600 mt-1">Cross-module insights and automated analytics</p>
          </div>
          <div className="flex space-x-3">
            <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Dialog open={isCustomReportOpen} onOpenChange={setIsCustomReportOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Custom Report
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Create Custom Report</DialogTitle>
                  <DialogDescription>
                    Build a custom analytics report with specific modules and metrics
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="report-name">Report Name</Label>
                      <Input id="report-name" placeholder="Enter report name" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="frequency">Frequency</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select frequency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="daily">Daily</SelectItem>
                          <SelectItem value="weekly">Weekly</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                          <SelectItem value="quarterly">Quarterly</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Modules to Include</Label>
                    <div className="grid grid-cols-2 gap-2">
                      {[
                        "User Management",
                        "Payroll",
                        "Tickets",
                        "CRM",
                        "Projects",
                        "Assets",
                        "IT Management",
                        "Consultations",
                      ].map((module) => (
                        <label key={module} className="flex items-center space-x-2">
                          <input type="checkbox" className="rounded" />
                          <span className="text-sm">{module}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Input id="description" placeholder="Report description" />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsCustomReportOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={() => setIsCustomReportOpen(false)}>Create Report</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Key Metrics Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          <Card className="hover:shadow-lg transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Users</p>
                  <p className="text-2xl font-bold text-gray-900">{overallMetrics.totalUsers.toLocaleString()}</p>
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card className="hover:shadow-lg transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(overallMetrics.totalRevenue)}</p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card className="hover:shadow-lg transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Projects</p>
                  <p className="text-2xl font-bold text-gray-900">{overallMetrics.activeProjects}</p>
                </div>
                <Target className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          <Card className="hover:shadow-lg transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Completion Rate</p>
                  <p className="text-2xl font-bold text-gray-900">{overallMetrics.completionRate}%</p>
                </div>
                <CheckCircle className="h-8 w-8 text-emerald-600" />
              </div>
            </CardContent>
          </Card>
          <Card className="hover:shadow-lg transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Satisfaction</p>
                  <p className="text-2xl font-bold text-gray-900">{overallMetrics.customerSatisfaction}/5</p>
                </div>
                <Activity className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
          <Card className="hover:shadow-lg transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">System Uptime</p>
                  <p className="text-2xl font-bold text-gray-900">{overallMetrics.systemUptime}%</p>
                </div>
                <Zap className="h-8 w-8 text-indigo-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Analytics Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="insights">AI Insights</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
            <TabsTrigger value="custom">Custom</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Revenue Trends */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <LineChartIcon className="h-5 w-5 mr-2 text-blue-600" />
                    Revenue Trends
                  </CardTitle>
                  <CardDescription>Monthly revenue across all modules</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={revenueData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip formatter={(value) => formatCurrency(value)} />
                      <Legend />
                      <Area type="monotone" dataKey="payroll" stackId="1" stroke="#3B82F6" fill="#3B82F6" />
                      <Area type="monotone" dataKey="consultations" stackId="1" stroke="#10B981" fill="#10B981" />
                      <Area type="monotone" dataKey="projects" stackId="1" stroke="#F59E0B" fill="#F59E0B" />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* User Activity */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChart3 className="h-5 w-5 mr-2 text-green-600" />
                    User Activity
                  </CardTitle>
                  <CardDescription>Daily user engagement metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={userActivityData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="users" fill="#3B82F6" />
                      <Bar dataKey="sessions" fill="#10B981" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Department Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <PieChartIcon className="h-5 w-5 mr-2 text-purple-600" />
                    Department Distribution
                  </CardTitle>
                  <CardDescription>Employee distribution across departments</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={departmentData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {departmentData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Module Performance */}
              <Card>
                <CardHeader>
                  <CardTitle>Module Performance</CardTitle>
                  <CardDescription>Performance metrics across all modules</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {modulePerformance.map((module, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">{module.module}</div>
                          <div className="text-sm text-gray-600">
                            {module.users && `${module.users.toLocaleString()} users`}
                            {module.employees && `${module.employees} employees`}
                            {module.tickets && `${module.tickets} tickets`}
                            {module.leads && `${module.leads} leads`}
                            {module.projects && `${module.projects} projects`}
                            {module.assets && `${module.assets} assets`}
                            {module.licenses && `${module.licenses} licenses`}
                            {module.appointments && `${module.appointments} appointments`}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold text-green-600">
                            {module.growth && `+${module.growth}%`}
                            {module.cost && formatCurrency(module.cost)}
                            {module.resolved && `${Math.round((module.resolved / module.tickets) * 100)}%`}
                            {module.converted && `${Math.round((module.converted / module.leads) * 100)}%`}
                            {module.completed && `${Math.round((module.completed / module.projects) * 100)}%`}
                            {module.value && formatCurrency(module.value)}
                            {module.spend && formatCurrency(module.spend)}
                            {module.revenue && formatCurrency(module.revenue)}
                          </div>
                          <div className="text-xs text-gray-500">
                            {module.efficiency && `${module.efficiency}% efficiency`}
                            {module.accuracy && `${module.accuracy}% accuracy`}
                            {module.satisfaction && `${module.satisfaction}/5 rating`}
                            {module.onTime && `${module.onTime}% on time`}
                            {module.depreciation && `${formatCurrency(module.depreciation)} depreciated`}
                            {module.compliance && `${module.compliance}% compliant`}
                            {module.rate && `${module.rate}% completion`}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* AI Insights Tab */}
          <TabsContent value="insights" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Brain className="h-5 w-5 mr-2 text-purple-600" />
                  Automated Insights
                </CardTitle>
                <CardDescription>AI-powered insights and recommendations based on your data</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {insights.map((insight) => (
                    <div key={insight.id} className={`p-4 border-l-4 rounded-lg ${getInsightColor(insight.type)}`}>
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3">
                          {getInsightIcon(insight.type)}
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900">{insight.title}</h4>
                            <p className="text-sm text-gray-700 mt-1">{insight.description}</p>
                            <div className="flex items-center space-x-4 mt-2">
                              <Badge variant="outline" className="text-xs">
                                {insight.module}
                              </Badge>
                              <span className="text-xs text-gray-500">Impact: {insight.impact}</span>
                              <span className="text-xs text-gray-500">Confidence: {insight.confidence}%</span>
                            </div>
                          </div>
                        </div>
                        <Button size="sm" variant="outline">
                          {insight.action}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Insight Categories */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card className="bg-green-50 border-green-200">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-green-800">Opportunities</p>
                      <p className="text-2xl font-bold text-green-900">
                        {insights.filter((i) => i.type === "opportunity").length}
                      </p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>
              <Card className="bg-orange-50 border-orange-200">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-orange-800">Warnings</p>
                      <p className="text-2xl font-bold text-orange-900">
                        {insights.filter((i) => i.type === "warning").length}
                      </p>
                    </div>
                    <AlertTriangle className="h-8 w-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-blue-800">Successes</p>
                      <p className="text-2xl font-bold text-blue-900">
                        {insights.filter((i) => i.type === "success").length}
                      </p>
                    </div>
                    <CheckCircle className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>
              <Card className="bg-purple-50 border-purple-200">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-purple-800">Trends</p>
                      <p className="text-2xl font-bold text-purple-900">
                        {insights.filter((i) => i.type === "trend").length}
                      </p>
                    </div>
                    <Activity className="h-8 w-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Performance Tab */}
          <TabsContent value="performance" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
                <CardDescription>Key performance indicators across all modules</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {performanceMetrics.map((metric, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-gray-900">{metric.metric}</span>
                          {getTrendIcon(metric.trend)}
                        </div>
                        <div className="text-right">
                          <span className="font-semibold text-gray-900">{metric.current}</span>
                          <span className="text-sm text-gray-500 ml-2">/ {metric.target}</span>
                        </div>
                      </div>
                      <Progress
                        value={(metric.current / metric.target) * 100}
                        className="h-2"
                        style={{
                          backgroundColor:
                            metric.current >= metric.target
                              ? "#10B981"
                              : metric.current >= metric.target * 0.8
                                ? "#F59E0B"
                                : "#EF4444",
                        }}
                      />
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>Current: {metric.current}</span>
                        <span>Target: {metric.target}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Performance Comparison */}
            <Card>
              <CardHeader>
                <CardTitle>Module Performance Comparison</CardTitle>
                <CardDescription>Comparative analysis of module efficiency</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={modulePerformance} layout="horizontal">
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="module" type="category" width={120} />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="efficiency" fill="#3B82F6" name="Efficiency %" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Reports Tab */}
          <TabsContent value="reports" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Saved Reports</CardTitle>
                <CardDescription>Manage and run your custom analytics reports</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {customReports.map((report) => (
                    <div
                      key={report.id}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{report.name}</h4>
                        <p className="text-sm text-gray-600">{report.description}</p>
                        <div className="flex items-center space-x-4 mt-2">
                          <Badge variant="outline" className="text-xs">
                            {report.frequency}
                          </Badge>
                          <span className="text-xs text-gray-500">Last run: {report.lastRun}</span>
                          <span className="text-xs text-gray-500">
                            Modules: {report.modules.includes("all") ? "All" : report.modules.join(", ")}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button size="sm" variant="ghost">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost">
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost">
                          <Share className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost">
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Report Templates */}
            <Card>
              <CardHeader>
                <CardTitle>Report Templates</CardTitle>
                <CardDescription>Pre-built report templates for common analytics needs</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[
                    {
                      name: "Executive Dashboard",
                      description: "High-level KPIs and trends",
                      modules: "All modules",
                      icon: <BarChart3 className="h-6 w-6" />,
                    },
                    {
                      name: "Financial Report",
                      description: "Revenue, costs, and profitability",
                      modules: "Payroll, CRM, Consultations",
                      icon: <DollarSign className="h-6 w-6" />,
                    },
                    {
                      name: "Operational Report",
                      description: "Efficiency and performance metrics",
                      modules: "Projects, Tickets, Assets",
                      icon: <Target className="h-6 w-6" />,
                    },
                    {
                      name: "HR Analytics",
                      description: "Employee and workforce insights",
                      modules: "Users, Payroll",
                      icon: <Users className="h-6 w-6" />,
                    },
                    {
                      name: "Customer Report",
                      description: "Customer satisfaction and engagement",
                      modules: "CRM, Consultations, Tickets",
                      icon: <Activity className="h-6 w-6" />,
                    },
                    {
                      name: "IT Report",
                      description: "Technology and infrastructure metrics",
                      modules: "IT Management, Assets",
                      icon: <Settings className="h-6 w-6" />,
                    },
                  ].map((template, index) => (
                    <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer">
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-3 mb-3">
                          <div className="p-2 bg-blue-100 rounded-lg text-blue-600">{template.icon}</div>
                          <div>
                            <h4 className="font-medium text-gray-900">{template.name}</h4>
                            <p className="text-sm text-gray-600">{template.description}</p>
                          </div>
                        </div>
                        <div className="text-xs text-gray-500 mb-3">{template.modules}</div>
                        <Button size="sm" className="w-full">
                          Use Template
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Custom Tab */}
          <TabsContent value="custom" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Custom Analytics Builder</CardTitle>
                <CardDescription>Build custom visualizations and reports</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="chart-type">Chart Type</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select chart type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="bar">Bar Chart</SelectItem>
                          <SelectItem value="line">Line Chart</SelectItem>
                          <SelectItem value="pie">Pie Chart</SelectItem>
                          <SelectItem value="area">Area Chart</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="data-source">Data Source</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select module" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="users">User Management</SelectItem>
                          <SelectItem value="payroll">Payroll</SelectItem>
                          <SelectItem value="tickets">Tickets</SelectItem>
                          <SelectItem value="crm">CRM</SelectItem>
                          <SelectItem value="projects">Projects</SelectItem>
                          <SelectItem value="assets">Assets</SelectItem>
                          <SelectItem value="it">IT Management</SelectItem>
                          <SelectItem value="consultations">Consultations</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="metric">Metric</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select metric" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="count">Count</SelectItem>
                          <SelectItem value="revenue">Revenue</SelectItem>
                          <SelectItem value="growth">Growth Rate</SelectItem>
                          <SelectItem value="efficiency">Efficiency</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="time-period">Time Period</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select period" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="7d">Last 7 days</SelectItem>
                          <SelectItem value="30d">Last 30 days</SelectItem>
                          <SelectItem value="90d">Last 90 days</SelectItem>
                          <SelectItem value="1y">Last year</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <Button className="w-full">Generate Chart</Button>
                  </div>
                  <div className="lg:col-span-2">
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                      <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">Your custom chart will appear here</p>
                      <p className="text-sm text-gray-400 mt-2">
                        Select options on the left to generate a visualization
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Saved Custom Charts */}
            <Card>
              <CardHeader>
                <CardTitle>Saved Custom Charts</CardTitle>
                <CardDescription>Your previously created custom visualizations</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[
                    { name: "Revenue by Module", type: "Bar Chart", created: "2024-01-15" },
                    { name: "User Growth Trend", type: "Line Chart", created: "2024-01-12" },
                    { name: "Project Status Distribution", type: "Pie Chart", created: "2024-01-10" },
                  ].map((chart, index) => (
                    <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-gray-900">{chart.name}</h4>
                          <Button size="sm" variant="ghost">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                        <p className="text-sm text-gray-600">{chart.type}</p>
                        <p className="text-xs text-gray-500 mt-1">Created: {chart.created}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
