"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>otateCcw } from "lucide-react"
import { useSpeechRecognition } from "@/hooks/use-speech-recognition"

export default function VoiceDictationField({
  component = "input",
  placeholder = "Type or speak...",
  value = "",
  onChange,
  className = "",
  ...props
}) {
  const {
    isListening,
    transcript,
    interimTranscript,
    error,
    confidence,
    startListening,
    stopListening,
    resetTranscript,
    isSupported,
  } = useSpeechRecognition()

  const [localValue, setLocalValue] = useState(value)
  const [showTranscript, setShowTranscript] = useState(false)
  const inputRef = useRef(null)

  useEffect(() => {
    setLocalValue(value)
  }, [value])

  useEffect(() => {
    if (transcript && !isListening) {
      const newValue = localValue + (localValue ? " " : "") + transcript
      setLocalValue(newValue)
      if (onChange) {
        onChange({ target: { value: newValue } })
      }
      resetTranscript()
    }
  }, [transcript, isListening, localValue, onChange, resetTranscript])

  const handleInputChange = (e) => {
    const newValue = e.target.value
    setLocalValue(newValue)
    if (onChange) {
      onChange(e)
    }
  }

  const handleVoiceToggle = () => {
    if (isListening) {
      stopListening()
    } else {
      startListening()
      setShowTranscript(true)
    }
  }

  const insertTranscript = () => {
    if (transcript && inputRef.current) {
      const input = inputRef.current
      const cursorPosition = input.selectionStart || localValue.length
      const beforeCursor = localValue.slice(0, cursorPosition)
      const afterCursor = localValue.slice(cursorPosition)
      const newValue = beforeCursor + transcript + afterCursor

      setLocalValue(newValue)
      if (onChange) {
        onChange({ target: { value: newValue } })
      }

      // Move cursor to end of inserted text
      setTimeout(() => {
        const newCursorPosition = cursorPosition + transcript.length
        input.setSelectionRange(newCursorPosition, newCursorPosition)
        input.focus()
      }, 0)

      resetTranscript()
      setShowTranscript(false)
    }
  }

  const clearTranscript = () => {
    resetTranscript()
    setShowTranscript(false)
  }

  if (!isSupported) {
    const Component = component === "textarea" ? Textarea : Input
    return (
      <Component
        ref={inputRef}
        value={localValue}
        onChange={handleInputChange}
        placeholder={placeholder}
        className={className}
        {...props}
      />
    )
  }

  const Component = component === "textarea" ? Textarea : Input

  return (
    <div className="space-y-2">
      <div className="relative">
        <Component
          ref={inputRef}
          value={localValue}
          onChange={handleInputChange}
          placeholder={placeholder}
          className={`pr-12 ${className}`}
          {...props}
        />
        <Button
          type="button"
          onClick={handleVoiceToggle}
          variant="ghost"
          size="sm"
          className={`absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 ${
            isListening ? "text-red-600 hover:text-red-700" : "text-gray-400 hover:text-gray-600"
          }`}
        >
          {isListening ? <MicOff className="h-4 w-4 animate-pulse" /> : <Mic className="h-4 w-4" />}
        </Button>
      </div>

      {/* Live Transcript Display */}
      {(showTranscript || isListening) && (transcript || interimTranscript) && (
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <Badge className="bg-blue-100 text-blue-800 text-xs">{isListening ? "Listening" : "Transcript"}</Badge>
              {confidence > 0 && (
                <Badge className="bg-gray-100 text-gray-600 text-xs">{Math.round(confidence * 100)}% confident</Badge>
              )}
            </div>
            <div className="flex space-x-1">
              {transcript && !isListening && (
                <>
                  <Button
                    type="button"
                    onClick={insertTranscript}
                    size="sm"
                    variant="ghost"
                    className="h-6 px-2 text-xs"
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    Insert
                  </Button>
                  <Button
                    type="button"
                    onClick={clearTranscript}
                    size="sm"
                    variant="ghost"
                    className="h-6 px-2 text-xs"
                  >
                    <RotateCcw className="h-3 w-3" />
                  </Button>
                </>
              )}
            </div>
          </div>
          <div className="text-sm">
            {transcript && <span className="bg-yellow-100 px-1 rounded">{transcript}</span>}
            {interimTranscript && <span className="text-gray-500 italic ml-1">{interimTranscript}</span>}
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
          Voice recognition error: {error}
        </div>
      )}

      {/* Status Indicator */}
      {isListening && (
        <div className="flex items-center space-x-2 text-xs text-gray-500">
          <div className="flex space-x-1">
            <div className="w-1 h-1 bg-red-500 rounded-full animate-pulse"></div>
            <div className="w-1 h-1 bg-red-500 rounded-full animate-pulse" style={{ animationDelay: "0.2s" }}></div>
            <div className="w-1 h-1 bg-red-500 rounded-full animate-pulse" style={{ animationDelay: "0.4s" }}></div>
          </div>
          <span>Listening for speech...</span>
        </div>
      )}
    </div>
  )
}
