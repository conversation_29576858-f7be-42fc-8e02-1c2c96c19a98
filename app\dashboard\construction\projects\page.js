"use client"

import { useState } from "react"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Building,
  DollarSign,
  MapPin,
  Plus,
  Search,
  MoreHorizontal,
  FileText,
  TrendingUp,
  Clock,
  CheckCircle,
  Settings,
  Download,
  Filter,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import Link from "next/link"

export default function ProjectManagementPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [selectedPriority, setSelectedPriority] = useState("all")
  const [isCreateProjectOpen, setIsCreateProjectOpen] = useState(false)

  const projects = [
    {
      id: "PROJ001",
      name: "Metro Tower Complex",
      description: "35-story mixed-use development with retail, office, and residential units",
      status: "In Progress",
      priority: "High",
      progress: 72,
      startDate: "2024-01-15",
      endDate: "2025-06-30",
      budget: 12500000,
      spent: 9000000,
      location: "Downtown Metro District",
      client: "Metro Development Corp",
      contractor: "BuildMax Construction",
      projectManager: "Sarah Wilson",
      architect: "Johnson & Associates",
      phases: [
        {
          name: "Foundation & Excavation",
          status: "Completed",
          progress: 100,
          startDate: "2024-01-15",
          endDate: "2024-04-30",
        },
        {
          name: "Structural Framework",
          status: "In Progress",
          progress: 85,
          startDate: "2024-03-01",
          endDate: "2024-09-15",
        },
        {
          name: "MEP Installation",
          status: "In Progress",
          progress: 45,
          startDate: "2024-06-01",
          endDate: "2024-12-30",
        },
        {
          name: "Interior & Finishing",
          status: "Planning",
          progress: 15,
          startDate: "2024-10-01",
          endDate: "2025-04-30",
        },
        {
          name: "Landscaping & Exterior",
          status: "Not Started",
          progress: 0,
          startDate: "2025-02-01",
          endDate: "2025-06-30",
        },
      ],
      team: [
        { name: "John Smith", role: "Site Supervisor", avatar: "/placeholder.svg?height=32&width=32" },
        { name: "Maria Garcia", role: "Safety Officer", avatar: "/placeholder.svg?height=32&width=32" },
        { name: "David Chen", role: "Quality Inspector", avatar: "/placeholder.svg?height=32&width=32" },
        { name: "Lisa Johnson", role: "Architect", avatar: "/placeholder.svg?height=32&width=32" },
      ],
      kpis: {
        scheduleVariance: -5,
        costVariance: 8,
        qualityScore: 92,
        safetyScore: 95,
        productivity: 87,
      },
    },
    {
      id: "PROJ002",
      name: "Green Valley Residential",
      description: "200-unit sustainable residential community with amenities",
      status: "Planning",
      priority: "Medium",
      progress: 28,
      startDate: "2024-03-01",
      endDate: "2025-02-28",
      budget: 8500000,
      spent: 2380000,
      location: "Green Valley Suburbs",
      client: "Valley Homes Ltd",
      contractor: "EcoConstruct Solutions",
      projectManager: "Mike Anderson",
      architect: "Green Design Studio",
      phases: [
        {
          name: "Site Preparation",
          status: "In Progress",
          progress: 75,
          startDate: "2024-03-01",
          endDate: "2024-05-15",
        },
        {
          name: "Infrastructure Development",
          status: "Planning",
          progress: 25,
          startDate: "2024-04-15",
          endDate: "2024-08-30",
        },
        {
          name: "Residential Construction",
          status: "Not Started",
          progress: 0,
          startDate: "2024-07-01",
          endDate: "2024-12-31",
        },
        {
          name: "Amenities & Common Areas",
          status: "Not Started",
          progress: 0,
          startDate: "2024-11-01",
          endDate: "2025-01-31",
        },
        {
          name: "Final Landscaping",
          status: "Not Started",
          progress: 0,
          startDate: "2025-01-01",
          endDate: "2025-02-28",
        },
      ],
      team: [
        { name: "Robert Brown", role: "Site Supervisor", avatar: "/placeholder.svg?height=32&width=32" },
        { name: "Jennifer Lee", role: "Environmental Officer", avatar: "/placeholder.svg?height=32&width=32" },
        { name: "Tom Wilson", role: "Foreman", avatar: "/placeholder.svg?height=32&width=32" },
      ],
      kpis: {
        scheduleVariance: 2,
        costVariance: -3,
        qualityScore: 88,
        safetyScore: 91,
        productivity: 82,
      },
    },
    {
      id: "PROJ003",
      name: "Industrial Park Phase 1",
      description: "Manufacturing and warehouse facilities with logistics infrastructure",
      status: "In Progress",
      priority: "Critical",
      progress: 58,
      startDate: "2023-10-01",
      endDate: "2024-08-31",
      budget: 15200000,
      spent: 8816000,
      location: "Industrial Zone East",
      client: "Industrial Development Authority",
      contractor: "Heavy Industries Construction",
      projectManager: "Emma Davis",
      architect: "Industrial Design Partners",
      phases: [
        {
          name: "Site Development",
          status: "Completed",
          progress: 100,
          startDate: "2023-10-01",
          endDate: "2024-01-15",
        },
        {
          name: "Utility Infrastructure",
          status: "Completed",
          progress: 100,
          startDate: "2023-12-01",
          endDate: "2024-03-30",
        },
        {
          name: "Building Construction",
          status: "In Progress",
          progress: 70,
          startDate: "2024-02-01",
          endDate: "2024-07-31",
        },
        {
          name: "Equipment Installation",
          status: "Planning",
          progress: 20,
          startDate: "2024-06-01",
          endDate: "2024-08-15",
        },
        {
          name: "Testing & Commissioning",
          status: "Not Started",
          progress: 0,
          startDate: "2024-08-01",
          endDate: "2024-08-31",
        },
      ],
      team: [
        { name: "Carlos Rodriguez", role: "Project Engineer", avatar: "/placeholder.svg?height=32&width=32" },
        { name: "Amy Thompson", role: "Safety Coordinator", avatar: "/placeholder.svg?height=32&width=32" },
        { name: "Kevin Park", role: "Quality Manager", avatar: "/placeholder.svg?height=32&width=32" },
      ],
      kpis: {
        scheduleVariance: -8,
        costVariance: 12,
        qualityScore: 94,
        safetyScore: 89,
        productivity: 91,
      },
    },
  ]

  const projectStats = [
    {
      title: "Active Projects",
      value: "12",
      change: "+3 this quarter",
      icon: Building,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Total Portfolio Value",
      value: "$45.2M",
      change: "+18% YoY",
      icon: DollarSign,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "On Schedule",
      value: "83%",
      change: "+5% improvement",
      icon: Clock,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
    {
      title: "Quality Score",
      value: "91%",
      change: "Industry leading",
      icon: CheckCircle,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case "Planning":
        return "bg-gray-100 text-gray-800"
      case "In Progress":
        return "bg-blue-100 text-blue-800"
      case "On Hold":
        return "bg-yellow-100 text-yellow-800"
      case "Completed":
        return "bg-green-100 text-green-800"
      case "Delayed":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "Critical":
        return "bg-red-100 text-red-800"
      case "High":
        return "bg-orange-100 text-orange-800"
      case "Medium":
        return "bg-yellow-100 text-yellow-800"
      case "Low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getVarianceColor = (variance) => {
    if (variance > 5) return "text-red-600"
    if (variance > 0) return "text-yellow-600"
    if (variance < -5) return "text-red-600"
    return "text-green-600"
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Project Management</h1>
            <p className="text-gray-600 mt-1">Strategic project planning, execution, and monitoring</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Reports
            </Button>
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Dialog open={isCreateProjectOpen} onOpenChange={setIsCreateProjectOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                  <Plus className="h-4 w-4 mr-2" />
                  New Project
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Create New Project</DialogTitle>
                  <DialogDescription>
                    Set up a new construction project with strategic alignment and resource allocation.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="projectName" className="text-right">
                      Project Name
                    </Label>
                    <Input id="projectName" placeholder="Enter project name" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-start gap-4">
                    <Label htmlFor="description" className="text-right mt-2">
                      Description
                    </Label>
                    <Textarea
                      id="description"
                      placeholder="Project description and objectives..."
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="client">Client</Label>
                      <Input id="client" placeholder="Client name" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="location">Location</Label>
                      <Input id="location" placeholder="Project location" />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="contractor">Contractor</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select contractor" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="buildmax">BuildMax Construction</SelectItem>
                          <SelectItem value="ecoconstruct">EcoConstruct Solutions</SelectItem>
                          <SelectItem value="heavyindustries">Heavy Industries Construction</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="projectManager">Project Manager</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Assign PM" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="sarah">Sarah Wilson</SelectItem>
                          <SelectItem value="mike">Mike Anderson</SelectItem>
                          <SelectItem value="emma">Emma Davis</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="budget">Total Budget</Label>
                      <Input id="budget" type="number" placeholder="Project budget" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="priority">Priority</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="critical">Critical</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="low">Low</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="startDate">Start Date</Label>
                      <Input id="startDate" type="date" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="endDate">Target Completion</Label>
                      <Input id="endDate" type="date" />
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" className="bg-gradient-to-r from-blue-600 to-purple-600">
                    Create Project
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {projectStats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-green-600">{stat.change}</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Project Portfolio</CardTitle>
              <div className="flex space-x-2">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search projects..."
                    className="pl-10 w-64"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="planning">Planning</SelectItem>
                    <SelectItem value="in-progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="on-hold">On Hold</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  Filters
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {projects.map((project) => (
                <Card key={project.id} className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <Link href={`/dashboard/construction/projects/${project.id}`}>
                            <h3 className="font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                              {project.name}
                            </h3>
                          </Link>
                          <Badge className={getStatusColor(project.status)}>{project.status}</Badge>
                          <Badge className={getPriorityColor(project.priority)}>{project.priority}</Badge>
                        </div>
                        <p className="text-sm text-gray-600 mb-3">{project.description}</p>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500">
                          <div className="flex items-center space-x-1">
                            <MapPin className="h-3 w-3" />
                            <span>{project.location}</span>
                          </div>
                          <span>Client: {project.client}</span>
                          <span>PM: {project.projectManager}</span>
                          <span>Contractor: {project.contractor}</span>
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem>
                            <Link href={`/dashboard/construction/projects/${project.id}`}>View Details</Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem>Edit Project</DropdownMenuItem>
                          <DropdownMenuItem>Gantt Chart</DropdownMenuItem>
                          <DropdownMenuItem>Resource Allocation</DropdownMenuItem>
                          <DropdownMenuItem>Financial Report</DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>Archive Project</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-gray-600">Overall Progress</span>
                          <span className="text-sm font-medium">{project.progress}%</span>
                        </div>
                        <Progress value={project.progress} className="h-2" />
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Budget</span>
                          <div className="font-semibold">{formatCurrency(project.budget)}</div>
                        </div>
                        <div>
                          <span className="text-gray-600">Spent</span>
                          <div className="font-semibold text-orange-600">{formatCurrency(project.spent)}</div>
                        </div>
                        <div>
                          <span className="text-gray-600">Timeline</span>
                          <div className="font-semibold">
                            {formatDate(project.startDate)} - {formatDate(project.endDate)}
                          </div>
                        </div>
                        <div>
                          <span className="text-gray-600">Quality Score</span>
                          <div className="font-semibold text-green-600">{project.kpis.qualityScore}%</div>
                        </div>
                        <div>
                          <span className="text-gray-600">Safety Score</span>
                          <div className="font-semibold text-blue-600">{project.kpis.safetyScore}%</div>
                        </div>
                      </div>

                      {/* KPI Dashboard */}
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="text-sm font-medium text-gray-900 mb-3">Key Performance Indicators</h4>
                        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Schedule Variance</span>
                            <div className={`font-semibold ${getVarianceColor(project.kpis.scheduleVariance)}`}>
                              {project.kpis.scheduleVariance > 0 ? "+" : ""}
                              {project.kpis.scheduleVariance}%
                            </div>
                          </div>
                          <div>
                            <span className="text-gray-600">Cost Variance</span>
                            <div className={`font-semibold ${getVarianceColor(project.kpis.costVariance)}`}>
                              {project.kpis.costVariance > 0 ? "+" : ""}
                              {project.kpis.costVariance}%
                            </div>
                          </div>
                          <div>
                            <span className="text-gray-600">Quality Score</span>
                            <div className="font-semibold text-green-600">{project.kpis.qualityScore}%</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Safety Score</span>
                            <div className="font-semibold text-blue-600">{project.kpis.safetyScore}%</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Productivity</span>
                            <div className="font-semibold text-purple-600">{project.kpis.productivity}%</div>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Project Phases</h4>
                        <div className="grid grid-cols-1 md:grid-cols-5 gap-2">
                          {project.phases.map((phase, index) => (
                            <div key={index} className="text-xs">
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-gray-600 truncate">{phase.name}</span>
                                <span className="font-medium">{phase.progress}%</span>
                              </div>
                              <Progress value={phase.progress} className="h-1" />
                              <Badge className={`mt-1 text-xs ${getStatusColor(phase.status)}`}>{phase.status}</Badge>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex -space-x-2">
                          {project.team.slice(0, 4).map((member, index) => (
                            <Avatar key={index} className="h-6 w-6 border-2 border-white">
                              <AvatarImage src={member.avatar || "/placeholder.svg"} alt={member.name} />
                              <AvatarFallback className="text-xs">
                                {member.name
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")}
                              </AvatarFallback>
                            </Avatar>
                          ))}
                          {project.team.length > 4 && (
                            <div className="h-6 w-6 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center">
                              <span className="text-xs text-gray-600">+{project.team.length - 4}</span>
                            </div>
                          )}
                        </div>
                        <div className="flex space-x-2">
                          <Button size="sm" variant="outline">
                            <FileText className="h-3 w-3 mr-1" />
                            Documents
                          </Button>
                          <Button size="sm" variant="outline">
                            <TrendingUp className="h-3 w-3 mr-1" />
                            Analytics
                          </Button>
                          <Link href={`/dashboard/construction/projects/${project.id}`}>
                            <Button size="sm" variant="outline">
                              View Details
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
