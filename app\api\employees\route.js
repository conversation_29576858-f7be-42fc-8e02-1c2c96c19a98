import { NextResponse } from "next/server"

const employees = [
  {
    id: "1",
    employee_code: "EMP001",
    first_name: "<PERSON>",
    last_name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "******-0123",
    department_id: "1",
    designation_id: "1",
    shift_id: "1",
    manager_id: null,
    hire_date: "2023-01-15",
    salary: 75000,
    status: "active",
    is_active: true,
    created_at: "2023-01-15T00:00:00Z",
    updated_at: "2023-01-15T00:00:00Z",
  },
  {
    id: "2",
    employee_code: "EMP002",
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    email: "<EMAIL>",
    phone: "******-0124",
    department_id: "2",
    designation_id: "2",
    shift_id: "1",
    manager_id: "1",
    hire_date: "2023-02-01",
    salary: 65000,
    status: "active",
    is_active: true,
    created_at: "2023-02-01T00:00:00Z",
    updated_at: "2023-02-01T00:00:00Z",
  },
  {
    id: "3",
    employee_code: "EMP003",
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    email: "<EMAIL>",
    phone: "******-0125",
    department_id: "1",
    designation_id: "3",
    shift_id: "2",
    manager_id: "1",
    hire_date: "2023-03-10",
    salary: 55000,
    status: "active",
    is_active: true,
    created_at: "2023-03-10T00:00:00Z",
    updated_at: "2023-03-10T00:00:00Z",
  },
]

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url)
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "10")
    const search = searchParams.get("search") || ""
    const department_id = searchParams.get("department_id")
    const status = searchParams.get("status")

    let filteredEmployees = employees

    if (search) {
      filteredEmployees = employees.filter(
        (employee) =>
          employee.first_name.toLowerCase().includes(search.toLowerCase()) ||
          employee.last_name.toLowerCase().includes(search.toLowerCase()) ||
          employee.email.toLowerCase().includes(search.toLowerCase()) ||
          employee.employee_code.toLowerCase().includes(search.toLowerCase()),
      )
    }

    if (department_id) {
      filteredEmployees = filteredEmployees.filter((employee) => employee.department_id === department_id)
    }

    if (status) {
      filteredEmployees = filteredEmployees.filter((employee) => employee.status === status)
    }

    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedEmployees = filteredEmployees.slice(startIndex, endIndex)

    return NextResponse.json({
      data: paginatedEmployees,
      pagination: {
        page,
        limit,
        total: filteredEmployees.length,
        totalPages: Math.ceil(filteredEmployees.length / limit),
      },
    })
  } catch (error) {
    return NextResponse.json({ error: "Failed to fetch employees" }, { status: 500 })
  }
}

export async function POST(request) {
  try {
    const body = await request.json()
    const newEmployee = {
      id: Date.now().toString(),
      employee_code: `EMP${String(employees.length + 1).padStart(3, "0")}`,
      ...body,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    employees.push(newEmployee)

    return NextResponse.json({ data: newEmployee }, { status: 201 })
  } catch (error) {
    return NextResponse.json({ error: "Failed to create employee" }, { status: 500 })
  }
}
