"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Bell, Package, AlertTriangle, Calendar, Wrench, CheckCircle, X, Clock, TrendingDown } from "lucide-react"

export default function AssetNotifications() {
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      type: "maintenance_due",
      title: "Maintenance Due",
      message: "MacBook Pro 16-inch (AST-001) requires scheduled maintenance",
      assetId: "AST-001",
      assetName: "MacBook Pro 16-inch",
      priority: "high",
      dueDate: "2024-02-15",
      timestamp: "2024-02-10T10:30:00Z",
      read: false,
    },
    {
      id: 2,
      type: "warranty_expiring",
      title: "Warranty Expiring",
      message: "Office Printer HP LaserJet warranty expires in 30 days",
      assetId: "AST-002",
      assetName: "Office Printer HP LaserJet",
      priority: "medium",
      expiryDate: "2024-03-15",
      timestamp: "2024-02-09T14:20:00Z",
      read: false,
    },
    {
      id: 3,
      type: "depreciation_milestone",
      title: "Depreciation Milestone",
      message: "Conference Room Table has reached 50% depreciation",
      assetId: "AST-003",
      assetName: "Conference Room Table",
      priority: "low",
      depreciationRate: 50,
      timestamp: "2024-02-08T09:15:00Z",
      read: true,
    },
    {
      id: 4,
      type: "maintenance_overdue",
      title: "Maintenance Overdue",
      message: "Industrial 3D Printer maintenance is 15 days overdue",
      assetId: "AST-005",
      assetName: "Industrial 3D Printer",
      priority: "critical",
      overdueDays: 15,
      timestamp: "2024-02-07T16:45:00Z",
      read: false,
    },
    {
      id: 5,
      type: "asset_assigned",
      title: "Asset Assigned",
      message: "New laptop assigned to Sarah Wilson",
      assetId: "AST-006",
      assetName: "Dell XPS 13",
      assignedTo: "Sarah Wilson",
      priority: "info",
      timestamp: "2024-02-06T11:30:00Z",
      read: true,
    },
  ])

  const [filter, setFilter] = useState("all")

  const getNotificationIcon = (type) => {
    switch (type) {
      case "maintenance_due":
      case "maintenance_overdue":
        return <Wrench className="h-4 w-4" />
      case "warranty_expiring":
        return <AlertTriangle className="h-4 w-4" />
      case "depreciation_milestone":
        return <TrendingDown className="h-4 w-4" />
      case "asset_assigned":
        return <Package className="h-4 w-4" />
      default:
        return <Bell className="h-4 w-4" />
    }
  }

  const getNotificationColor = (priority) => {
    switch (priority) {
      case "critical":
        return "border-l-red-500 bg-red-50"
      case "high":
        return "border-l-orange-500 bg-orange-50"
      case "medium":
        return "border-l-yellow-500 bg-yellow-50"
      case "low":
        return "border-l-blue-500 bg-blue-50"
      case "info":
        return "border-l-green-500 bg-green-50"
      default:
        return "border-l-gray-500 bg-gray-50"
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "critical":
        return "bg-red-100 text-red-800"
      case "high":
        return "bg-orange-100 text-orange-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "low":
        return "bg-blue-100 text-blue-800"
      case "info":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const markAsRead = (notificationId) => {
    setNotifications((prev) =>
      prev.map((notification) => (notification.id === notificationId ? { ...notification, read: true } : notification)),
    )
  }

  const dismissNotification = (notificationId) => {
    setNotifications((prev) => prev.filter((notification) => notification.id !== notificationId))
  }

  const filteredNotifications = notifications.filter((notification) => {
    if (filter === "all") return true
    if (filter === "unread") return !notification.read
    return notification.priority === filter
  })

  const unreadCount = notifications.filter((n) => !n.read).length

  const formatTimeAgo = (timestamp) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diffInHours = Math.floor((now - time) / (1000 * 60 * 60))

    if (diffInHours < 1) return "Just now"
    if (diffInHours < 24) return `${diffInHours}h ago`
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays}d ago`
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Bell className="h-5 w-5 mr-2 text-indigo-600" />
              Asset Notifications
              {unreadCount > 0 && <Badge className="ml-2 bg-red-500 hover:bg-red-500">{unreadCount}</Badge>}
            </CardTitle>
            <CardDescription>Stay updated on asset maintenance, warranties, and important changes</CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant={filter === "all" ? "default" : "outline"} size="sm" onClick={() => setFilter("all")}>
              All
            </Button>
            <Button variant={filter === "unread" ? "default" : "outline"} size="sm" onClick={() => setFilter("unread")}>
              Unread ({unreadCount})
            </Button>
            <Button
              variant={filter === "critical" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilter("critical")}
            >
              Critical
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {filteredNotifications.length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">All caught up!</h3>
              <p className="text-gray-600">No notifications match your current filter.</p>
            </div>
          ) : (
            filteredNotifications.map((notification) => (
              <Alert
                key={notification.id}
                className={`border-l-4 ${getNotificationColor(notification.priority)} ${
                  !notification.read ? "ring-2 ring-blue-100" : ""
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    <div className="p-2 rounded-lg bg-white">{getNotificationIcon(notification.type)}</div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-gray-900">{notification.title}</h4>
                        <Badge className={getPriorityColor(notification.priority)}>{notification.priority}</Badge>
                        {!notification.read && <div className="w-2 h-2 bg-blue-500 rounded-full"></div>}
                      </div>
                      <AlertDescription className="text-gray-700 mb-2">{notification.message}</AlertDescription>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span className="flex items-center">
                          <Package className="h-3 w-3 mr-1" />
                          {notification.assetName}
                        </span>
                        <span className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {formatTimeAgo(notification.timestamp)}
                        </span>
                        {notification.dueDate && (
                          <span className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            Due: {notification.dueDate}
                          </span>
                        )}
                        {notification.overdueDays && (
                          <span className="flex items-center text-red-600">
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            {notification.overdueDays} days overdue
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    {!notification.read && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => markAsRead(notification.id)}
                        className="text-blue-600 hover:text-blue-700"
                      >
                        Mark as read
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => dismissNotification(notification.id)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </Alert>
            ))
          )}
        </div>

        {/* Quick Actions */}
        {filteredNotifications.some((n) => n.type === "maintenance_due" || n.type === "maintenance_overdue") && (
          <div className="mt-6 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">Quick Actions</h3>
            <div className="flex flex-wrap gap-2">
              <Button size="sm" className="bg-gradient-to-r from-indigo-600 to-purple-600">
                <Wrench className="h-4 w-4 mr-2" />
                Schedule All Maintenance
              </Button>
              <Button size="sm" variant="outline">
                <Calendar className="h-4 w-4 mr-2" />
                View Maintenance Calendar
              </Button>
              <Button size="sm" variant="outline">
                <Package className="h-4 w-4 mr-2" />
                Asset Overview
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
