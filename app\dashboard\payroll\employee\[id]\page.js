"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { DollarSign, Calendar, Download, Mail, FileText, Receipt, ArrowLeft } from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import Link from "next/link"

export default function EmployeePayrollPage({ params }) {
  const [isPayslipOpen, setIsPayslipOpen] = useState(false)

  // Mock employee data - in real app, fetch based on params.id
  const employee = {
    id: "EMP001",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    department: "Engineering",
    position: "Senior Developer",
    hireDate: "2022-03-15",
    employeeType: "Full-time",
    salary: 95000,
    hourlyRate: 45.67,
    avatar: "/placeholder.svg?height=80&width=80",
  }

  const currentPayroll = {
    period: "January 2024",
    hoursWorked: 160,
    overtimeHours: 8,
    regularPay: 7307,
    overtimePay: 548,
    grossPay: 8200,
    federalTax: 1312,
    stateTax: 328,
    socialSecurity: 508,
    medicare: 119,
    benefits: 450,
    totalDeductions: 2717,
    netPay: 5483,
    payDate: "2024-01-31",
    status: "Processed",
  }

  const payrollHistory = [
    {
      period: "December 2023",
      grossPay: 7917,
      netPay: 5284,
      status: "Paid",
      payDate: "2023-12-31",
    },
    {
      period: "November 2023",
      grossPay: 7917,
      netPay: 5284,
      status: "Paid",
      payDate: "2023-11-30",
    },
    {
      period: "October 2023",
      grossPay: 8156,
      netPay: 5442,
      status: "Paid",
      payDate: "2023-10-31",
    },
  ]

  const benefits = [
    { name: "Health Insurance", employeeContribution: 150, employerContribution: 300 },
    { name: "Dental Insurance", employeeContribution: 25, employerContribution: 50 },
    { name: "Vision Insurance", employeeContribution: 15, employerContribution: 30 },
    { name: "401(k) Contribution", employeeContribution: 260, employerContribution: 130 },
  ]

  const taxWithholdings = [
    { name: "Federal Income Tax", amount: 1312, rate: "16.0%" },
    { name: "State Income Tax", amount: 328, rate: "4.0%" },
    { name: "Social Security", amount: 508, rate: "6.2%" },
    { name: "Medicare", amount: 119, rate: "1.45%" },
  ]

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/payroll">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Payroll
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Employee Payroll Details</h1>
              <p className="text-gray-600 mt-1">Comprehensive payroll information for {employee.name}</p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Mail className="h-4 w-4 mr-2" />
              Send Payslip
            </Button>
            <Dialog open={isPayslipOpen} onOpenChange={setIsPayslipOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                  <FileText className="h-4 w-4 mr-2" />
                  View Payslip
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Payslip - {currentPayroll.period}</DialogTitle>
                  <DialogDescription>Detailed payslip for {employee.name}</DialogDescription>
                </DialogHeader>
                <div className="space-y-6 py-4">
                  {/* Employee Info */}
                  <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={employee.avatar || "/placeholder.svg"} alt={employee.name} />
                      <AvatarFallback>
                        {employee.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-semibold">{employee.name}</h3>
                      <p className="text-sm text-gray-600">{employee.position}</p>
                      <p className="text-xs text-gray-500">Employee ID: {employee.id}</p>
                    </div>
                  </div>

                  {/* Earnings */}
                  <div>
                    <h4 className="font-semibold mb-3">Earnings</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Regular Pay ({currentPayroll.hoursWorked} hours)</span>
                        <span>{formatCurrency(currentPayroll.regularPay)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Overtime Pay ({currentPayroll.overtimeHours} hours)</span>
                        <span>{formatCurrency(currentPayroll.overtimePay)}</span>
                      </div>
                      <Separator />
                      <div className="flex justify-between font-semibold">
                        <span>Gross Pay</span>
                        <span>{formatCurrency(currentPayroll.grossPay)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Deductions */}
                  <div>
                    <h4 className="font-semibold mb-3">Deductions</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Federal Tax</span>
                        <span className="text-red-600">-{formatCurrency(currentPayroll.federalTax)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>State Tax</span>
                        <span className="text-red-600">-{formatCurrency(currentPayroll.stateTax)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Social Security</span>
                        <span className="text-red-600">-{formatCurrency(currentPayroll.socialSecurity)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Medicare</span>
                        <span className="text-red-600">-{formatCurrency(currentPayroll.medicare)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Benefits</span>
                        <span className="text-red-600">-{formatCurrency(currentPayroll.benefits)}</span>
                      </div>
                      <Separator />
                      <div className="flex justify-between font-semibold">
                        <span>Total Deductions</span>
                        <span className="text-red-600">-{formatCurrency(currentPayroll.totalDeductions)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Net Pay */}
                  <div className="p-4 bg-green-50 rounded-lg">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-semibold">Net Pay</span>
                      <span className="text-2xl font-bold text-green-600">{formatCurrency(currentPayroll.netPay)}</span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">Pay Date: {formatDate(currentPayroll.payDate)}</p>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download PDF
                  </Button>
                  <Button>
                    <Mail className="h-4 w-4 mr-2" />
                    Email Payslip
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Employee Overview Card */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-6">
              <Avatar className="h-20 w-20">
                <AvatarImage src={employee.avatar || "/placeholder.svg"} alt={employee.name} />
                <AvatarFallback className="text-lg">
                  {employee.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <h2 className="text-2xl font-bold text-gray-900">{employee.name}</h2>
                <p className="text-lg text-gray-600">{employee.position}</p>
                <div className="flex items-center space-x-4 mt-2">
                  <Badge variant="outline">{employee.department}</Badge>
                  <Badge variant="outline">{employee.employeeType}</Badge>
                  <span className="text-sm text-gray-500">ID: {employee.id}</span>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-600">Annual Salary</div>
                <div className="text-2xl font-bold text-green-600">{formatCurrency(employee.salary)}</div>
                <div className="text-sm text-gray-500">{formatCurrency(employee.hourlyRate)}/hour</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content Tabs */}
        <Tabs defaultValue="current" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="current">Current Payroll</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
            <TabsTrigger value="benefits">Benefits</TabsTrigger>
            <TabsTrigger value="taxes">Tax Details</TabsTrigger>
          </TabsList>

          {/* Current Payroll Tab */}
          <TabsContent value="current" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600">Gross Pay</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">{formatCurrency(currentPayroll.grossPay)}</div>
                  <p className="text-sm text-gray-600 mt-1">{currentPayroll.period}</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600">Total Deductions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-600">
                    -{formatCurrency(currentPayroll.totalDeductions)}
                  </div>
                  <p className="text-sm text-gray-600 mt-1">Taxes & Benefits</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600">Net Pay</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">{formatCurrency(currentPayroll.netPay)}</div>
                  <p className="text-sm text-gray-600 mt-1">Take-home pay</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600">Hours Worked</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">{currentPayroll.hoursWorked}</div>
                  <p className="text-sm text-gray-600 mt-1">+{currentPayroll.overtimeHours} overtime</p>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <DollarSign className="h-5 w-5 mr-2 text-green-600" />
                    Earnings Breakdown
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Regular Pay</span>
                      <span className="font-semibold">{formatCurrency(currentPayroll.regularPay)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Overtime Pay</span>
                      <span className="font-semibold">{formatCurrency(currentPayroll.overtimePay)}</span>
                    </div>
                    <Separator />
                    <div className="flex justify-between items-center font-semibold text-lg">
                      <span>Total Gross Pay</span>
                      <span className="text-green-600">{formatCurrency(currentPayroll.grossPay)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Receipt className="h-5 w-5 mr-2 text-red-600" />
                    Deductions Breakdown
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Federal Tax</span>
                      <span className="font-semibold text-red-600">-{formatCurrency(currentPayroll.federalTax)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">State Tax</span>
                      <span className="font-semibold text-red-600">-{formatCurrency(currentPayroll.stateTax)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">FICA Taxes</span>
                      <span className="font-semibold text-red-600">
                        -{formatCurrency(currentPayroll.socialSecurity + currentPayroll.medicare)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Benefits</span>
                      <span className="font-semibold text-red-600">-{formatCurrency(currentPayroll.benefits)}</span>
                    </div>
                    <Separator />
                    <div className="flex justify-between items-center font-semibold text-lg">
                      <span>Total Deductions</span>
                      <span className="text-red-600">-{formatCurrency(currentPayroll.totalDeductions)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* History Tab */}
          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Payroll History</CardTitle>
                <CardDescription>Previous payroll periods and payments</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {payrollHistory.map((payroll, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <Calendar className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <div className="font-medium">{payroll.period}</div>
                          <div className="text-sm text-gray-600">Paid on {formatDate(payroll.payDate)}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">{formatCurrency(payroll.grossPay)}</div>
                        <div className="text-sm text-green-600">Net: {formatCurrency(payroll.netPay)}</div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className="bg-green-100 text-green-800">{payroll.status}</Badge>
                        <Button variant="ghost" size="sm">
                          <FileText className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Benefits Tab */}
          <TabsContent value="benefits" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Employee Benefits</CardTitle>
                <CardDescription>Current benefit enrollments and contributions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {benefits.map((benefit, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium">{benefit.name}</h4>
                        <Badge variant="outline">Active</Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <div className="text-sm text-gray-600">Employee Contribution</div>
                          <div className="font-semibold text-red-600">
                            -{formatCurrency(benefit.employeeContribution)}
                          </div>
                        </div>
                        <div>
                          <div className="text-sm text-gray-600">Employer Contribution</div>
                          <div className="font-semibold text-green-600">
                            +{formatCurrency(benefit.employerContribution)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tax Details Tab */}
          <TabsContent value="taxes" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Tax Withholdings</CardTitle>
                <CardDescription>Current tax withholding details and rates</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {taxWithholdings.map((tax, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{tax.name}</h4>
                          <div className="text-sm text-gray-600">Rate: {tax.rate}</div>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold text-red-600">-{formatCurrency(tax.amount)}</div>
                          <div className="text-sm text-gray-600">This period</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
