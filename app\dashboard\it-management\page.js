"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import {
  Plus,
  Search,
  Download,
  Shield,
  Server,
  Cloud,
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  DollarSign,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Key,
  HardDrive,
  Wifi,
  Activity,
  BarChart3,
  FileText,
  Settings,
  Database,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"

export default function ITManagementPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [isAddLicenseOpen, setIsAddLicenseOpen] = useState(false)
  const [isAddInfrastructureOpen, setIsAddInfrastructureOpen] = useState(false)

  // Mock data for software licenses
  const [licenses] = useState([
    {
      id: "LIC-001",
      name: "Microsoft Office 365",
      vendor: "Microsoft",
      type: "Subscription",
      totalLicenses: 150,
      usedLicenses: 142,
      availableLicenses: 8,
      costPerLicense: 12.5,
      totalCost: 1875.0,
      renewalDate: "2024-12-15",
      status: "Active",
      compliance: "Compliant",
      category: "Productivity",
      lastAudit: "2024-01-15",
    },
    {
      id: "LIC-002",
      name: "Adobe Creative Suite",
      vendor: "Adobe",
      type: "Subscription",
      totalLicenses: 25,
      usedLicenses: 28,
      availableLicenses: -3,
      costPerLicense: 52.99,
      totalCost: 1324.75,
      renewalDate: "2024-06-30",
      status: "Over-allocated",
      compliance: "Non-compliant",
      category: "Design",
      lastAudit: "2024-01-10",
    },
    {
      id: "LIC-003",
      name: "Slack Business+",
      vendor: "Slack",
      type: "Subscription",
      totalLicenses: 200,
      usedLicenses: 185,
      availableLicenses: 15,
      costPerLicense: 12.5,
      totalCost: 2500.0,
      renewalDate: "2024-08-20",
      status: "Active",
      compliance: "Compliant",
      category: "Communication",
      lastAudit: "2024-01-20",
    },
    {
      id: "LIC-004",
      name: "JetBrains IntelliJ IDEA",
      vendor: "JetBrains",
      type: "Annual",
      totalLicenses: 50,
      usedLicenses: 45,
      availableLicenses: 5,
      costPerLicense: 149.0,
      totalCost: 7450.0,
      renewalDate: "2024-03-15",
      status: "Expiring Soon",
      compliance: "Compliant",
      category: "Development",
      lastAudit: "2024-01-05",
    },
  ])

  // Mock data for SaaS applications
  const [saasApps] = useState([
    {
      id: "SAAS-001",
      name: "Salesforce",
      category: "CRM",
      users: 85,
      monthlySpend: 8500.0,
      riskLevel: "Low",
      lastUsed: "2024-02-10",
      integrations: 12,
      dataClassification: "Sensitive",
      vendor: "Salesforce.com",
      contractEnd: "2024-11-30",
    },
    {
      id: "SAAS-002",
      name: "Zoom",
      category: "Communication",
      users: 200,
      monthlySpend: 1200.0,
      riskLevel: "Medium",
      lastUsed: "2024-02-10",
      integrations: 5,
      dataClassification: "Internal",
      vendor: "Zoom Video Communications",
      contractEnd: "2024-09-15",
    },
    {
      id: "SAAS-003",
      name: "GitHub Enterprise",
      category: "Development",
      users: 75,
      monthlySpend: 2100.0,
      riskLevel: "Low",
      lastUsed: "2024-02-10",
      integrations: 8,
      dataClassification: "Confidential",
      vendor: "GitHub Inc.",
      contractEnd: "2025-01-20",
    },
    {
      id: "SAAS-004",
      name: "Dropbox Business",
      category: "Storage",
      users: 150,
      monthlySpend: 1800.0,
      riskLevel: "High",
      lastUsed: "2024-01-25",
      integrations: 3,
      dataClassification: "Sensitive",
      vendor: "Dropbox Inc.",
      contractEnd: "2024-07-10",
    },
  ])

  // Mock data for IT infrastructure
  const [infrastructure] = useState([
    {
      id: "INF-001",
      name: "Production Web Server",
      type: "Server",
      status: "Online",
      health: 95,
      location: "Data Center A",
      os: "Ubuntu 22.04 LTS",
      cpu: "Intel Xeon E5-2680 v4",
      memory: "64 GB",
      storage: "2 TB SSD",
      uptime: "99.9%",
      lastMaintenance: "2024-01-15",
      nextMaintenance: "2024-04-15",
    },
    {
      id: "INF-002",
      name: "Database Server Primary",
      type: "Database",
      status: "Online",
      health: 88,
      location: "Data Center A",
      os: "CentOS 8",
      cpu: "AMD EPYC 7742",
      memory: "128 GB",
      storage: "4 TB NVMe",
      uptime: "99.8%",
      lastMaintenance: "2024-01-10",
      nextMaintenance: "2024-04-10",
    },
    {
      id: "INF-003",
      name: "Office Network Switch",
      type: "Network",
      status: "Warning",
      health: 75,
      location: "Office Floor 1",
      os: "Cisco IOS",
      cpu: "ARM Cortex-A9",
      memory: "4 GB",
      storage: "512 MB Flash",
      uptime: "98.5%",
      lastMaintenance: "2023-12-20",
      nextMaintenance: "2024-03-20",
    },
    {
      id: "INF-004",
      name: "Backup Storage Array",
      type: "Storage",
      status: "Critical",
      health: 45,
      location: "Data Center B",
      os: "FreeNAS",
      cpu: "Intel Xeon Silver 4214",
      memory: "32 GB",
      storage: "50 TB RAID 6",
      uptime: "95.2%",
      lastMaintenance: "2023-11-30",
      nextMaintenance: "2024-02-15",
    },
  ])

  // Mock data for security compliance
  const [securityMetrics] = useState({
    overallScore: 78,
    vulnerabilities: {
      critical: 2,
      high: 8,
      medium: 15,
      low: 23,
    },
    policies: {
      total: 45,
      compliant: 38,
      nonCompliant: 7,
    },
    lastScan: "2024-02-09",
    nextScan: "2024-02-16",
  })

  const stats = [
    {
      title: "Software Licenses",
      value: "324",
      change: "+12",
      icon: Key,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Monthly SaaS Spend",
      value: "$13.6K",
      change: "****%",
      icon: DollarSign,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Infrastructure Health",
      value: "87%",
      change: "-2.1%",
      icon: Server,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
    {
      title: "Security Score",
      value: "78/100",
      change: "+5",
      icon: Shield,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
  ]

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case "active":
      case "online":
      case "compliant":
        return "bg-green-100 text-green-800"
      case "expiring soon":
      case "warning":
        return "bg-yellow-100 text-yellow-800"
      case "over-allocated":
      case "critical":
      case "non-compliant":
        return "bg-red-100 text-red-800"
      case "offline":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getRiskColor = (risk) => {
    switch (risk.toLowerCase()) {
      case "low":
        return "bg-green-100 text-green-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "high":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getHealthColor = (health) => {
    if (health >= 90) return "text-green-600"
    if (health >= 70) return "text-yellow-600"
    return "text-red-600"
  }

  const handleAddLicense = (e) => {
    e.preventDefault()
    setIsAddLicenseOpen(false)
    // Handle license creation logic
  }

  const handleAddInfrastructure = (e) => {
    e.preventDefault()
    setIsAddInfrastructureOpen(false)
    // Handle infrastructure creation logic
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">IT Management</h1>
            <p className="text-gray-600 mt-1">Manage software licenses, SaaS applications, and IT infrastructure</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" className="flex items-center bg-transparent">
              <Activity className="h-4 w-4 mr-2" />
              System Health
            </Button>
            <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700">
              <Plus className="h-4 w-4 mr-2" />
              Add Resource
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className={`text-sm ${stat.change.startsWith("+") ? "text-green-600" : "text-red-600"}`}>
                      {stat.change} from last month
                    </p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content */}
        <Tabs defaultValue="licenses" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="licenses">Software Licenses</TabsTrigger>
            <TabsTrigger value="saas">SaaS Discovery</TabsTrigger>
            <TabsTrigger value="infrastructure">Infrastructure</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>

          <TabsContent value="licenses" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center">
                      <Key className="h-5 w-5 mr-2 text-blue-600" />
                      Software License Management
                    </CardTitle>
                    <CardDescription>Track software licenses, usage, and compliance</CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                    <Dialog open={isAddLicenseOpen} onOpenChange={setIsAddLicenseOpen}>
                      <DialogTrigger asChild>
                        <Button size="sm" className="bg-gradient-to-r from-indigo-600 to-purple-600">
                          <Plus className="h-4 w-4 mr-2" />
                          Add License
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[600px]">
                        <DialogHeader>
                          <DialogTitle>Add Software License</DialogTitle>
                          <DialogDescription>Register a new software license for tracking</DialogDescription>
                        </DialogHeader>
                        <form onSubmit={handleAddLicense}>
                          <div className="grid gap-4 py-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor="license-name">Software Name</Label>
                                <Input id="license-name" placeholder="Enter software name" required />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="license-vendor">Vendor</Label>
                                <Input id="license-vendor" placeholder="Software vendor" required />
                              </div>
                            </div>
                            <div className="grid grid-cols-3 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor="license-type">License Type</Label>
                                <Select required>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select type" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="subscription">Subscription</SelectItem>
                                    <SelectItem value="perpetual">Perpetual</SelectItem>
                                    <SelectItem value="annual">Annual</SelectItem>
                                    <SelectItem value="concurrent">Concurrent</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="total-licenses">Total Licenses</Label>
                                <Input id="total-licenses" type="number" placeholder="0" required />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="cost-per-license">Cost per License</Label>
                                <Input id="cost-per-license" type="number" placeholder="0.00" required />
                              </div>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor="renewal-date">Renewal Date</Label>
                                <Input id="renewal-date" type="date" required />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="category">Category</Label>
                                <Select required>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select category" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="productivity">Productivity</SelectItem>
                                    <SelectItem value="development">Development</SelectItem>
                                    <SelectItem value="design">Design</SelectItem>
                                    <SelectItem value="communication">Communication</SelectItem>
                                    <SelectItem value="security">Security</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="license-notes">Notes</Label>
                              <Textarea id="license-notes" placeholder="Additional license information" />
                            </div>
                          </div>
                          <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setIsAddLicenseOpen(false)}>
                              Cancel
                            </Button>
                            <Button type="submit" className="bg-gradient-to-r from-indigo-600 to-purple-600">
                              Add License
                            </Button>
                          </DialogFooter>
                        </form>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search licenses..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="expiring">Expiring Soon</SelectItem>
                      <SelectItem value="over-allocated">Over-allocated</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      <SelectItem value="productivity">Productivity</SelectItem>
                      <SelectItem value="development">Development</SelectItem>
                      <SelectItem value="design">Design</SelectItem>
                      <SelectItem value="communication">Communication</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Software</TableHead>
                        <TableHead>License Usage</TableHead>
                        <TableHead>Cost</TableHead>
                        <TableHead>Renewal Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Compliance</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {licenses.map((license) => (
                        <TableRow key={license.id}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <div className="bg-gradient-to-r from-blue-100 to-indigo-100 p-2 rounded-lg">
                                <Key className="h-4 w-4 text-blue-600" />
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">{license.name}</div>
                                <div className="text-sm text-gray-500">
                                  {license.vendor} • {license.type}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-2">
                              <div className="flex items-center justify-between text-sm">
                                <span>
                                  {license.usedLicenses} / {license.totalLicenses}
                                </span>
                                <span
                                  className={
                                    license.availableLicenses < 0
                                      ? "text-red-600"
                                      : license.availableLicenses < 5
                                        ? "text-yellow-600"
                                        : "text-green-600"
                                  }
                                >
                                  {license.availableLicenses < 0 ? "Over by " : ""}
                                  {Math.abs(license.availableLicenses)} available
                                </span>
                              </div>
                              <Progress value={(license.usedLicenses / license.totalLicenses) * 100} className="h-2" />
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">${license.totalCost.toLocaleString()}/mo</div>
                              <div className="text-xs text-gray-500">${license.costPerLicense}/license</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">{license.renewalDate}</div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(license.status)}>{license.status}</Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(license.compliance)}>{license.compliance}</Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  View Details
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Users className="mr-2 h-4 w-4" />
                                  Manage Users
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <BarChart3 className="mr-2 h-4 w-4" />
                                  Usage Analytics
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit License
                                </DropdownMenuItem>
                                <DropdownMenuItem className="text-red-600">
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Remove License
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="saas" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center">
                      <Cloud className="h-5 w-5 mr-2 text-green-600" />
                      SaaS Application Discovery
                    </CardTitle>
                    <CardDescription>Discover and manage SaaS applications across your organization</CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Activity className="h-4 w-4 mr-2" />
                      Scan Network
                    </Button>
                    <Button size="sm" className="bg-gradient-to-r from-green-600 to-blue-600">
                      <Plus className="h-4 w-4 mr-2" />
                      Add SaaS App
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-6">
                  <div className="lg:col-span-3">
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Application</TableHead>
                            <TableHead>Users</TableHead>
                            <TableHead>Monthly Spend</TableHead>
                            <TableHead>Risk Level</TableHead>
                            <TableHead>Data Classification</TableHead>
                            <TableHead>Last Used</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {saasApps.map((app) => (
                            <TableRow key={app.id}>
                              <TableCell>
                                <div className="flex items-center space-x-3">
                                  <div className="bg-gradient-to-r from-green-100 to-blue-100 p-2 rounded-lg">
                                    <Cloud className="h-4 w-4 text-green-600" />
                                  </div>
                                  <div>
                                    <div className="font-medium text-gray-900">{app.name}</div>
                                    <div className="text-sm text-gray-500">
                                      {app.vendor} • {app.category}
                                    </div>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center space-x-2">
                                  <Users className="h-4 w-4 text-gray-400" />
                                  <span>{app.users}</span>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="font-medium">${app.monthlySpend.toLocaleString()}</div>
                              </TableCell>
                              <TableCell>
                                <Badge className={getRiskColor(app.riskLevel)}>{app.riskLevel}</Badge>
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline">{app.dataClassification}</Badge>
                              </TableCell>
                              <TableCell>
                                <div className="text-sm">{app.lastUsed}</div>
                              </TableCell>
                              <TableCell className="text-right">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" className="h-8 w-8 p-0">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                    <DropdownMenuItem>
                                      <Eye className="mr-2 h-4 w-4" />
                                      View Details
                                    </DropdownMenuItem>
                                    <DropdownMenuItem>
                                      <Users className="mr-2 h-4 w-4" />
                                      Manage Access
                                    </DropdownMenuItem>
                                    <DropdownMenuItem>
                                      <Shield className="mr-2 h-4 w-4" />
                                      Security Review
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem>
                                      <Settings className="mr-2 h-4 w-4" />
                                      Configure
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                  <div>
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">SaaS Insights</h3>
                      <div className="space-y-3">
                        <div className="p-4 bg-blue-50 rounded-lg">
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="text-2xl font-bold text-blue-600">47</div>
                              <div className="text-sm text-blue-700">Total Applications</div>
                            </div>
                            <Cloud className="h-8 w-8 text-blue-600" />
                          </div>
                        </div>
                        <div className="p-4 bg-green-50 rounded-lg">
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="text-2xl font-bold text-green-600">$13.6K</div>
                              <div className="text-sm text-green-700">Monthly Spend</div>
                            </div>
                            <DollarSign className="h-8 w-8 text-green-600" />
                          </div>
                        </div>
                        <div className="p-4 bg-red-50 rounded-lg">
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="text-2xl font-bold text-red-600">8</div>
                              <div className="text-sm text-red-700">High Risk Apps</div>
                            </div>
                            <AlertTriangle className="h-8 w-8 text-red-600" />
                          </div>
                        </div>
                        <div className="p-4 bg-purple-50 rounded-lg">
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="text-2xl font-bold text-purple-600">1,247</div>
                              <div className="text-sm text-purple-700">Total Users</div>
                            </div>
                            <Users className="h-8 w-8 text-purple-600" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="infrastructure" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center">
                      <Server className="h-5 w-5 mr-2 text-orange-600" />
                      IT Infrastructure Management
                    </CardTitle>
                    <CardDescription>Monitor and manage servers, networks, and IT infrastructure</CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Activity className="h-4 w-4 mr-2" />
                      Health Check
                    </Button>
                    <Dialog open={isAddInfrastructureOpen} onOpenChange={setIsAddInfrastructureOpen}>
                      <DialogTrigger asChild>
                        <Button size="sm" className="bg-gradient-to-r from-orange-600 to-red-600">
                          <Plus className="h-4 w-4 mr-2" />
                          Add Infrastructure
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[600px]">
                        <DialogHeader>
                          <DialogTitle>Add Infrastructure Component</DialogTitle>
                          <DialogDescription>Register a new infrastructure component for monitoring</DialogDescription>
                        </DialogHeader>
                        <form onSubmit={handleAddInfrastructure}>
                          <div className="grid gap-4 py-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor="infra-name">Component Name</Label>
                                <Input id="infra-name" placeholder="Enter component name" required />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="infra-type">Type</Label>
                                <Select required>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select type" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="server">Server</SelectItem>
                                    <SelectItem value="database">Database</SelectItem>
                                    <SelectItem value="network">Network</SelectItem>
                                    <SelectItem value="storage">Storage</SelectItem>
                                    <SelectItem value="security">Security</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor="infra-location">Location</Label>
                                <Input id="infra-location" placeholder="Physical location" required />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="infra-os">Operating System</Label>
                                <Input id="infra-os" placeholder="OS version" />
                              </div>
                            </div>
                            <div className="grid grid-cols-3 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor="infra-cpu">CPU</Label>
                                <Input id="infra-cpu" placeholder="CPU specification" />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="infra-memory">Memory</Label>
                                <Input id="infra-memory" placeholder="RAM amount" />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="infra-storage">Storage</Label>
                                <Input id="infra-storage" placeholder="Storage capacity" />
                              </div>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="infra-description">Description</Label>
                              <Textarea id="infra-description" placeholder="Component description and notes" />
                            </div>
                          </div>
                          <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setIsAddInfrastructureOpen(false)}>
                              Cancel
                            </Button>
                            <Button type="submit" className="bg-gradient-to-r from-orange-600 to-red-600">
                              Add Component
                            </Button>
                          </DialogFooter>
                        </form>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Component</TableHead>
                        <TableHead>Health</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Specifications</TableHead>
                        <TableHead>Uptime</TableHead>
                        <TableHead>Next Maintenance</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {infrastructure.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <div className="bg-gradient-to-r from-orange-100 to-red-100 p-2 rounded-lg">
                                {item.type === "Server" && <Server className="h-4 w-4 text-orange-600" />}
                                {item.type === "Database" && <Database className="h-4 w-4 text-orange-600" />}
                                {item.type === "Network" && <Wifi className="h-4 w-4 text-orange-600" />}
                                {item.type === "Storage" && <HardDrive className="h-4 w-4 text-orange-600" />}
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">{item.name}</div>
                                <div className="text-sm text-gray-500">
                                  {item.id} • {item.type}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                  className={`h-2 rounded-full ${
                                    item.health >= 90
                                      ? "bg-green-500"
                                      : item.health >= 70
                                        ? "bg-yellow-500"
                                        : "bg-red-500"
                                  }`}
                                  style={{ width: `${item.health}%` }}
                                ></div>
                              </div>
                              <span className={`text-sm font-medium ${getHealthColor(item.health)}`}>
                                {item.health}%
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(item.status)}>{item.status}</Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">{item.location}</div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div>{item.cpu}</div>
                              <div className="text-gray-500">
                                {item.memory} • {item.storage}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm font-medium">{item.uptime}</div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">{item.nextMaintenance}</div>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  View Details
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Activity className="mr-2 h-4 w-4" />
                                  Performance Metrics
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Settings className="mr-2 h-4 w-4" />
                                  Configure Monitoring
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit Component
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Shield className="h-5 w-5 mr-2 text-purple-600" />
                      Security Compliance Dashboard
                    </CardTitle>
                    <CardDescription>Monitor security posture and compliance status</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-lg font-semibold">Overall Security Score</h3>
                          <p className="text-sm text-gray-600">Based on policies, vulnerabilities, and compliance</p>
                        </div>
                        <div className="text-right">
                          <div className="text-3xl font-bold text-purple-600">{securityMetrics.overallScore}/100</div>
                          <div className="text-sm text-gray-500">Good</div>
                        </div>
                      </div>
                      <Progress value={securityMetrics.overallScore} className="h-3" />

                      <div className="grid grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-medium mb-3">Vulnerabilities</h4>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between p-2 bg-red-50 rounded">
                              <span className="text-sm">Critical</span>
                              <Badge className="bg-red-100 text-red-800">
                                {securityMetrics.vulnerabilities.critical}
                              </Badge>
                            </div>
                            <div className="flex items-center justify-between p-2 bg-orange-50 rounded">
                              <span className="text-sm">High</span>
                              <Badge className="bg-orange-100 text-orange-800">
                                {securityMetrics.vulnerabilities.high}
                              </Badge>
                            </div>
                            <div className="flex items-center justify-between p-2 bg-yellow-50 rounded">
                              <span className="text-sm">Medium</span>
                              <Badge className="bg-yellow-100 text-yellow-800">
                                {securityMetrics.vulnerabilities.medium}
                              </Badge>
                            </div>
                            <div className="flex items-center justify-between p-2 bg-blue-50 rounded">
                              <span className="text-sm">Low</span>
                              <Badge className="bg-blue-100 text-blue-800">{securityMetrics.vulnerabilities.low}</Badge>
                            </div>
                          </div>
                        </div>
                        <div>
                          <h4 className="font-medium mb-3">Policy Compliance</h4>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between p-2 bg-green-50 rounded">
                              <span className="text-sm">Compliant</span>
                              <Badge className="bg-green-100 text-green-800">
                                {securityMetrics.policies.compliant}
                              </Badge>
                            </div>
                            <div className="flex items-center justify-between p-2 bg-red-50 rounded">
                              <span className="text-sm">Non-compliant</span>
                              <Badge className="bg-red-100 text-red-800">{securityMetrics.policies.nonCompliant}</Badge>
                            </div>
                            <div className="mt-4">
                              <div className="text-sm text-gray-600">
                                Compliance Rate:{" "}
                                {Math.round(
                                  (securityMetrics.policies.compliant / securityMetrics.policies.total) * 100,
                                )}
                                %
                              </div>
                              <Progress
                                value={(securityMetrics.policies.compliant / securityMetrics.policies.total) * 100}
                                className="h-2 mt-1"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Recent Security Events</CardTitle>
                    <CardDescription>Latest security alerts and compliance issues</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-start space-x-3 p-4 border-l-4 border-l-red-500 bg-red-50">
                        <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">Critical Vulnerability Detected</div>
                          <div className="text-sm text-gray-600">CVE-2024-1234 found in production web server</div>
                          <div className="text-xs text-gray-500 mt-1">2 hours ago • Production Environment</div>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3 p-4 border-l-4 border-l-yellow-500 bg-yellow-50">
                        <Clock className="h-5 w-5 text-yellow-600 mt-0.5" />
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">Policy Compliance Check</div>
                          <div className="text-sm text-gray-600">Password policy compliance scan completed</div>
                          <div className="text-xs text-gray-500 mt-1">6 hours ago • Security Team</div>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3 p-4 border-l-4 border-l-green-500 bg-green-50">
                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">Security Patch Applied</div>
                          <div className="text-sm text-gray-600">Security updates applied to all database servers</div>
                          <div className="text-xs text-gray-500 mt-1">1 day ago • IT Operations</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Security Actions</CardTitle>
                    <CardDescription>Quick security management actions</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button className="w-full bg-gradient-to-r from-purple-600 to-indigo-600">
                      <Shield className="h-4 w-4 mr-2" />
                      Run Security Scan
                    </Button>
                    <Button variant="outline" className="w-full bg-transparent">
                      <FileText className="h-4 w-4 mr-2" />
                      Generate Compliance Report
                    </Button>
                    <Button variant="outline" className="w-full bg-transparent">
                      <Settings className="h-4 w-4 mr-2" />
                      Configure Policies
                    </Button>
                    <Button variant="outline" className="w-full bg-transparent">
                      <Eye className="h-4 w-4 mr-2" />
                      View Audit Log
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Scan Schedule</CardTitle>
                    <CardDescription>Automated security scanning schedule</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <div className="font-medium text-sm">Last Scan</div>
                          <div className="text-xs text-gray-600">{securityMetrics.lastScan}</div>
                        </div>
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      </div>
                      <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <div>
                          <div className="font-medium text-sm">Next Scan</div>
                          <div className="text-xs text-gray-600">{securityMetrics.nextScan}</div>
                        </div>
                        <Clock className="h-5 w-5 text-blue-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Compliance Standards</CardTitle>
                    <CardDescription>Current compliance status</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">SOC 2</span>
                        <Badge className="bg-green-100 text-green-800">Compliant</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">ISO 27001</span>
                        <Badge className="bg-green-100 text-green-800">Compliant</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">GDPR</span>
                        <Badge className="bg-yellow-100 text-yellow-800">In Progress</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">HIPAA</span>
                        <Badge className="bg-red-100 text-red-800">Non-compliant</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="reports" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-green-600" />
                  IT Management Reports
                </CardTitle>
                <CardDescription>Generate comprehensive reports for IT assets and compliance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="p-6 border rounded-lg hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-semibold">Software License Report</h3>
                      <Key className="h-5 w-5 text-blue-600" />
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      Complete software license inventory with usage and compliance status
                    </p>
                    <Button size="sm" className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      Generate Report
                    </Button>
                  </div>
                  <div className="p-6 border rounded-lg hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-semibold">SaaS Spend Analysis</h3>
                      <DollarSign className="h-5 w-5 text-green-600" />
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      Detailed analysis of SaaS spending and optimization opportunities
                    </p>
                    <Button size="sm" className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      Generate Report
                    </Button>
                  </div>
                  <div className="p-6 border rounded-lg hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-semibold">Infrastructure Health</h3>
                      <Server className="h-5 w-5 text-orange-600" />
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      Infrastructure performance metrics and maintenance schedules
                    </p>
                    <Button size="sm" className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      Generate Report
                    </Button>
                  </div>
                  <div className="p-6 border rounded-lg hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-semibold">Security Compliance</h3>
                      <Shield className="h-5 w-5 text-purple-600" />
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      Security posture assessment and compliance audit results
                    </p>
                    <Button size="sm" className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      Generate Report
                    </Button>
                  </div>
                  <div className="p-6 border rounded-lg hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-semibold">IT Cost Analysis</h3>
                      <BarChart3 className="h-5 w-5 text-indigo-600" />
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      Total IT cost breakdown and budget optimization recommendations
                    </p>
                    <Button size="sm" className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      Generate Report
                    </Button>
                  </div>
                  <div className="p-6 border rounded-lg hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-semibold">Custom Report</h3>
                      <Settings className="h-5 w-5 text-gray-600" />
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      Create custom reports with specific metrics and filters
                    </p>
                    <Button size="sm" className="w-full">
                      <Plus className="h-4 w-4 mr-2" />
                      Create Custom
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
