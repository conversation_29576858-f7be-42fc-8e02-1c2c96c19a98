"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Package,
  QrCode,
  Calendar,
  DollarSign,
  MapPin,
  User,
  Wrench,
  FileText,
  Download,
  Edit,
  Plus,
  Clock,
  AlertTriangle,
  CheckCircle,
  TrendingDown,
  Upload,
  ArrowLeft,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import Link from "next/link"
import { useParams } from "next/navigation"

export default function AssetDetailPage() {
  const params = useParams()
  const [isMaintenanceOpen, setIsMaintenanceOpen] = useState(false)
  const [isEditOpen, setIsEditOpen] = useState(false)

  // Mock asset data - in real app, fetch based on params.id
  const asset = {
    id: "AST-001",
    name: "MacBook Pro 16-inch",
    category: "IT Equipment",
    status: "Active",
    assignedTo: "John Doe",
    assignedEmail: "<EMAIL>",
    location: "Office Floor 2",
    purchaseDate: "2023-01-15",
    purchasePrice: 2499.0,
    currentValue: 1874.25,
    depreciation: 25.0,
    nextMaintenance: "2024-03-15",
    condition: "Excellent",
    qrCode: "QR001",
    warrantyExpiry: "2025-01-15",
    serialNumber: "MBP2023001",
    model: "MacBook Pro 16-inch M2",
    manufacturer: "Apple Inc.",
    supplier: "Apple Store",
    description: "High-performance laptop for development work",
    notes: "Assigned to senior developer for React Native projects",
  }

  const maintenanceHistory = [
    {
      id: 1,
      date: "2023-12-15",
      type: "Preventive",
      description: "Software updates and system optimization",
      cost: 0,
      technician: "IT Support",
      status: "Completed",
      nextDue: "2024-03-15",
    },
    {
      id: 2,
      date: "2023-09-20",
      type: "Repair",
      description: "Keyboard replacement due to sticky keys",
      cost: 299.0,
      technician: "Apple Service Center",
      status: "Completed",
      nextDue: "N/A",
    },
    {
      id: 3,
      date: "2023-06-10",
      type: "Preventive",
      description: "Battery health check and calibration",
      cost: 0,
      technician: "IT Support",
      status: "Completed",
      nextDue: "2023-12-10",
    },
  ]

  const depreciationSchedule = [
    { year: 2023, startValue: 2499.0, depreciation: 312.38, endValue: 2186.62 },
    { year: 2024, startValue: 2186.62, depreciation: 312.38, endValue: 1874.24 },
    { year: 2025, startValue: 1874.24, depreciation: 312.38, endValue: 1561.86 },
    { year: 2026, startValue: 1561.86, depreciation: 312.38, endValue: 1249.48 },
    { year: 2027, startValue: 1249.48, depreciation: 312.38, endValue: 937.1 },
  ]

  const documents = [
    { id: 1, name: "Purchase Invoice", type: "PDF", size: "245 KB", date: "2023-01-15" },
    { id: 2, name: "Warranty Certificate", type: "PDF", size: "156 KB", date: "2023-01-15" },
    { id: 3, name: "User Manual", type: "PDF", size: "2.1 MB", date: "2023-01-15" },
    { id: 4, name: "Service Report", type: "PDF", size: "89 KB", date: "2023-09-20" },
  ]

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800"
      case "maintenance":
        return "bg-yellow-100 text-yellow-800"
      case "retired":
        return "bg-gray-100 text-gray-800"
      case "disposed":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getConditionColor = (condition) => {
    switch (condition.toLowerCase()) {
      case "excellent":
        return "bg-green-100 text-green-800"
      case "good":
        return "bg-blue-100 text-blue-800"
      case "fair":
        return "bg-yellow-100 text-yellow-800"
      case "poor":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const handleScheduleMaintenance = (e) => {
    e.preventDefault()
    setIsMaintenanceOpen(false)
    // Handle maintenance scheduling logic
  }

  const handleEditAsset = (e) => {
    e.preventDefault()
    setIsEditOpen(false)
    // Handle asset editing logic
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/assets">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Assets
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{asset.name}</h1>
              <p className="text-gray-600 mt-1">
                {asset.id} • {asset.category}
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <QrCode className="h-4 w-4 mr-2" />
              View QR Code
            </Button>
            <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Asset
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Edit Asset</DialogTitle>
                  <DialogDescription>Update asset information and details</DialogDescription>
                </DialogHeader>
                <form onSubmit={handleEditAsset}>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="edit-name">Asset Name</Label>
                        <Input id="edit-name" defaultValue={asset.name} />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="edit-status">Status</Label>
                        <Select defaultValue={asset.status.toLowerCase()}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="maintenance">Maintenance</SelectItem>
                            <SelectItem value="retired">Retired</SelectItem>
                            <SelectItem value="disposed">Disposed</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="edit-location">Location</Label>
                        <Input id="edit-location" defaultValue={asset.location} />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="edit-assigned">Assigned To</Label>
                        <Input id="edit-assigned" defaultValue={asset.assignedTo} />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edit-notes">Notes</Label>
                      <Textarea id="edit-notes" defaultValue={asset.notes} />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setIsEditOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" className="bg-gradient-to-r from-indigo-600 to-purple-600">
                      Save Changes
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
            <Dialog open={isMaintenanceOpen} onOpenChange={setIsMaintenanceOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700">
                  <Wrench className="h-4 w-4 mr-2" />
                  Schedule Maintenance
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Schedule Maintenance</DialogTitle>
                  <DialogDescription>Schedule maintenance for {asset.name}</DialogDescription>
                </DialogHeader>
                <form onSubmit={handleScheduleMaintenance}>
                  <div className="grid gap-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="maintenance-type">Maintenance Type</Label>
                      <Select required>
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="preventive">Preventive</SelectItem>
                          <SelectItem value="repair">Repair</SelectItem>
                          <SelectItem value="inspection">Inspection</SelectItem>
                          <SelectItem value="upgrade">Upgrade</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="maintenance-date">Scheduled Date</Label>
                        <Input id="maintenance-date" type="date" required />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="maintenance-cost">Estimated Cost</Label>
                        <Input id="maintenance-cost" type="number" placeholder="0.00" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="maintenance-technician">Technician</Label>
                      <Input id="maintenance-technician" placeholder="Assigned technician" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="maintenance-description">Description</Label>
                      <Textarea id="maintenance-description" placeholder="Maintenance details" />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setIsMaintenanceOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" className="bg-gradient-to-r from-indigo-600 to-purple-600">
                      Schedule Maintenance
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Asset Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Current Value</p>
                  <p className="text-2xl font-bold text-gray-900">${asset.currentValue.toLocaleString()}</p>
                  <p className="text-sm text-red-600">-{asset.depreciation}% depreciated</p>
                </div>
                <div className="p-3 rounded-full bg-green-100">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Status</p>
                  <Badge className={`${getStatusColor(asset.status)} text-lg px-3 py-1 mt-2`}>{asset.status}</Badge>
                  <p className="text-sm text-gray-500 mt-1">Condition: {asset.condition}</p>
                </div>
                <div className="p-3 rounded-full bg-blue-100">
                  <Package className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Next Maintenance</p>
                  <p className="text-lg font-bold text-gray-900">{asset.nextMaintenance}</p>
                  <p className="text-sm text-orange-600">In 45 days</p>
                </div>
                <div className="p-3 rounded-full bg-orange-100">
                  <Calendar className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Warranty</p>
                  <p className="text-lg font-bold text-gray-900">{asset.warrantyExpiry}</p>
                  <p className="text-sm text-green-600">11 months left</p>
                </div>
                <div className="p-3 rounded-full bg-purple-100">
                  <CheckCircle className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="details" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
            <TabsTrigger value="depreciation">Depreciation</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
            <TabsTrigger value="qr-code">QR Code</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Asset Information</CardTitle>
                  <CardDescription>Basic asset details and specifications</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Asset ID</Label>
                      <p className="text-sm text-gray-900">{asset.id}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Serial Number</Label>
                      <p className="text-sm text-gray-900">{asset.serialNumber}</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Model</Label>
                      <p className="text-sm text-gray-900">{asset.model}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Manufacturer</Label>
                      <p className="text-sm text-gray-900">{asset.manufacturer}</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Category</Label>
                      <p className="text-sm text-gray-900">{asset.category}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Supplier</Label>
                      <p className="text-sm text-gray-900">{asset.supplier}</p>
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Description</Label>
                    <p className="text-sm text-gray-900">{asset.description}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Notes</Label>
                    <p className="text-sm text-gray-900">{asset.notes}</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Assignment & Location</CardTitle>
                  <CardDescription>Current assignment and location details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <User className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{asset.assignedTo}</p>
                      <p className="text-sm text-gray-600">{asset.assignedEmail}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-4 bg-green-50 rounded-lg">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <MapPin className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{asset.location}</p>
                      <p className="text-sm text-gray-600">Current location</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Purchase Date</Label>
                      <p className="text-sm text-gray-900">{asset.purchaseDate}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Purchase Price</Label>
                      <p className="text-sm text-gray-900">${asset.purchasePrice.toLocaleString()}</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Warranty Expiry</Label>
                      <p className="text-sm text-gray-900">{asset.warrantyExpiry}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Condition</Label>
                      <Badge className={getConditionColor(asset.condition)}>{asset.condition}</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="maintenance" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center">
                      <Wrench className="h-5 w-5 mr-2 text-indigo-600" />
                      Maintenance History
                    </CardTitle>
                    <CardDescription>Complete maintenance record for this asset</CardDescription>
                  </div>
                  <Button size="sm" className="bg-gradient-to-r from-indigo-600 to-purple-600">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Maintenance
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Technician</TableHead>
                        <TableHead>Cost</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Next Due</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {maintenanceHistory.map((maintenance) => (
                        <TableRow key={maintenance.id}>
                          <TableCell className="font-medium">{maintenance.date}</TableCell>
                          <TableCell>
                            <Badge variant="outline">{maintenance.type}</Badge>
                          </TableCell>
                          <TableCell>{maintenance.description}</TableCell>
                          <TableCell>{maintenance.technician}</TableCell>
                          <TableCell>
                            {maintenance.cost > 0 ? `$${maintenance.cost.toLocaleString()}` : "Free"}
                          </TableCell>
                          <TableCell>
                            <Badge className="bg-green-100 text-green-800">{maintenance.status}</Badge>
                          </TableCell>
                          <TableCell>{maintenance.nextDue}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="depreciation" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingDown className="h-5 w-5 mr-2 text-orange-600" />
                  Depreciation Schedule
                </CardTitle>
                <CardDescription>Asset depreciation using straight-line method over 8 years</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">${asset.purchasePrice.toLocaleString()}</div>
                    <div className="text-sm text-blue-700">Original Value</div>
                  </div>
                  <div className="p-4 bg-orange-50 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">
                      ${(asset.purchasePrice - asset.currentValue).toLocaleString()}
                    </div>
                    <div className="text-sm text-orange-700">Total Depreciation</div>
                  </div>
                  <div className="p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">${asset.currentValue.toLocaleString()}</div>
                    <div className="text-sm text-green-700">Current Book Value</div>
                  </div>
                </div>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Year</TableHead>
                        <TableHead>Beginning Value</TableHead>
                        <TableHead>Depreciation</TableHead>
                        <TableHead>Ending Value</TableHead>
                        <TableHead>Accumulated Depreciation</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {depreciationSchedule.map((schedule, index) => (
                        <TableRow key={schedule.year} className={index === 1 ? "bg-blue-50" : ""}>
                          <TableCell className="font-medium">{schedule.year}</TableCell>
                          <TableCell>${schedule.startValue.toLocaleString()}</TableCell>
                          <TableCell className="text-red-600">-${schedule.depreciation.toLocaleString()}</TableCell>
                          <TableCell>${schedule.endValue.toLocaleString()}</TableCell>
                          <TableCell>${(asset.purchasePrice - schedule.endValue).toLocaleString()}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center">
                      <FileText className="h-5 w-5 mr-2 text-green-600" />
                      Asset Documents
                    </CardTitle>
                    <CardDescription>Invoices, warranties, manuals, and service records</CardDescription>
                  </div>
                  <Button size="sm" className="bg-gradient-to-r from-indigo-600 to-purple-600">
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Document
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {documents.map((doc) => (
                    <div
                      key={doc.id}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-red-100 rounded-lg">
                          <FileText className="h-4 w-4 text-red-600" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{doc.name}</div>
                          <div className="text-sm text-gray-500">
                            {doc.type} • {doc.size} • {doc.date}
                          </div>
                        </div>
                      </div>
                      <Button variant="ghost" size="sm">
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2 text-purple-600" />
                  Asset History
                </CardTitle>
                <CardDescription>Complete timeline of asset activities and changes</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3 p-4 border-l-4 border-l-green-500 bg-green-50">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">Asset Created</div>
                      <div className="text-sm text-gray-600">Asset registered in system with QR code generation</div>
                      <div className="text-xs text-gray-500 mt-1">January 15, 2023 • System</div>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-4 border-l-4 border-l-blue-500 bg-blue-50">
                    <User className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">Asset Assigned</div>
                      <div className="text-sm text-gray-600">Assigned to John Doe for development work</div>
                      <div className="text-xs text-gray-500 mt-1">January 16, 2023 • IT Manager</div>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-4 border-l-4 border-l-orange-500 bg-orange-50">
                    <Wrench className="h-5 w-5 text-orange-600 mt-0.5" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">Maintenance Completed</div>
                      <div className="text-sm text-gray-600">Keyboard replacement due to sticky keys</div>
                      <div className="text-xs text-gray-500 mt-1">September 20, 2023 • Apple Service Center</div>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-4 border-l-4 border-l-purple-500 bg-purple-50">
                    <Edit className="h-5 w-5 text-purple-600 mt-0.5" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">Asset Updated</div>
                      <div className="text-sm text-gray-600">Location changed to Office Floor 2</div>
                      <div className="text-xs text-gray-500 mt-1">December 1, 2023 • IT Manager</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="qr-code" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <QrCode className="h-5 w-5 mr-2 text-indigo-600" />
                  QR Code
                </CardTitle>
                <CardDescription>Scan this QR code to quickly access asset information</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="flex flex-col items-center space-y-4">
                    <div className="p-8 bg-white border-2 border-gray-200 rounded-lg">
                      <div className="w-48 h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                        <QrCode className="h-24 w-24 text-gray-400" />
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-gray-900">QR Code: {asset.qrCode}</div>
                      <div className="text-sm text-gray-600">Asset ID: {asset.id}</div>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Download PNG
                      </Button>
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Download PDF
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">QR Code Information</h3>
                    <div className="space-y-3">
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <div className="text-sm font-medium text-gray-900">Asset Name</div>
                        <div className="text-sm text-gray-600">{asset.name}</div>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <div className="text-sm font-medium text-gray-900">Asset ID</div>
                        <div className="text-sm text-gray-600">{asset.id}</div>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <div className="text-sm font-medium text-gray-900">Location</div>
                        <div className="text-sm text-gray-600">{asset.location}</div>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <div className="text-sm font-medium text-gray-900">Assigned To</div>
                        <div className="text-sm text-gray-600">{asset.assignedTo}</div>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <div className="text-sm font-medium text-gray-900">Status</div>
                        <Badge className={getStatusColor(asset.status)}>{asset.status}</Badge>
                      </div>
                    </div>
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <div className="flex items-start space-x-2">
                        <AlertTriangle className="h-5 w-5 text-blue-600 mt-0.5" />
                        <div>
                          <div className="text-sm font-medium text-blue-900">QR Code Usage</div>
                          <div className="text-sm text-blue-700 mt-1">
                            Use mobile app to scan QR codes for quick asset lookup, maintenance logging, and location
                            updates.
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
