const CACHE_NAME = "saas-platform-v1"
const urlsToCache = [
  "/",
  "/dashboard",
  "/dashboard/users",
  "/dashboard/tickets",
  "/dashboard/payroll",
  "/dashboard/customers",
  "/dashboard/projects",
  "/dashboard/assets",
  "/dashboard/consultations",
  "/dashboard/analytics",
  "/offline",
  "/static/js/bundle.js",
  "/static/css/main.css",
  "/icon-192x192.png",
  "/icon-512x512.png",
]

// Install event
self.addEventListener("install", (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      console.log("Opened cache")
      return cache.addAll(urlsToCache)
    }),
  )
})

// Fetch event
self.addEventListener("fetch", (event) => {
  event.respondWith(
    caches.match(event.request).then((response) => {
      // Return cached version or fetch from network
      if (response) {
        return response
      }
      return fetch(event.request).catch(() => {
        // If both cache and network fail, show offline page
        if (event.request.destination === "document") {
          return caches.match("/offline")
        }
      })
    }),
  )
})

// Activate event
self.addEventListener("activate", (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log("Deleting old cache:", cacheName)
            return caches.delete(cacheName)
          }
        }),
      )
    }),
  )
})

// Push notification event
self.addEventListener("push", (event) => {
  const options = {
    body: event.data ? event.data.text() : "New notification from SaaS Platform",
    icon: "/icon-192x192.png",
    badge: "/badge-72x72.png",
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1,
    },
    actions: [
      {
        action: "explore",
        title: "View Details",
        icon: "/action-explore.png",
      },
      {
        action: "close",
        title: "Close",
        icon: "/action-close.png",
      },
    ],
  }

  event.waitUntil(self.registration.showNotification("SaaS Platform", options))
})

// Notification click event
self.addEventListener("notificationclick", (event) => {
  event.notification.close()

  if (event.action === "explore") {
    event.waitUntil(clients.openWindow("/dashboard/notifications"))
  } else if (event.action === "close") {
    // Just close the notification
    return
  } else {
    event.waitUntil(clients.openWindow("/dashboard"))
  }
})

// Background sync
self.addEventListener("sync", (event) => {
  if (event.tag === "background-sync") {
    event.waitUntil(doBackgroundSync())
  }
})

function doBackgroundSync() {
  return fetch("/api/sync")
    .then((response) => {
      console.log("Background sync completed")
      return response
    })
    .catch((error) => {
      console.error("Background sync failed:", error)
    })
}
