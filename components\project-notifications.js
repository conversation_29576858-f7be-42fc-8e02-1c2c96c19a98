"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { FolderKanban, Bell, X, Settings, Volume2, VolumeX, CheckCircle, Clock, Users, Target } from "lucide-react"

export default function ProjectNotifications() {
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      type: "project",
      title: "Task Completed",
      message: "User authentication module has been completed by <PERSON> ahead of schedule.",
      timestamp: new Date(Date.now() - 15 * 60 * 1000),
      read: false,
      priority: "medium",
      category: "task",
      project: "E-commerce Platform",
    },
    {
      id: 2,
      type: "project",
      title: "Deadline Approaching",
      message: "Shopping cart functionality is due in 2 days. Current progress: 65%",
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      read: false,
      priority: "high",
      category: "deadline",
      project: "E-commerce Platform",
    },
    {
      id: 3,
      type: "project",
      title: "Team Member Added",
      message: "<PERSON> has been assigned to the Mobile App Development project as QA Engineer.",
      timestamp: new Date(Date.now() - 60 * 60 * 1000),
      read: true,
      priority: "low",
      category: "team",
      project: "Mobile App Development",
    },
    {
      id: 4,
      type: "project",
      title: "Milestone Reached",
      message: "Design phase completed for Data Analytics Dashboard project. Moving to development phase.",
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      read: false,
      priority: "medium",
      category: "milestone",
      project: "Data Analytics Dashboard",
    },
    {
      id: 5,
      type: "project",
      title: "Budget Alert",
      message: "Security Audit project has used 85% of allocated budget with 2 weeks remaining.",
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
      read: false,
      priority: "high",
      category: "budget",
      project: "Security Audit",
    },
  ])

  const [soundEnabled, setSoundEnabled] = useState(true)
  const [showToast, setShowToast] = useState(false)

  useEffect(() => {
    // Simulate real-time project notifications
    const interval = setInterval(() => {
      const projectNotifications = [
        {
          title: "New Task Created",
          message: "Payment gateway integration task has been created and assigned to Mike Johnson.",
          category: "task",
          project: "E-commerce Platform",
        },
        {
          title: "Time Logged",
          message: "Lisa Chen logged 4 hours on mobile UI design components.",
          category: "time",
          project: "Mobile App Development",
        },
        {
          title: "Comment Added",
          message: "Sarah Wilson added feedback on the dashboard wireframes.",
          category: "comment",
          project: "Data Analytics Dashboard",
        },
        {
          title: "Status Updated",
          message: "Database optimization task moved to review stage.",
          category: "status",
          project: "Security Audit",
        },
      ]

      const randomNotification = projectNotifications[Math.floor(Math.random() * projectNotifications.length)]

      const newNotification = {
        id: Date.now(),
        type: "project",
        title: randomNotification.title,
        message: randomNotification.message,
        timestamp: new Date(),
        read: false,
        priority: "medium",
        category: randomNotification.category,
        project: randomNotification.project,
      }

      setNotifications((prev) => [newNotification, ...prev])
      setShowToast(true)

      if (soundEnabled) {
        console.log("📋 Project notification sound played")
      }

      setTimeout(() => setShowToast(false), 5000)
    }, 35000) // New notification every 35 seconds

    return () => clearInterval(interval)
  }, [soundEnabled])

  const markAsRead = (id) => {
    setNotifications((prev) => prev.map((notif) => (notif.id === id ? { ...notif, read: true } : notif)))
  }

  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((notif) => ({ ...notif, read: true })))
  }

  const removeNotification = (id) => {
    setNotifications((prev) => prev.filter((notif) => notif.id !== id))
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "critical":
        return "bg-red-100 text-red-800 border-red-200"
      case "high":
        return "bg-orange-100 text-orange-800 border-orange-200"
      case "medium":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "low":
        return "bg-green-100 text-green-800 border-green-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getCategoryIcon = (category) => {
    switch (category) {
      case "task":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "deadline":
        return <Clock className="h-4 w-4 text-orange-600" />
      case "team":
        return <Users className="h-4 w-4 text-blue-600" />
      case "milestone":
        return <Target className="h-4 w-4 text-purple-600" />
      case "budget":
        return <FolderKanban className="h-4 w-4 text-red-600" />
      case "time":
        return <Clock className="h-4 w-4 text-indigo-600" />
      case "comment":
        return <Bell className="h-4 w-4 text-yellow-600" />
      case "status":
        return <CheckCircle className="h-4 w-4 text-teal-600" />
      default:
        return <Bell className="h-4 w-4 text-gray-600" />
    }
  }

  const formatTimestamp = (timestamp) => {
    const now = new Date()
    const diff = now - timestamp
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)

    if (minutes < 1) return "Just now"
    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    return `${days}d ago`
  }

  const unreadCount = notifications.filter((n) => !n.read).length

  return (
    <div className="space-y-4">
      {/* Toast Notification */}
      {showToast && (
        <div className="fixed top-4 right-4 z-50 animate-in slide-in-from-top-2">
          <Alert className="w-80 border-purple-200 bg-purple-50">
            <FolderKanban className="h-4 w-4 text-purple-600" />
            <AlertDescription className="text-purple-800">New project notification received!</AlertDescription>
          </Alert>
        </div>
      )}

      {/* Notification Panel */}
      <Card className="w-full max-w-md">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <FolderKanban className="h-5 w-5 text-purple-600" />
              <CardTitle className="text-lg">Project Notifications</CardTitle>
              {unreadCount > 0 && <Badge className="bg-red-500 hover:bg-red-500 text-white">{unreadCount}</Badge>}
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" onClick={() => setSoundEnabled(!soundEnabled)} className="h-8 w-8 p-0">
                {soundEnabled ? (
                  <Volume2 className="h-4 w-4 text-green-600" />
                ) : (
                  <VolumeX className="h-4 w-4 text-gray-400" />
                )}
              </Button>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <CardDescription>Stay updated with project progress, deadlines, and team activities</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">{unreadCount} unread notifications</span>
            {unreadCount > 0 && (
              <Button variant="ghost" size="sm" onClick={markAllAsRead}>
                Mark all as read
              </Button>
            )}
          </div>

          <div className="space-y-2 max-h-96 overflow-y-auto">
            {notifications.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <FolderKanban className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                <p>No project notifications yet</p>
              </div>
            ) : (
              notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-3 rounded-lg border transition-all hover:shadow-sm ${
                    notification.read ? "bg-gray-50 border-gray-200" : "bg-white border-purple-200 shadow-sm"
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className="mt-0.5">{getCategoryIcon(notification.category)}</div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4
                            className={`text-sm font-medium truncate ${
                              notification.read ? "text-gray-700" : "text-gray-900"
                            }`}
                          >
                            {notification.title}
                          </h4>
                          <Badge variant="outline" className={`text-xs ${getPriorityColor(notification.priority)}`}>
                            {notification.priority}
                          </Badge>
                        </div>
                        <p className={`text-sm mb-2 ${notification.read ? "text-gray-500" : "text-gray-700"}`}>
                          {notification.message}
                        </p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline" className="text-xs">
                              {notification.project}
                            </Badge>
                            <span className="text-xs text-gray-500">{formatTimestamp(notification.timestamp)}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            {!notification.read && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => markAsRead(notification.id)}
                                className="h-6 px-2 text-xs text-purple-600 hover:text-purple-700"
                              >
                                Mark read
                              </Button>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeNotification(notification.id)}
                              className="h-6 w-6 p-0 text-gray-400 hover:text-red-600"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {notifications.length > 0 && (
            <div className="pt-3 border-t">
              <Button variant="outline" className="w-full bg-transparent" size="sm">
                <FolderKanban className="h-4 w-4 mr-2" />
                View All Project Notifications
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
