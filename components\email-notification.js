"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Mail, Bell, CheckCircle, AlertTriangle, Info, X, Settings, Volume2, VolumeX } from "lucide-react"

export default function EmailNotification() {
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      type: "email",
      title: "New User Registration",
      message: "<PERSON> has successfully registered and requires account approval.",
      timestamp: new Date(Date.now() - 2 * 60 * 1000),
      read: false,
      priority: "medium",
    },
    {
      id: 2,
      type: "email",
      title: "Payroll Processing Complete",
      message: "Monthly payroll has been processed for 45 employees. Total amount: $125,430.",
      timestamp: new Date(Date.now() - 15 * 60 * 1000),
      read: false,
      priority: "high",
    },
    {
      id: 3,
      type: "email",
      title: "High Priority Ticket",
      message: "Critical database connection issue reported by multiple users.",
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      read: true,
      priority: "critical",
    },
    {
      id: 4,
      type: "email",
      title: "Asset Maintenance Due",
      message: "5 assets require scheduled maintenance within the next 7 days.",
      timestamp: new Date(Date.now() - 60 * 60 * 1000),
      read: false,
      priority: "low",
    },
  ])

  const [soundEnabled, setSoundEnabled] = useState(true)
  const [showToast, setShowToast] = useState(false)

  useEffect(() => {
    // Simulate real-time notifications
    const interval = setInterval(() => {
      const newNotification = {
        id: Date.now(),
        type: "email",
        title: "System Alert",
        message: "New activity detected in your system.",
        timestamp: new Date(),
        read: false,
        priority: "medium",
      }

      setNotifications((prev) => [newNotification, ...prev])
      setShowToast(true)

      if (soundEnabled) {
        // Play notification sound (in a real app, you'd use an actual audio file)
        console.log("🔔 Notification sound played")
      }

      setTimeout(() => setShowToast(false), 5000)
    }, 30000) // New notification every 30 seconds

    return () => clearInterval(interval)
  }, [soundEnabled])

  const markAsRead = (id) => {
    setNotifications((prev) => prev.map((notif) => (notif.id === id ? { ...notif, read: true } : notif)))
  }

  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((notif) => ({ ...notif, read: true })))
  }

  const removeNotification = (id) => {
    setNotifications((prev) => prev.filter((notif) => notif.id !== id))
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "critical":
        return "bg-red-100 text-red-800 border-red-200"
      case "high":
        return "bg-orange-100 text-orange-800 border-orange-200"
      case "medium":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "low":
        return "bg-green-100 text-green-800 border-green-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getPriorityIcon = (priority) => {
    switch (priority) {
      case "critical":
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      case "high":
        return <AlertTriangle className="h-4 w-4 text-orange-600" />
      case "medium":
        return <Info className="h-4 w-4 text-blue-600" />
      case "low":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      default:
        return <Bell className="h-4 w-4 text-gray-600" />
    }
  }

  const formatTimestamp = (timestamp) => {
    const now = new Date()
    const diff = now - timestamp
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)

    if (minutes < 1) return "Just now"
    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    return `${days}d ago`
  }

  const unreadCount = notifications.filter((n) => !n.read).length

  return (
    <div className="space-y-4">
      {/* Toast Notification */}
      {showToast && (
        <div className="fixed top-4 right-4 z-50 animate-in slide-in-from-top-2">
          <Alert className="w-80 border-blue-200 bg-blue-50">
            <Mail className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">New email notification received!</AlertDescription>
          </Alert>
        </div>
      )}

      {/* Notification Panel */}
      <Card className="w-full max-w-md">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Mail className="h-5 w-5 text-blue-600" />
              <CardTitle className="text-lg">Email Notifications</CardTitle>
              {unreadCount > 0 && <Badge className="bg-red-500 hover:bg-red-500 text-white">{unreadCount}</Badge>}
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" onClick={() => setSoundEnabled(!soundEnabled)} className="h-8 w-8 p-0">
                {soundEnabled ? (
                  <Volume2 className="h-4 w-4 text-green-600" />
                ) : (
                  <VolumeX className="h-4 w-4 text-gray-400" />
                )}
              </Button>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <CardDescription>Stay updated with real-time email alerts and system notifications</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">{unreadCount} unread notifications</span>
            {unreadCount > 0 && (
              <Button variant="ghost" size="sm" onClick={markAllAsRead}>
                Mark all as read
              </Button>
            )}
          </div>

          <div className="space-y-2 max-h-96 overflow-y-auto">
            {notifications.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Mail className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                <p>No notifications yet</p>
              </div>
            ) : (
              notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-3 rounded-lg border transition-all hover:shadow-sm ${
                    notification.read ? "bg-gray-50 border-gray-200" : "bg-white border-blue-200 shadow-sm"
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className="mt-0.5">{getPriorityIcon(notification.priority)}</div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4
                            className={`text-sm font-medium truncate ${
                              notification.read ? "text-gray-700" : "text-gray-900"
                            }`}
                          >
                            {notification.title}
                          </h4>
                          <Badge variant="outline" className={`text-xs ${getPriorityColor(notification.priority)}`}>
                            {notification.priority}
                          </Badge>
                        </div>
                        <p className={`text-sm mb-2 ${notification.read ? "text-gray-500" : "text-gray-700"}`}>
                          {notification.message}
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">{formatTimestamp(notification.timestamp)}</span>
                          <div className="flex items-center space-x-1">
                            {!notification.read && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => markAsRead(notification.id)}
                                className="h-6 px-2 text-xs text-blue-600 hover:text-blue-700"
                              >
                                Mark read
                              </Button>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeNotification(notification.id)}
                              className="h-6 w-6 p-0 text-gray-400 hover:text-red-600"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {notifications.length > 0 && (
            <div className="pt-3 border-t">
              <Button variant="outline" className="w-full bg-transparent" size="sm">
                <Mail className="h-4 w-4 mr-2" />
                View All Email Notifications
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
