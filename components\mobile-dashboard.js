"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Users,
  DollarSign,
  Ticket,
  FolderKanban,
  TrendingUp,
  TrendingDown,
  Clock,
  Bell,
  ChevronRight,
  Activity,
  Zap,
  Target,
} from "lucide-react"
import Link from "next/link"

export default function MobileDashboard() {
  const [stats] = useState([
    {
      title: "Users",
      value: "2,847",
      change: "+12%",
      trend: "up",
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Revenue",
      value: "$45.2K",
      change: "****%",
      trend: "up",
      icon: DollarSign,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Tickets",
      value: "23",
      change: "-15%",
      trend: "down",
      icon: Ticket,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
    {
      title: "Projects",
      value: "12",
      change: "+3",
      trend: "up",
      icon: FolderKanban,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
  ])

  const [quickActions] = useState([
    { name: "Add User", icon: Users, href: "/dashboard/users/new", color: "bg-blue-500" },
    { name: "New Ticket", icon: Ticket, href: "/dashboard/tickets/new", color: "bg-orange-500" },
    { name: "Start Project", icon: FolderKanban, href: "/dashboard/projects/new", color: "bg-purple-500" },
    { name: "View Analytics", icon: Activity, href: "/dashboard/analytics", color: "bg-green-500" },
  ])

  const [recentActivities] = useState([
    {
      id: 1,
      action: "New user registered",
      user: "Sarah Wilson",
      time: "2 min ago",
      type: "user",
      priority: "normal",
    },
    {
      id: 2,
      action: "High priority ticket created",
      user: "Mike Johnson",
      time: "5 min ago",
      type: "ticket",
      priority: "high",
    },
    {
      id: 3,
      action: "Project milestone completed",
      user: "Lisa Chen",
      time: "15 min ago",
      type: "project",
      priority: "normal",
    },
    {
      id: 4,
      action: "Payment received",
      user: "David Brown",
      time: "1 hour ago",
      type: "payment",
      priority: "normal",
    },
  ])

  const [notifications] = useState([
    {
      id: 1,
      title: "System maintenance scheduled",
      message: "Scheduled for tonight at 2:00 AM",
      time: "10 min ago",
      type: "info",
      read: false,
    },
    {
      id: 2,
      title: "New feature available",
      message: "Mobile push notifications are now live",
      time: "1 hour ago",
      type: "success",
      read: false,
    },
    {
      id: 3,
      title: "Security alert",
      message: "Unusual login activity detected",
      time: "2 hours ago",
      type: "warning",
      read: true,
    },
  ])

  const [currentTime, setCurrentTime] = useState(new Date())

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const getActivityIcon = (type) => {
    switch (type) {
      case "user":
        return <Users className="h-4 w-4 text-blue-600" />
      case "ticket":
        return <Ticket className="h-4 w-4 text-orange-600" />
      case "project":
        return <FolderKanban className="h-4 w-4 text-purple-600" />
      case "payment":
        return <DollarSign className="h-4 w-4 text-green-600" />
      default:
        return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "high":
        return "border-l-red-500 bg-red-50"
      case "medium":
        return "border-l-yellow-500 bg-yellow-50"
      default:
        return "border-l-blue-500 bg-blue-50"
    }
  }

  const getNotificationColor = (type) => {
    switch (type) {
      case "success":
        return "border-l-green-500 bg-green-50"
      case "warning":
        return "border-l-orange-500 bg-orange-50"
      case "error":
        return "border-l-red-500 bg-red-50"
      default:
        return "border-l-blue-500 bg-blue-50"
    }
  }

  return (
    <div className="space-y-6 pb-20">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold">Welcome back!</h1>
            <p className="text-blue-100">Here's your business overview</p>
          </div>
          <div className="text-right">
            <p className="text-sm text-blue-100">{currentTime.toLocaleDateString()}</p>
            <p className="text-lg font-semibold">
              {currentTime.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-sm text-blue-100">All systems operational</span>
          </div>
          <Badge className="bg-white/20 text-white border-white/30">
            <Zap className="h-3 w-3 mr-1" />
            Live
          </Badge>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 gap-4">
        {stats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-2">
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </div>
                <div className={`flex items-center text-xs ${stat.trend === "up" ? "text-green-600" : "text-red-600"}`}>
                  {stat.trend === "up" ? (
                    <TrendingUp className="h-3 w-3 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 mr-1" />
                  )}
                  {stat.change}
                </div>
              </div>
              <div>
                <p className="text-xs text-gray-600 mb-1">{stat.title}</p>
                <p className="text-xl font-bold text-gray-900">{stat.value}</p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center">
            <Target className="h-5 w-5 mr-2 text-blue-600" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-3">
            {quickActions.map((action, index) => (
              <Link key={index} href={action.href}>
                <Button
                  variant="outline"
                  className="w-full h-auto p-4 flex flex-col items-center space-y-2 hover:shadow-md transition-all bg-transparent"
                >
                  <div className={`p-2 rounded-lg ${action.color} text-white`}>
                    <action.icon className="h-5 w-5" />
                  </div>
                  <span className="text-sm font-medium">{action.name}</span>
                </Button>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center">
              <Clock className="h-5 w-5 mr-2 text-green-600" />
              Recent Activity
            </CardTitle>
            <Link href="/dashboard/activity">
              <Button variant="ghost" size="sm" className="text-blue-600">
                View All
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentActivities.slice(0, 3).map((activity) => (
              <div
                key={activity.id}
                className={`flex items-center space-x-3 p-3 rounded-lg border-l-4 ${getPriorityColor(activity.priority)}`}
              >
                <div className="flex-shrink-0">{getActivityIcon(activity.type)}</div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">{activity.action}</p>
                  <p className="text-xs text-gray-500">
                    by {activity.user} • {activity.time}
                  </p>
                </div>
                {activity.priority === "high" && <Badge className="bg-red-100 text-red-800 text-xs">High</Badge>}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Notifications */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center">
              <Bell className="h-5 w-5 mr-2 text-orange-600" />
              Notifications
              <Badge className="ml-2 bg-red-100 text-red-800">{notifications.filter((n) => !n.read).length}</Badge>
            </CardTitle>
            <Link href="/dashboard/notifications">
              <Button variant="ghost" size="sm" className="text-blue-600">
                View All
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {notifications.slice(0, 2).map((notification) => (
              <div
                key={notification.id}
                className={`p-3 rounded-lg border-l-4 ${getNotificationColor(notification.type)} ${
                  !notification.read ? "ring-2 ring-blue-100" : ""
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900 mb-1">
                      {notification.title}
                      {!notification.read && (
                        <span className="ml-2 w-2 h-2 bg-blue-500 rounded-full inline-block"></span>
                      )}
                    </h4>
                    <p className="text-xs text-gray-600 mb-1">{notification.message}</p>
                    <p className="text-xs text-gray-500">{notification.time}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Performance Overview */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center">
            <Activity className="h-5 w-5 mr-2 text-purple-600" />
            Performance Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">System Health</span>
                <span className="text-sm text-green-600 font-medium">98%</span>
              </div>
              <Progress value={98} className="h-2" />
            </div>
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">User Satisfaction</span>
                <span className="text-sm text-blue-600 font-medium">94%</span>
              </div>
              <Progress value={94} className="h-2" />
            </div>
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Task Completion</span>
                <span className="text-sm text-purple-600 font-medium">87%</span>
              </div>
              <Progress value={87} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
