"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { WifiOff, RefreshCw, Home, Clock, Database } from "lucide-react"
import { useState, useEffect } from "react"

export default function OfflinePage() {
  const [isOnline, setIsOnline] = useState(false)
  const [lastSync, setLastSync] = useState(null)
  const [cachedData, setCachedData] = useState([])

  useEffect(() => {
    // Check online status
    setIsOnline(navigator.onLine)

    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener("online", handleOnline)
    window.addEventListener("offline", handleOffline)

    // Load cached data from localStorage
    const cached = localStorage.getItem("saas-platform-cache")
    if (cached) {
      setCachedData(JSON.parse(cached))
    }

    // Get last sync time
    const lastSyncTime = localStorage.getItem("saas-platform-last-sync")
    if (lastSyncTime) {
      setLastSync(new Date(lastSyncTime))
    }

    return () => {
      window.removeEventListener("online", handleOnline)
      window.removeEventListener("offline", handleOffline)
    }
  }, [])

  const handleRetry = () => {
    if (isOnline) {
      window.location.href = "/dashboard"
    } else {
      window.location.reload()
    }
  }

  const handleGoHome = () => {
    window.location.href = "/dashboard"
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Offline Status */}
        <Card className="text-center">
          <CardHeader>
            <div className="mx-auto w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-4">
              <WifiOff className="h-8 w-8 text-orange-600" />
            </div>
            <CardTitle className="text-xl">You're Offline</CardTitle>
            <CardDescription>
              {isOnline
                ? "Connection restored! You can now access all features."
                : "No internet connection detected. Some features may be limited."}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {isOnline ? (
                <Alert className="border-green-200 bg-green-50">
                  <Database className="h-4 w-4 text-green-600" />
                  <AlertDescription className="text-green-800">
                    Connection restored! Syncing your data...
                  </AlertDescription>
                </Alert>
              ) : (
                <Alert className="border-orange-200 bg-orange-50">
                  <WifiOff className="h-4 w-4 text-orange-600" />
                  <AlertDescription className="text-orange-800">
                    You can still view cached data and use basic features while offline.
                  </AlertDescription>
                </Alert>
              )}

              <div className="flex flex-col space-y-2">
                <Button onClick={handleRetry} className={`w-full ${isOnline ? "bg-green-600 hover:bg-green-700" : ""}`}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {isOnline ? "Go to Dashboard" : "Try Again"}
                </Button>

                <Button variant="outline" onClick={handleGoHome} className="w-full bg-transparent">
                  <Home className="h-4 w-4 mr-2" />
                  Go to Home
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Offline Features */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Available Offline</CardTitle>
            <CardDescription>These features work even without an internet connection</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50">
                <Database className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium text-sm">Cached Data</p>
                  <p className="text-xs text-gray-600">View previously loaded information</p>
                </div>
              </div>

              <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50">
                <Clock className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium text-sm">Draft Mode</p>
                  <p className="text-xs text-gray-600">Create drafts that sync when online</p>
                </div>
              </div>

              <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50">
                <RefreshCw className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="font-medium text-sm">Auto Sync</p>
                  <p className="text-xs text-gray-600">Automatic sync when connection returns</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Last Sync Info */}
        {lastSync && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <Clock className="h-5 w-5 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">Last synced: {lastSync.toLocaleString()}</p>
                <p className="text-xs text-gray-500 mt-1">{cachedData.length} items cached locally</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Network Status */}
        <div className="text-center">
          <div
            className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full text-xs ${
              isOnline ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
            }`}
          >
            <div className={`w-2 h-2 rounded-full ${isOnline ? "bg-green-500 animate-pulse" : "bg-red-500"}`}></div>
            <span>{isOnline ? "Online" : "Offline"}</span>
          </div>
        </div>
      </div>
    </div>
  )
}
