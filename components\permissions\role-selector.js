"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { ROLES, PERMISSIONS, ROLE_PERMISSIONS, getRoleDisplayName } from "@/lib/permissions"
import { Shield, Users, Plus, Edit } from "lucide-react"

export default function RoleSelector({ users = [], onRoleChange, onPermissionChange }) {
  const [selectedUser, setSelectedUser] = useState(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [customPermissions, setCustomPermissions] = useState([])

  const handleRoleChange = (userId, newRole) => {
    onRoleChange?.(userId, newRole)
  }

  const handleCustomPermissions = (userId, permissions) => {
    setCustomPermissions(permissions)
    onPermissionChange?.(userId, permissions)
  }

  const getRoleColor = (role) => {
    const colors = {
      [ROLES.ADMIN]: "bg-red-100 text-red-800",
      [ROLES.PROJECT_MANAGER]: "bg-blue-100 text-blue-800",
      [ROLES.PORTFOLIO_MANAGER]: "bg-purple-100 text-purple-800",
      [ROLES.BUSINESS_ANALYST]: "bg-green-100 text-green-800",
      [ROLES.TEAM_LEAD]: "bg-orange-100 text-orange-800",
      [ROLES.DEVELOPER]: "bg-gray-100 text-gray-800",
      [ROLES.STAKEHOLDER]: "bg-yellow-100 text-yellow-800",
      [ROLES.VIEWER]: "bg-slate-100 text-slate-800",
    }
    return colors[role] || "bg-gray-100 text-gray-800"
  }

  const groupedPermissions = {
    "Create Phase": [
      PERMISSIONS.CREATE_PROPOSE_IDEA,
      PERMISSIONS.CREATE_REVIEW_IDEA,
      PERMISSIONS.CREATE_APPROVE_REQUEST,
      PERMISSIONS.CREATE_EDIT_PROPOSAL,
    ],
    "Select Phase": [
      PERMISSIONS.SELECT_REQUEST_REVIEW,
      PERMISSIONS.SELECT_PORTFOLIO_SELECTION,
      PERMISSIONS.SELECT_COMPLETE_SELECTION,
      PERMISSIONS.SELECT_REJECT_PROJECT,
    ],
    "Plan Phase": [
      PERMISSIONS.PLAN_CREATE_BUSINESS_CASE,
      PERMISSIONS.PLAN_REVIEW_BUSINESS_CASE,
      PERMISSIONS.PLAN_APPROVE_BUSINESS_CASE,
      PERMISSIONS.PLAN_DENY_BUSINESS_CASE,
      PERMISSIONS.PLAN_EDIT_BUSINESS_CASE,
    ],
    "Manage Phase": [
      PERMISSIONS.MANAGE_START_PROJECT,
      PERMISSIONS.MANAGE_SUSPEND_PROJECT,
      PERMISSIONS.MANAGE_COMPLETE_PROJECT,
      PERMISSIONS.MANAGE_POST_IMPLEMENTATION,
      PERMISSIONS.MANAGE_ASSIGN_TEAM,
    ],
    General: [
      PERMISSIONS.VIEW_PROJECT,
      PERMISSIONS.EDIT_PROJECT,
      PERMISSIONS.DELETE_PROJECT,
      PERMISSIONS.VIEW_FINANCIALS,
      PERMISSIONS.EDIT_FINANCIALS,
      PERMISSIONS.MANAGE_TEAM,
      PERMISSIONS.VIEW_REPORTS,
    ],
  }

  return (
    <div className="space-y-6">
      {/* Role Management Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2 text-blue-600" />
                Role & Permission Management
              </CardTitle>
              <CardDescription>Manage user roles and permissions for workflow phases</CardDescription>
            </div>
            <Dialog>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add User
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New User</DialogTitle>
                  <DialogDescription>Add a new user and assign their role and permissions</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label>Email</Label>
                    <input type="email" className="w-full p-2 border rounded-md" placeholder="<EMAIL>" />
                  </div>
                  <div>
                    <Label>Role</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(ROLES).map((role) => (
                          <SelectItem key={role} value={role}>
                            {getRoleDisplayName(role)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button>Add User</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
      </Card>

      {/* Users List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {users.map((user) => (
          <Card key={user.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4 mb-4">
                <Avatar>
                  <AvatarImage src={user.avatar || "/placeholder.svg"} alt={user.name} />
                  <AvatarFallback>
                    {user.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <h3 className="font-semibold">{user.name}</h3>
                  <p className="text-sm text-gray-600">{user.email}</p>
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <Label className="text-sm font-medium">Current Role</Label>
                  <Badge className={`${getRoleColor(user.role)} mt-1`}>{getRoleDisplayName(user.role)}</Badge>
                </div>

                <div>
                  <Label className="text-sm font-medium">Change Role</Label>
                  <Select value={user.role} onValueChange={(newRole) => handleRoleChange(user.id, newRole)}>
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(ROLES).map((role) => (
                        <SelectItem key={role} value={role}>
                          {getRoleDisplayName(role)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex space-x-2">
                  <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1 bg-transparent"
                        onClick={() => setSelectedUser(user)}
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        Permissions
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Edit Permissions - {selectedUser?.name}</DialogTitle>
                        <DialogDescription>
                          Customize permissions for this user beyond their role defaults
                        </DialogDescription>
                      </DialogHeader>

                      <div className="space-y-6">
                        <div>
                          <h4 className="font-medium mb-2">Current Role Permissions</h4>
                          <div className="grid grid-cols-2 gap-2">
                            {(ROLE_PERMISSIONS[selectedUser?.role] || []).map((permission) => (
                              <Badge key={permission} variant="outline" className="text-xs">
                                {permission.replace(/_/g, " ").toLowerCase()}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium mb-4">Additional Permissions</h4>
                          {Object.entries(groupedPermissions).map(([group, permissions]) => (
                            <div key={group} className="mb-4">
                              <h5 className="font-medium text-sm mb-2 text-gray-700">{group}</h5>
                              <div className="space-y-2">
                                {permissions.map((permission) => (
                                  <div key={permission} className="flex items-center space-x-2">
                                    <Checkbox
                                      id={permission}
                                      checked={customPermissions.includes(permission)}
                                      onCheckedChange={(checked) => {
                                        if (checked) {
                                          setCustomPermissions([...customPermissions, permission])
                                        } else {
                                          setCustomPermissions(customPermissions.filter((p) => p !== permission))
                                        }
                                      }}
                                    />
                                    <Label htmlFor={permission} className="text-sm">
                                      {permission.replace(/_/g, " ").toLowerCase()}
                                    </Label>
                                  </div>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      <DialogFooter>
                        <Button
                          onClick={() => {
                            handleCustomPermissions(selectedUser?.id, customPermissions)
                            setIsEditDialogOpen(false)
                          }}
                        >
                          Save Permissions
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>

                  <Button variant="outline" size="sm" className="flex-1 bg-transparent">
                    <Users className="h-3 w-3 mr-1" />
                    Projects
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Role Permissions Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Role Permissions Overview</CardTitle>
          <CardDescription>Default permissions for each role across workflow phases</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Role</th>
                  <th className="text-left p-2">Create</th>
                  <th className="text-left p-2">Select</th>
                  <th className="text-left p-2">Plan</th>
                  <th className="text-left p-2">Manage</th>
                  <th className="text-left p-2">General</th>
                </tr>
              </thead>
              <tbody>
                {Object.values(ROLES).map((role) => (
                  <tr key={role} className="border-b">
                    <td className="p-2">
                      <Badge className={getRoleColor(role)}>{getRoleDisplayName(role)}</Badge>
                    </td>
                    {Object.keys(groupedPermissions).map((group) => (
                      <td key={group} className="p-2">
                        <div className="text-xs">
                          {
                            groupedPermissions[group].filter((permission) =>
                              ROLE_PERMISSIONS[role]?.includes(permission),
                            ).length
                          }{" "}
                          / {groupedPermissions[group].length}
                        </div>
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
