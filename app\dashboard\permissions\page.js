"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Shield, Users, Settings, AlertTriangle, CheckCircle } from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import RoleSelector from "@/components/permissions/role-selector"
import { ROLES, getRoleDisplayName } from "@/lib/permissions"

export default function PermissionsPage() {
  const [users, setUsers] = useState([])
  const [roles, setRoles] = useState([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalRoles: 0,
    activeUsers: 0,
    pendingApprovals: 0,
  })

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockUsers = [
      {
        id: "1",
        name: "<PERSON>",
        email: "<EMAIL>",
        role: <PERSON><PERSON><PERSON>.PROJECT_MANAGER,
        avatar: "/placeholder.svg?height=40&width=40",
        isActive: true,
        lastLogin: "2024-01-15T10:30:00Z",
      },
      {
        id: "2",
        name: "Sarah Wilson",
        email: "<EMAIL>",
        role: ROLES.PORTFOLIO_MANAGER,
        avatar: "/placeholder.svg?height=40&width=40",
        isActive: true,
        lastLogin: "2024-01-15T09:15:00Z",
      },
      {
        id: "3",
        name: "Mike Johnson",
        email: "<EMAIL>",
        role: ROLES.BUSINESS_ANALYST,
        avatar: "/placeholder.svg?height=40&width=40",
        isActive: true,
        lastLogin: "2024-01-14T16:45:00Z",
      },
      {
        id: "4",
        name: "Lisa Chen",
        email: "<EMAIL>",
        role: ROLES.TEAM_LEAD,
        avatar: "/placeholder.svg?height=40&width=40",
        isActive: true,
        lastLogin: "2024-01-15T08:20:00Z",
      },
      {
        id: "5",
        name: "Emma Davis",
        email: "<EMAIL>",
        role: ROLES.DEVELOPER,
        avatar: "/placeholder.svg?height=40&width=40",
        isActive: true,
        lastLogin: "2024-01-15T11:10:00Z",
      },
    ]

    const mockRoles = Object.values(ROLES).map((role) => ({
      id: role,
      name: role,
      displayName: getRoleDisplayName(role),
      userCount: mockUsers.filter((user) => user.role === role).length,
      isActive: true,
    }))

    setUsers(mockUsers)
    setRoles(mockRoles)
    setStats({
      totalUsers: mockUsers.length,
      totalRoles: mockRoles.length,
      activeUsers: mockUsers.filter((user) => user.isActive).length,
      pendingApprovals: 3,
    })
    setLoading(false)
  }, [])

  const handleRoleChange = async (userId, newRole) => {
    try {
      // Update user role in state
      setUsers((prevUsers) => prevUsers.map((user) => (user.id === userId ? { ...user, role: newRole } : user)))

      // Here you would make an API call to update the user's role
      console.log(`Updated user ${userId} to role ${newRole}`)
    } catch (error) {
      console.error("Failed to update user role:", error)
    }
  }

  const handlePermissionChange = async (userId, permissions) => {
    try {
      // Here you would make an API call to update custom permissions
      console.log(`Updated permissions for user ${userId}:`, permissions)
    } catch (error) {
      console.error("Failed to update user permissions:", error)
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading permissions...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Permissions Management</h1>
            <p className="text-gray-600 mt-1">Manage user roles and workflow permissions</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button className="bg-gradient-to-r from-purple-600 to-blue-600">
              <Shield className="h-4 w-4 mr-2" />
              Security Audit
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Users</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalUsers}</p>
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Roles</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalRoles}</p>
                </div>
                <Shield className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Users</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.activeUsers}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending Approvals</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.pendingApprovals}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="users" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="users">User Management</TabsTrigger>
            <TabsTrigger value="roles">Role Management</TabsTrigger>
            <TabsTrigger value="audit">Audit Log</TabsTrigger>
          </TabsList>

          <TabsContent value="users" className="space-y-6">
            <RoleSelector users={users} onRoleChange={handleRoleChange} onPermissionChange={handlePermissionChange} />
          </TabsContent>

          <TabsContent value="roles" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Role Management</CardTitle>
                <CardDescription>Manage system roles and their default permissions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {roles.map((role) => (
                    <Card key={role.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-3">
                          <Badge className="bg-blue-100 text-blue-800">{role.displayName}</Badge>
                          <span className="text-sm text-gray-500">{role.userCount} users</span>
                        </div>
                        <div className="space-y-2">
                          <Button variant="outline" size="sm" className="w-full bg-transparent">
                            Edit Permissions
                          </Button>
                          <Button variant="outline" size="sm" className="w-full bg-transparent">
                            View Users
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="audit" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Permission Audit Log</CardTitle>
                <CardDescription>Track all permission changes and role assignments</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      id: 1,
                      action: "Role Changed",
                      user: "John Doe",
                      details: "Changed from Developer to Project Manager",
                      timestamp: "2024-01-15T10:30:00Z",
                      performedBy: "Admin",
                    },
                    {
                      id: 2,
                      action: "Permission Granted",
                      user: "Sarah Wilson",
                      details: "Added plan_approve_business_case permission",
                      timestamp: "2024-01-15T09:15:00Z",
                      performedBy: "Admin",
                    },
                    {
                      id: 3,
                      action: "User Created",
                      user: "Mike Johnson",
                      details: "Created with Business Analyst role",
                      timestamp: "2024-01-14T16:45:00Z",
                      performedBy: "Admin",
                    },
                  ].map((entry) => (
                    <div key={entry.id} className="flex items-start space-x-4 p-4 border rounded-lg">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <Shield className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-1">
                          <span className="font-semibold">{entry.action}</span>
                          <Badge variant="outline">{entry.user}</Badge>
                          <span className="text-sm text-gray-500">{new Date(entry.timestamp).toLocaleString()}</span>
                        </div>
                        <p className="text-sm text-gray-600">{entry.details}</p>
                        <p className="text-xs text-gray-500 mt-1">by {entry.performedBy}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
