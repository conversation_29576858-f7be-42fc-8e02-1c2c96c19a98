"use client"

import { useState } from "react"
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Bell, Calendar, Clock, DollarSign, AlertTriangle, CheckCircle, X, Filter } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export default function ConsultationNotifications() {
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      type: "appointment",
      priority: "high",
      title: "Upcoming Appointment Reminder",
      message: "<PERSON>'s consultation starts in 30 minutes (9:00 AM)",
      time: "5 minutes ago",
      read: false,
      actionable: true,
      actions: ["Join Call", "Reschedule"],
    },
    {
      id: 2,
      type: "payment",
      priority: "medium",
      title: "Payment Received",
      message: "<PERSON> paid $120 for Follow-up Session consultation",
      time: "1 hour ago",
      read: false,
      actionable: false,
    },
    {
      id: 3,
      type: "schedule",
      priority: "low",
      title: "New Appointment Request",
      message: "<PERSON> requested an Initial Consultation for tomorrow at 2:00 PM",
      time: "2 hours ago",
      read: true,
      actionable: true,
      actions: ["Approve", "Suggest Alternative"],
    },
    {
      id: 4,
      type: "reminder",
      priority: "medium",
      title: "Client Follow-up Due",
      message: "Follow up with Emily Davis - last consultation was 1 week ago",
      time: "3 hours ago",
      read: true,
      actionable: true,
      actions: ["Send Email", "Schedule Call"],
    },
    {
      id: 5,
      type: "billing",
      priority: "high",
      title: "Overdue Payment Alert",
      message: "Robert Wilson's invoice (INV-2024-004) is 3 days overdue - $175",
      time: "4 hours ago",
      read: false,
      actionable: true,
      actions: ["Send Reminder", "Call Client"],
    },
  ])

  const [filter, setFilter] = useState("all")
  const [showRead, setShowRead] = useState(true)

  const getNotificationIcon = (type) => {
    switch (type) {
      case "appointment":
        return <Calendar className="h-4 w-4" />
      case "payment":
        return <DollarSign className="h-4 w-4" />
      case "schedule":
        return <Clock className="h-4 w-4" />
      case "reminder":
        return <Bell className="h-4 w-4" />
      case "billing":
        return <AlertTriangle className="h-4 w-4" />
      default:
        return <Bell className="h-4 w-4" />
    }
  }

  const getNotificationColor = (type, priority) => {
    if (priority === "high") {
      return "border-l-red-500 bg-red-50"
    } else if (priority === "medium") {
      return "border-l-yellow-500 bg-yellow-50"
    } else {
      return "border-l-blue-500 bg-blue-50"
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "low":
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const markAsRead = (id) => {
    setNotifications(notifications.map((notif) => (notif.id === id ? { ...notif, read: true } : notif)))
  }

  const dismissNotification = (id) => {
    setNotifications(notifications.filter((notif) => notif.id !== id))
  }

  const filteredNotifications = notifications.filter((notif) => {
    if (!showRead && notif.read) return false
    if (filter === "all") return true
    if (filter === "unread") return !notif.read
    if (filter === "actionable") return notif.actionable
    return notif.priority === filter
  })

  const unreadCount = notifications.filter((n) => !n.read).length

  return (
    <div className="space-y-4">
      {/* Notification Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Bell className="h-5 w-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">Notifications</h3>
          {unreadCount > 0 && <Badge className="bg-red-500 hover:bg-red-500">{unreadCount} new</Badge>}
        </div>

        <div className="flex items-center space-x-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Filter Notifications</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setFilter("all")}>All Notifications</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilter("unread")}>Unread Only</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilter("actionable")}>Actionable Items</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setFilter("high")}>High Priority</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilter("medium")}>Medium Priority</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilter("low")}>Low Priority</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setNotifications(notifications.map((n) => ({ ...n, read: true })))}
          >
            Mark All Read
          </Button>
        </div>
      </div>

      {/* Notifications List */}
      <div className="space-y-3">
        {filteredNotifications.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Bell className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No notifications to display</p>
          </div>
        ) : (
          filteredNotifications.map((notification) => (
            <Alert
              key={notification.id}
              className={`border-l-4 ${getNotificationColor(notification.type, notification.priority)} ${
                !notification.read ? "ring-2 ring-blue-100" : ""
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                  <div className="mt-0.5">{getNotificationIcon(notification.type)}</div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="text-sm font-medium text-gray-900">{notification.title}</h4>
                      <Badge className={getPriorityColor(notification.priority)}>{notification.priority}</Badge>
                      {!notification.read && <div className="w-2 h-2 bg-blue-500 rounded-full"></div>}
                    </div>
                    <AlertDescription className="text-sm text-gray-700 mb-2">{notification.message}</AlertDescription>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">{notification.time}</span>
                      {notification.actionable && notification.actions && (
                        <div className="flex items-center space-x-2">
                          {notification.actions.map((action, index) => (
                            <Button key={index} size="sm" variant="outline" className="h-6 text-xs bg-transparent">
                              {action}
                            </Button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-1 ml-4">
                  {!notification.read && (
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => markAsRead(notification.id)}
                      className="h-6 w-6 p-0"
                    >
                      <CheckCircle className="h-3 w-3" />
                    </Button>
                  )}
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => dismissNotification(notification.id)}
                    className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </Alert>
          ))
        )}
      </div>

      {/* Quick Stats */}
      {filteredNotifications.length > 0 && (
        <div className="flex items-center justify-between text-sm text-gray-500 pt-4 border-t">
          <div className="flex items-center space-x-4">
            <span>{filteredNotifications.length} notifications</span>
            <span>•</span>
            <span>{unreadCount} unread</span>
            <span>•</span>
            <span>{notifications.filter((n) => n.actionable).length} actionable</span>
          </div>
          <div className="flex items-center space-x-2">
            <span>Last updated: just now</span>
            <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
              <Bell className="h-3 w-3" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
