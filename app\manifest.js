export default function manifest() {
  return {
    name: "SaaS Platform - Business Management",
    short_name: "SaaS Platform",
    description: "Complete business management solution with real-time analytics and notifications",
    start_url: "/dashboard",
    display: "standalone",
    background_color: "#ffffff",
    theme_color: "#3b82f6",
    orientation: "portrait-primary",
    icons: [
      {
        src: "/icon-192x192.png",
        sizes: "192x192",
        type: "image/png",
        purpose: "maskable",
      },
      {
        src: "/icon-512x512.png",
        sizes: "512x512",
        type: "image/png",
        purpose: "any",
      },
    ],
    categories: ["business", "productivity", "utilities"],
    screenshots: [
      {
        src: "/screenshot-mobile.png",
        sizes: "390x844",
        type: "image/png",
        form_factor: "narrow",
      },
      {
        src: "/screenshot-desktop.png",
        sizes: "1920x1080",
        type: "image/png",
        form_factor: "wide",
      },
    ],
    shortcuts: [
      {
        name: "Dashboard",
        short_name: "Dashboard",
        description: "View main dashboard",
        url: "/dashboard",
        icons: [{ src: "/shortcut-dashboard.png", sizes: "96x96" }],
      },
      {
        name: "Notifications",
        short_name: "Notifications",
        description: "View notifications",
        url: "/dashboard/notifications",
        icons: [{ src: "/shortcut-notifications.png", sizes: "96x96" }],
      },
      {
        name: "Analytics",
        short_name: "Analytics",
        description: "View analytics",
        url: "/dashboard/analytics",
        icons: [{ src: "/shortcut-analytics.png", sizes: "96x96" }],
      },
    ],
  }
}
