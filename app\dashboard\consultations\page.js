"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Calendar,
  Clock,
  Users,
  DollarSign,
  Video,
  Phone,
  MapPin,
  Plus,
  Search,
  Download,
  Bell,
  CheckCircle,
  User,
  Mail,
  FileText,
  CalendarDays,
  Zap,
  Settings,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import ConsultationNotifications from "@/components/consultation-notifications"

export default function ConsultationsPage() {
  const [selectedTab, setSelectedTab] = useState("dashboard")
  const [searchTerm, setSearchTerm] = useState("")
  const [filterStatus, setFilterStatus] = useState("all")
  const [showNewAppointment, setShowNewAppointment] = useState(false)
  const [showNewClient, setShowNewClient] = useState(false)

  // Mock data
  const stats = [
    {
      title: "Today's Appointments",
      value: "8",
      change: "+2 from yesterday",
      icon: Calendar,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Total Clients",
      value: "247",
      change: "+12 this month",
      icon: Users,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Monthly Revenue",
      value: "$18,450",
      change: "+15.3% from last month",
      icon: DollarSign,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      title: "Completion Rate",
      value: "94.2%",
      change: "+2.1% improvement",
      icon: CheckCircle,
      color: "text-emerald-600",
      bgColor: "bg-emerald-100",
    },
  ]

  const todayAppointments = [
    {
      id: 1,
      time: "09:00 AM",
      client: "Sarah Johnson",
      type: "Initial Consultation",
      duration: "60 min",
      status: "confirmed",
      method: "video",
      fee: "$150",
      notes: "First-time client, interested in business strategy consulting",
    },
    {
      id: 2,
      time: "10:30 AM",
      client: "Michael Chen",
      type: "Follow-up Session",
      duration: "45 min",
      status: "confirmed",
      method: "phone",
      fee: "$120",
      notes: "Review progress on marketing strategy implementation",
    },
    {
      id: 3,
      time: "02:00 PM",
      client: "Emily Davis",
      type: "Strategy Review",
      duration: "90 min",
      status: "pending",
      method: "in-person",
      fee: "$200",
      notes: "Quarterly business review and planning session",
    },
    {
      id: 4,
      time: "03:45 PM",
      client: "Robert Wilson",
      type: "Technical Consultation",
      duration: "60 min",
      status: "confirmed",
      method: "video",
      fee: "$175",
      notes: "IT infrastructure assessment and recommendations",
    },
  ]

  const clients = [
    {
      id: 1,
      name: "Sarah Johnson",
      email: "<EMAIL>",
      phone: "+****************",
      company: "Tech Innovations Inc.",
      totalSessions: 5,
      totalSpent: "$750",
      lastSession: "2024-01-15",
      status: "active",
      notes: "Excellent client, always punctual and prepared",
    },
    {
      id: 2,
      name: "Michael Chen",
      email: "<EMAIL>",
      phone: "+****************",
      company: "Growth Dynamics LLC",
      totalSessions: 12,
      totalSpent: "$1,440",
      lastSession: "2024-01-10",
      status: "active",
      notes: "Long-term client, focuses on marketing strategy",
    },
    {
      id: 3,
      name: "Emily Davis",
      email: "<EMAIL>",
      phone: "+****************",
      company: "Startup Solutions",
      totalSessions: 3,
      totalSpent: "$450",
      lastSession: "2024-01-08",
      status: "active",
      notes: "New entrepreneur, needs guidance on business planning",
    },
    {
      id: 4,
      name: "Robert Wilson",
      email: "<EMAIL>",
      phone: "+****************",
      company: "Enterprise Corp",
      totalSessions: 8,
      totalSpent: "$1,200",
      lastSession: "2024-01-05",
      status: "inactive",
      notes: "Corporate client, focuses on IT strategy",
    },
  ]

  const upcomingAppointments = [
    {
      id: 5,
      date: "2024-01-17",
      time: "10:00 AM",
      client: "Jennifer Lopez",
      type: "Initial Consultation",
      duration: "60 min",
      status: "confirmed",
      method: "video",
      fee: "$150",
    },
    {
      id: 6,
      date: "2024-01-17",
      time: "02:30 PM",
      client: "David Brown",
      type: "Strategy Session",
      duration: "90 min",
      status: "pending",
      method: "in-person",
      fee: "$225",
    },
    {
      id: 7,
      date: "2024-01-18",
      time: "11:00 AM",
      client: "Lisa Anderson",
      type: "Follow-up",
      duration: "45 min",
      status: "confirmed",
      method: "phone",
      fee: "$120",
    },
  ]

  const billingData = [
    {
      id: 1,
      invoiceNumber: "INV-2024-001",
      client: "Sarah Johnson",
      date: "2024-01-15",
      amount: "$150",
      status: "paid",
      dueDate: "2024-01-30",
      services: "Initial Consultation (60 min)",
    },
    {
      id: 2,
      invoiceNumber: "INV-2024-002",
      client: "Michael Chen",
      date: "2024-01-10",
      amount: "$120",
      status: "paid",
      dueDate: "2024-01-25",
      services: "Follow-up Session (45 min)",
    },
    {
      id: 3,
      invoiceNumber: "INV-2024-003",
      client: "Emily Davis",
      date: "2024-01-08",
      amount: "$200",
      status: "pending",
      dueDate: "2024-01-23",
      services: "Strategy Review (90 min)",
    },
    {
      id: 4,
      invoiceNumber: "INV-2024-004",
      client: "Robert Wilson",
      date: "2024-01-05",
      amount: "$175",
      status: "overdue",
      dueDate: "2024-01-20",
      services: "Technical Consultation (60 min)",
    },
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case "confirmed":
        return "bg-green-100 text-green-800"
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      case "cancelled":
        return "bg-red-100 text-red-800"
      case "completed":
        return "bg-blue-100 text-blue-800"
      case "paid":
        return "bg-green-100 text-green-800"
      case "overdue":
        return "bg-red-100 text-red-800"
      case "active":
        return "bg-green-100 text-green-800"
      case "inactive":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getMethodIcon = (method) => {
    switch (method) {
      case "video":
        return <Video className="h-4 w-4" />
      case "phone":
        return <Phone className="h-4 w-4" />
      case "in-person":
        return <MapPin className="h-4 w-4" />
      default:
        return <Calendar className="h-4 w-4" />
    }
  }

  const filteredClients = clients.filter((client) => {
    const matchesSearch =
      client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.company.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = filterStatus === "all" || client.status === filterStatus
    return matchesSearch && matchesFilter
  })

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Consultation Management</h1>
            <p className="text-gray-600 mt-1">Manage appointments, clients, and consultation workflows</p>
          </div>
          <div className="flex space-x-3">
            <Button onClick={() => setShowNewClient(true)} variant="outline" className="bg-white">
              <User className="h-4 w-4 mr-2" />
              Add Client
            </Button>
            <Button
              onClick={() => setShowNewAppointment(true)}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              Schedule Appointment
            </Button>
          </div>
        </div>

        {/* Notifications */}
        <ConsultationNotifications />

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-green-600">{stat.change}</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Tabs */}
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
            <TabsTrigger value="appointments">Appointments</TabsTrigger>
            <TabsTrigger value="clients">Clients</TabsTrigger>
            <TabsTrigger value="billing">Billing</TabsTrigger>
            <TabsTrigger value="calendar">Calendar</TabsTrigger>
          </TabsList>

          {/* Dashboard Tab */}
          <TabsContent value="dashboard" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Today's Appointments */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Calendar className="h-5 w-5 mr-2 text-blue-600" />
                    Today's Appointments
                  </CardTitle>
                  <CardDescription>{todayAppointments.length} appointments scheduled for today</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {todayAppointments.map((appointment) => (
                      <div
                        key={appointment.id}
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="text-center">
                            <div className="text-sm font-medium text-gray-900">{appointment.time}</div>
                            <div className="text-xs text-gray-500">{appointment.duration}</div>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h4 className="font-medium text-gray-900">{appointment.client}</h4>
                              <Badge className={getStatusColor(appointment.status)}>{appointment.status}</Badge>
                            </div>
                            <p className="text-sm text-gray-600">{appointment.type}</p>
                            <div className="flex items-center space-x-2 mt-1">
                              {getMethodIcon(appointment.method)}
                              <span className="text-xs text-gray-500 capitalize">{appointment.method}</span>
                              <span className="text-xs text-gray-500">•</span>
                              <span className="text-xs font-medium text-green-600">{appointment.fee}</span>
                            </div>
                          </div>
                        </div>
                        <Button size="sm" variant="ghost">
                          <Video className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Upcoming Appointments */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Clock className="h-5 w-5 mr-2 text-purple-600" />
                    Upcoming Appointments
                  </CardTitle>
                  <CardDescription>Next {upcomingAppointments.length} scheduled appointments</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {upcomingAppointments.map((appointment) => (
                      <div
                        key={appointment.id}
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="text-center">
                            <div className="text-sm font-medium text-gray-900">{appointment.date}</div>
                            <div className="text-xs text-gray-500">{appointment.time}</div>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h4 className="font-medium text-gray-900">{appointment.client}</h4>
                              <Badge className={getStatusColor(appointment.status)}>{appointment.status}</Badge>
                            </div>
                            <p className="text-sm text-gray-600">{appointment.type}</p>
                            <div className="flex items-center space-x-2 mt-1">
                              {getMethodIcon(appointment.method)}
                              <span className="text-xs text-gray-500 capitalize">{appointment.method}</span>
                              <span className="text-xs text-gray-500">•</span>
                              <span className="text-xs font-medium text-green-600">{appointment.fee}</span>
                            </div>
                          </div>
                        </div>
                        <Button size="sm" variant="ghost">
                          <Bell className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Zap className="h-5 w-5 mr-2 text-orange-600" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Button
                    onClick={() => setShowNewAppointment(true)}
                    className="h-20 flex flex-col items-center justify-center space-y-2 bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200"
                    variant="outline"
                  >
                    <Calendar className="h-6 w-6" />
                    <span className="text-sm">Schedule Appointment</span>
                  </Button>
                  <Button
                    onClick={() => setShowNewClient(true)}
                    className="h-20 flex flex-col items-center justify-center space-y-2 bg-green-50 hover:bg-green-100 text-green-700 border-green-200"
                    variant="outline"
                  >
                    <User className="h-6 w-6" />
                    <span className="text-sm">Add New Client</span>
                  </Button>
                  <Button
                    className="h-20 flex flex-col items-center justify-center space-y-2 bg-purple-50 hover:bg-purple-100 text-purple-700 border-purple-200"
                    variant="outline"
                  >
                    <FileText className="h-6 w-6" />
                    <span className="text-sm">Generate Invoice</span>
                  </Button>
                  <Button
                    className="h-20 flex flex-col items-center justify-center space-y-2 bg-orange-50 hover:bg-orange-100 text-orange-700 border-orange-200"
                    variant="outline"
                  >
                    <Download className="h-6 w-6" />
                    <span className="text-sm">Export Reports</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Appointments Tab */}
          <TabsContent value="appointments" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>All Appointments</CardTitle>
                <CardDescription>Manage and track all consultation appointments</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Search and Filter */}
                  <div className="flex items-center space-x-4">
                    <div className="relative flex-1">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        placeholder="Search appointments..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    <Select value={filterStatus} onValueChange={setFilterStatus}>
                      <SelectTrigger className="w-48">
                        <SelectValue placeholder="Filter by status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="confirmed">Confirmed</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="cancelled">Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Appointments List */}
                  <div className="space-y-3">
                    {[...todayAppointments, ...upcomingAppointments].map((appointment) => (
                      <div
                        key={appointment.id}
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="text-center min-w-[80px]">
                            <div className="text-sm font-medium text-gray-900">{appointment.date || "Today"}</div>
                            <div className="text-xs text-gray-500">{appointment.time}</div>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h4 className="font-medium text-gray-900">{appointment.client}</h4>
                              <Badge className={getStatusColor(appointment.status)}>{appointment.status}</Badge>
                            </div>
                            <p className="text-sm text-gray-600">{appointment.type}</p>
                            <div className="flex items-center space-x-2 mt-1">
                              {getMethodIcon(appointment.method)}
                              <span className="text-xs text-gray-500 capitalize">{appointment.method}</span>
                              <span className="text-xs text-gray-500">•</span>
                              <span className="text-xs text-gray-500">{appointment.duration}</span>
                              <span className="text-xs text-gray-500">•</span>
                              <span className="text-xs font-medium text-green-600">{appointment.fee}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button size="sm" variant="ghost">
                            Edit
                          </Button>
                          <Button size="sm" variant="ghost">
                            {getMethodIcon(appointment.method)}
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Clients Tab */}
          <TabsContent value="clients" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Client Management</CardTitle>
                <CardDescription>Manage client profiles and consultation history</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Search and Filter */}
                  <div className="flex items-center space-x-4">
                    <div className="relative flex-1">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        placeholder="Search clients..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    <Select value={filterStatus} onValueChange={setFilterStatus}>
                      <SelectTrigger className="w-48">
                        <SelectValue placeholder="Filter by status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Clients</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Clients List */}
                  <div className="space-y-3">
                    {filteredClients.map((client) => (
                      <div
                        key={client.id}
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-medium">
                            {client.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h4 className="font-medium text-gray-900">{client.name}</h4>
                              <Badge className={getStatusColor(client.status)}>{client.status}</Badge>
                            </div>
                            <p className="text-sm text-gray-600">{client.company}</p>
                            <div className="flex items-center space-x-4 mt-1">
                              <span className="text-xs text-gray-500">
                                <Mail className="h-3 w-3 inline mr-1" />
                                {client.email}
                              </span>
                              <span className="text-xs text-gray-500">
                                <Phone className="h-3 w-3 inline mr-1" />
                                {client.phone}
                              </span>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium text-gray-900">{client.totalSpent}</div>
                            <div className="text-xs text-gray-500">{client.totalSessions} sessions</div>
                            <div className="text-xs text-gray-500">Last: {client.lastSession}</div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button size="sm" variant="ghost">
                            View
                          </Button>
                          <Button size="sm" variant="ghost">
                            Schedule
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Billing Tab */}
          <TabsContent value="billing" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Billing & Invoices</CardTitle>
                <CardDescription>Manage consultation fees and payment tracking</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Billing Stats */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">$18,450</div>
                      <div className="text-sm text-green-700">Total Revenue</div>
                    </div>
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">$645</div>
                      <div className="text-sm text-blue-700">Pending Payments</div>
                    </div>
                    <div className="bg-red-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-red-600">$175</div>
                      <div className="text-sm text-red-700">Overdue</div>
                    </div>
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">94.2%</div>
                      <div className="text-sm text-purple-700">Collection Rate</div>
                    </div>
                  </div>

                  {/* Invoices List */}
                  <div className="space-y-3">
                    {billingData.map((invoice) => (
                      <div
                        key={invoice.id}
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="text-center min-w-[100px]">
                            <div className="text-sm font-medium text-gray-900">{invoice.invoiceNumber}</div>
                            <div className="text-xs text-gray-500">{invoice.date}</div>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h4 className="font-medium text-gray-900">{invoice.client}</h4>
                              <Badge className={getStatusColor(invoice.status)}>{invoice.status}</Badge>
                            </div>
                            <p className="text-sm text-gray-600">{invoice.services}</p>
                            <div className="text-xs text-gray-500 mt-1">Due: {invoice.dueDate}</div>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold text-gray-900">{invoice.amount}</div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button size="sm" variant="ghost">
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="ghost">
                            <Mail className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Calendar Tab */}
          <TabsContent value="calendar" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Calendar Integration</CardTitle>
                <CardDescription>Sync appointments with your calendar and manage availability</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Calendar Sync Status */}
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <CalendarDays className="h-6 w-6 text-blue-600" />
                        <div>
                          <h3 className="font-medium text-blue-900">Calendar Sync</h3>
                          <p className="text-sm text-blue-700">Connected to Google Calendar</p>
                        </div>
                      </div>
                      <Badge className="bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Synced
                      </Badge>
                    </div>
                  </div>

                  {/* Availability Settings */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="font-medium text-gray-900 mb-4">Working Hours</h3>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-3 border rounded-lg">
                          <span className="text-sm text-gray-600">Monday - Friday</span>
                          <span className="text-sm font-medium">9:00 AM - 6:00 PM</span>
                        </div>
                        <div className="flex items-center justify-between p-3 border rounded-lg">
                          <span className="text-sm text-gray-600">Saturday</span>
                          <span className="text-sm font-medium">10:00 AM - 2:00 PM</span>
                        </div>
                        <div className="flex items-center justify-between p-3 border rounded-lg">
                          <span className="text-sm text-gray-600">Sunday</span>
                          <span className="text-sm font-medium text-red-600">Closed</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="font-medium text-gray-900 mb-4">Consultation Types</h3>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-3 border rounded-lg">
                          <span className="text-sm text-gray-600">Initial Consultation</span>
                          <span className="text-sm font-medium">60 min - $150</span>
                        </div>
                        <div className="flex items-center justify-between p-3 border rounded-lg">
                          <span className="text-sm text-gray-600">Follow-up Session</span>
                          <span className="text-sm font-medium">45 min - $120</span>
                        </div>
                        <div className="flex items-center justify-between p-3 border rounded-lg">
                          <span className="text-sm text-gray-600">Strategy Review</span>
                          <span className="text-sm font-medium">90 min - $200</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Calendar Actions */}
                  <div className="flex space-x-4">
                    <Button className="bg-blue-600 hover:bg-blue-700">
                      <CalendarDays className="h-4 w-4 mr-2" />
                      Sync Calendar
                    </Button>
                    <Button variant="outline">
                      <Settings className="h-4 w-4 mr-2" />
                      Calendar Settings
                    </Button>
                    <Button variant="outline">
                      <Download className="h-4 w-4 mr-2" />
                      Export Calendar
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* New Appointment Dialog */}
        <Dialog open={showNewAppointment} onOpenChange={setShowNewAppointment}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Schedule New Appointment</DialogTitle>
              <DialogDescription>Create a new consultation appointment with a client</DialogDescription>
            </DialogHeader>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="client">Client</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select client" />
                    </SelectTrigger>
                    <SelectContent>
                      {clients.map((client) => (
                        <SelectItem key={client.id} value={client.id.toString()}>
                          {client.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="date">Date</Label>
                  <Input type="date" />
                </div>
                <div>
                  <Label htmlFor="time">Time</Label>
                  <Input type="time" />
                </div>
                <div>
                  <Label htmlFor="duration">Duration</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select duration" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="30">30 minutes</SelectItem>
                      <SelectItem value="45">45 minutes</SelectItem>
                      <SelectItem value="60">60 minutes</SelectItem>
                      <SelectItem value="90">90 minutes</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="type">Consultation Type</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="initial">Initial Consultation</SelectItem>
                      <SelectItem value="followup">Follow-up Session</SelectItem>
                      <SelectItem value="strategy">Strategy Review</SelectItem>
                      <SelectItem value="technical">Technical Consultation</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="method">Method</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="video">Video Call</SelectItem>
                      <SelectItem value="phone">Phone Call</SelectItem>
                      <SelectItem value="in-person">In-Person</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="fee">Fee</Label>
                  <Input type="number" placeholder="150" />
                </div>
                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea placeholder="Additional notes..." />
                </div>
              </div>
            </div>
            <div className="flex justify-end space-x-2 mt-6">
              <Button variant="outline" onClick={() => setShowNewAppointment(false)}>
                Cancel
              </Button>
              <Button onClick={() => setShowNewAppointment(false)}>Schedule Appointment</Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* New Client Dialog */}
        <Dialog open={showNewClient} onOpenChange={setShowNewClient}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add New Client</DialogTitle>
              <DialogDescription>Create a new client profile for consultation management</DialogDescription>
            </DialogHeader>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Full Name</Label>
                  <Input placeholder="John Doe" />
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input type="email" placeholder="<EMAIL>" />
                </div>
                <div>
                  <Label htmlFor="phone">Phone</Label>
                  <Input placeholder="+****************" />
                </div>
                <div>
                  <Label htmlFor="company">Company</Label>
                  <Input placeholder="Company Name" />
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="industry">Industry</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select industry" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="technology">Technology</SelectItem>
                      <SelectItem value="healthcare">Healthcare</SelectItem>
                      <SelectItem value="finance">Finance</SelectItem>
                      <SelectItem value="retail">Retail</SelectItem>
                      <SelectItem value="manufacturing">Manufacturing</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="source">How did they find us?</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select source" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="referral">Referral</SelectItem>
                      <SelectItem value="website">Website</SelectItem>
                      <SelectItem value="social">Social Media</SelectItem>
                      <SelectItem value="advertising">Advertising</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea placeholder="Additional client information..." className="h-20" />
                </div>
              </div>
            </div>
            <div className="flex justify-end space-x-2 mt-6">
              <Button variant="outline" onClick={() => setShowNewClient(false)}>
                Cancel
              </Button>
              <Button onClick={() => setShowNewClient(false)}>Add Client</Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}
