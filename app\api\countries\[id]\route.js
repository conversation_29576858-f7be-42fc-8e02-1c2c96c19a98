import { NextResponse } from "next/server"

// Mock data - replace with actual database operations
const countries = [
  { id: "1", name: "United States", code: "US", currency_code: "USD", phone_code: "+1", is_active: true },
  { id: "2", name: "India", code: "IN", currency_code: "INR", phone_code: "+91", is_active: true },
  { id: "3", name: "United Kingdom", code: "GB", currency_code: "GBP", phone_code: "+44", is_active: true },
]

export async function GET(request, { params }) {
  try {
    const country = countries.find((c) => c.id === params.id)

    if (!country) {
      return NextResponse.json({ error: "Country not found" }, { status: 404 })
    }

    return NextResponse.json({ data: country })
  } catch (error) {
    return NextResponse.json({ error: "Failed to fetch country" }, { status: 500 })
  }
}

export async function PUT(request, { params }) {
  try {
    const body = await request.json()
    const countryIndex = countries.findIndex((c) => c.id === params.id)

    if (countryIndex === -1) {
      return NextResponse.json({ error: "Country not found" }, { status: 404 })
    }

    countries[countryIndex] = {
      ...countries[countryIndex],
      ...body,
      updated_at: new Date().toISOString(),
    }

    return NextResponse.json({ data: countries[countryIndex] })
  } catch (error) {
    return NextResponse.json({ error: "Failed to update country" }, { status: 500 })
  }
}

export async function DELETE(request, { params }) {
  try {
    const countryIndex = countries.findIndex((c) => c.id === params.id)

    if (countryIndex === -1) {
      return NextResponse.json({ error: "Country not found" }, { status: 404 })
    }

    countries.splice(countryIndex, 1)

    return NextResponse.json({ message: "Country deleted successfully" })
  } catch (error) {
    return NextResponse.json({ error: "Failed to delete country" }, { status: 500 })
  }
}
