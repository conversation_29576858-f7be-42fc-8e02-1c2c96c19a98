"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { UserCheck, Bell, X, Settings, Volume2, VolumeX, Target, Calendar, TrendingUp, Star } from "lucide-react"

export default function CRMNotifications() {
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      type: "crm",
      title: "New High-Value Lead",
      message: "<PERSON> from TechCorp Solutions has been qualified as a hot lead with $25K potential value.",
      timestamp: new Date(Date.now() - 10 * 60 * 1000),
      read: false,
      priority: "high",
      category: "lead",
    },
    {
      id: 2,
      type: "crm",
      title: "Deal Moved to Negotiation",
      message: "Innovate Labs deal worth $45K has progressed to negotiation stage.",
      timestamp: new Date(Date.now() - 25 * 60 * 1000),
      read: false,
      priority: "medium",
      category: "deal",
    },
    {
      id: 3,
      type: "crm",
      title: "Follow-up Reminder",
      message: "Scheduled follow-up call with Enterprise Systems is due in 30 minutes.",
      timestamp: new Date(Date.now() - 45 * 60 * 1000),
      read: true,
      priority: "medium",
      category: "activity",
    },
    {
      id: 4,
      type: "crm",
      title: "Lead Score Updated",
      message: "Growth Partners lead score increased to 88 based on recent engagement.",
      timestamp: new Date(Date.now() - 90 * 60 * 1000),
      read: false,
      priority: "low",
      category: "scoring",
    },
    {
      id: 5,
      type: "crm",
      title: "Workflow Triggered",
      message: "New lead welcome sequence started for 3 new prospects from website.",
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      read: false,
      priority: "low",
      category: "workflow",
    },
  ])

  const [soundEnabled, setSoundEnabled] = useState(true)
  const [showToast, setShowToast] = useState(false)

  useEffect(() => {
    // Simulate real-time CRM notifications
    const interval = setInterval(() => {
      const crmNotifications = [
        {
          title: "New Lead Captured",
          message: "Website form submission from potential enterprise client.",
          category: "lead",
        },
        {
          title: "Deal Stage Updated",
          message: "Proposal sent to qualified prospect, moved to proposal stage.",
          category: "deal",
        },
        {
          title: "Meeting Scheduled",
          message: "Demo meeting booked with high-priority lead for next week.",
          category: "activity",
        },
        {
          title: "Lead Engagement Alert",
          message: "Prospect opened proposal email and visited pricing page 3 times.",
          category: "scoring",
        },
      ]

      const randomNotification = crmNotifications[Math.floor(Math.random() * crmNotifications.length)]

      const newNotification = {
        id: Date.now(),
        type: "crm",
        title: randomNotification.title,
        message: randomNotification.message,
        timestamp: new Date(),
        read: false,
        priority: "medium",
        category: randomNotification.category,
      }

      setNotifications((prev) => [newNotification, ...prev])
      setShowToast(true)

      if (soundEnabled) {
        console.log("🎯 CRM notification sound played")
      }

      setTimeout(() => setShowToast(false), 5000)
    }, 40000) // New notification every 40 seconds

    return () => clearInterval(interval)
  }, [soundEnabled])

  const markAsRead = (id) => {
    setNotifications((prev) => prev.map((notif) => (notif.id === id ? { ...notif, read: true } : notif)))
  }

  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((notif) => ({ ...notif, read: true })))
  }

  const removeNotification = (id) => {
    setNotifications((prev) => prev.filter((notif) => notif.id !== id))
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "critical":
        return "bg-red-100 text-red-800 border-red-200"
      case "high":
        return "bg-orange-100 text-orange-800 border-orange-200"
      case "medium":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "low":
        return "bg-green-100 text-green-800 border-green-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getCategoryIcon = (category) => {
    switch (category) {
      case "lead":
        return <UserCheck className="h-4 w-4 text-blue-600" />
      case "deal":
        return <Target className="h-4 w-4 text-green-600" />
      case "activity":
        return <Calendar className="h-4 w-4 text-purple-600" />
      case "scoring":
        return <Star className="h-4 w-4 text-yellow-600" />
      case "workflow":
        return <TrendingUp className="h-4 w-4 text-indigo-600" />
      default:
        return <Bell className="h-4 w-4 text-gray-600" />
    }
  }

  const formatTimestamp = (timestamp) => {
    const now = new Date()
    const diff = now - timestamp
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)

    if (minutes < 1) return "Just now"
    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    return `${days}d ago`
  }

  const unreadCount = notifications.filter((n) => !n.read).length

  return (
    <div className="space-y-4">
      {/* Toast Notification */}
      {showToast && (
        <div className="fixed top-4 right-4 z-50 animate-in slide-in-from-top-2">
          <Alert className="w-80 border-blue-200 bg-blue-50">
            <UserCheck className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">New CRM notification received!</AlertDescription>
          </Alert>
        </div>
      )}

      {/* Notification Panel */}
      <Card className="w-full max-w-md">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <UserCheck className="h-5 w-5 text-blue-600" />
              <CardTitle className="text-lg">CRM Notifications</CardTitle>
              {unreadCount > 0 && <Badge className="bg-red-500 hover:bg-red-500 text-white">{unreadCount}</Badge>}
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" onClick={() => setSoundEnabled(!soundEnabled)} className="h-8 w-8 p-0">
                {soundEnabled ? (
                  <Volume2 className="h-4 w-4 text-green-600" />
                ) : (
                  <VolumeX className="h-4 w-4 text-gray-400" />
                )}
              </Button>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <CardDescription>Stay updated with leads, deals, and customer activities</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">{unreadCount} unread notifications</span>
            {unreadCount > 0 && (
              <Button variant="ghost" size="sm" onClick={markAllAsRead}>
                Mark all as read
              </Button>
            )}
          </div>

          <div className="space-y-2 max-h-96 overflow-y-auto">
            {notifications.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <UserCheck className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                <p>No CRM notifications yet</p>
              </div>
            ) : (
              notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-3 rounded-lg border transition-all hover:shadow-sm ${
                    notification.read ? "bg-gray-50 border-gray-200" : "bg-white border-blue-200 shadow-sm"
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className="mt-0.5">{getCategoryIcon(notification.category)}</div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4
                            className={`text-sm font-medium truncate ${
                              notification.read ? "text-gray-700" : "text-gray-900"
                            }`}
                          >
                            {notification.title}
                          </h4>
                          <Badge variant="outline" className={`text-xs ${getPriorityColor(notification.priority)}`}>
                            {notification.priority}
                          </Badge>
                        </div>
                        <p className={`text-sm mb-2 ${notification.read ? "text-gray-500" : "text-gray-700"}`}>
                          {notification.message}
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">{formatTimestamp(notification.timestamp)}</span>
                          <div className="flex items-center space-x-1">
                            {!notification.read && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => markAsRead(notification.id)}
                                className="h-6 px-2 text-xs text-blue-600 hover:text-blue-700"
                              >
                                Mark read
                              </Button>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeNotification(notification.id)}
                              className="h-6 w-6 p-0 text-gray-400 hover:text-red-600"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {notifications.length > 0 && (
            <div className="pt-3 border-t">
              <Button variant="outline" className="w-full bg-transparent" size="sm">
                <UserCheck className="h-4 w-4 mr-2" />
                View All CRM Notifications
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
