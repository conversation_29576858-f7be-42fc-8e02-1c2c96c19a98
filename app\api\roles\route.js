import { NextResponse } from "next/server"

// Mock data - replace with actual database
const roles = [
  {
    id: "1",
    name: "admin",
    displayName: "Administrator",
    description: "Full system access with all permissions",
    permissions: ["*"], // All permissions
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "2",
    name: "project_manager",
    displayName: "Project Manager",
    description: "Manages projects through all phases",
    permissions: [
      "create_propose_idea",
      "create_edit_proposal",
      "select_request_review",
      "plan_create_business_case",
      "plan_edit_business_case",
      "manage_start_project",
      "manage_suspend_project",
      "manage_complete_project",
      "manage_assign_team",
      "view_project",
      "edit_project",
      "view_financials",
      "manage_team",
      "view_reports",
    ],
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "3",
    name: "portfolio_manager",
    displayName: "Portfolio Manager",
    description: "Manages project portfolio and approvals",
    permissions: [
      "create_review_idea",
      "create_approve_request",
      "select_portfolio_selection",
      "select_complete_selection",
      "select_reject_project",
      "plan_review_business_case",
      "plan_approve_business_case",
      "plan_deny_business_case",
      "view_project",
      "view_financials",
      "view_reports",
    ],
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
]

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url)
    const isActive = searchParams.get("active")

    let filteredRoles = roles

    if (isActive !== null) {
      filteredRoles = roles.filter((role) => role.isActive === (isActive === "true"))
    }

    return NextResponse.json({
      success: true,
      data: filteredRoles,
      total: filteredRoles.length,
    })
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to fetch roles" }, { status: 500 })
  }
}

export async function POST(request) {
  try {
    const body = await request.json()
    const { name, displayName, description, permissions } = body

    // Validate required fields
    if (!name || !displayName || !permissions) {
      return NextResponse.json({ success: false, error: "Missing required fields" }, { status: 400 })
    }

    // Check if role already exists
    const existingRole = roles.find((role) => role.name === name)
    if (existingRole) {
      return NextResponse.json({ success: false, error: "Role already exists" }, { status: 409 })
    }

    const newRole = {
      id: (roles.length + 1).toString(),
      name,
      displayName,
      description: description || "",
      permissions: Array.isArray(permissions) ? permissions : [],
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    roles.push(newRole)

    return NextResponse.json(
      {
        success: true,
        data: newRole,
        message: "Role created successfully",
      },
      { status: 201 },
    )
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to create role" }, { status: 500 })
  }
}
