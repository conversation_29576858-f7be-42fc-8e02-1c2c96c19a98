"use client"

import { useState, useEffect, useCallback } from "react"
import { useRouter } from "next/navigation"
import { useSpeechRecognition } from "./use-speech-recognition"

export function useVoiceCommands() {
  const router = useRouter()
  const { transcript, isListening, startListening, stopListening, resetTranscript, isSupported } =
    useSpeechRecognition()
  const [lastCommand, setLastCommand] = useState("")
  const [commandHistory, setCommandHistory] = useState([])
  const [isProcessing, setIsProcessing] = useState(false)

  // Voice command patterns
  const commands = {
    navigation: {
      "go to dashboard": () => router.push("/dashboard"),
      "open dashboard": () => router.push("/dashboard"),
      "go to users": () => router.push("/dashboard/users"),
      "open user management": () => router.push("/dashboard/users"),
      "go to tickets": () => router.push("/dashboard/tickets"),
      "open ticket management": () => router.push("/dashboard/tickets"),
      "go to payroll": () => router.push("/dashboard/payroll"),
      "open payroll management": () => router.push("/dashboard/payroll"),
      "go to projects": () => router.push("/dashboard/projects"),
      "open project management": () => router.push("/dashboard/projects"),
      "go to customers": () => router.push("/dashboard/customers"),
      "open customer management": () => router.push("/dashboard/customers"),
      "go to assets": () => router.push("/dashboard/assets"),
      "open asset management": () => router.push("/dashboard/assets"),
      "go to analytics": () => router.push("/dashboard/analytics"),
      "open analytics": () => router.push("/dashboard/analytics"),
      "go to consultations": () => router.push("/dashboard/consultations"),
      "open consultation management": () => router.push("/dashboard/consultations"),
      "go back": () => window.history.back(),
      "go home": () => router.push("/dashboard"),
    },
    actions: {
      "create new user": () => router.push("/dashboard/users/new"),
      "add new user": () => router.push("/dashboard/users/new"),
      "create ticket": () => router.push("/dashboard/tickets/new"),
      "new ticket": () => router.push("/dashboard/tickets/new"),
      "create project": () => router.push("/dashboard/projects/new"),
      "new project": () => router.push("/dashboard/projects/new"),
      "add asset": () => router.push("/dashboard/assets/new"),
      "create asset": () => router.push("/dashboard/assets/new"),
      "schedule appointment": () => router.push("/dashboard/consultations/new"),
      "new appointment": () => router.push("/dashboard/consultations/new"),
      "refresh page": () => window.location.reload(),
      reload: () => window.location.reload(),
    },
    search: {
      "search for": (query) => {
        const searchInput = document.querySelector('input[type="search"]')
        if (searchInput) {
          searchInput.value = query
          searchInput.dispatchEvent(new Event("input", { bubbles: true }))
        }
      },
    },
    system: {
      help: () => showHelp(),
      "what can i say": () => showHelp(),
      "voice commands": () => showHelp(),
      "stop listening": () => stopListening(),
      cancel: () => {
        stopListening()
        resetTranscript()
      },
    },
  }

  const showHelp = () => {
    const helpCommands = [
      "Navigation: 'Go to dashboard', 'Open users', 'Go to tickets'",
      "Actions: 'Create new user', 'New ticket', 'Add asset'",
      "Search: 'Search for [query]'",
      "System: 'Help', 'Stop listening', 'Cancel'",
    ]

    // Create and show help modal or notification
    if ("speechSynthesis" in window) {
      const utterance = new SpeechSynthesisUtterance(
        "Here are some voice commands you can use: " + helpCommands.join(". "),
      )
      utterance.rate = 0.8
      utterance.pitch = 1
      speechSynthesis.speak(utterance)
    }
  }

  const processCommand = useCallback(
    (command) => {
      setIsProcessing(true)
      const normalizedCommand = command.toLowerCase().trim()
      setLastCommand(normalizedCommand)

      // Add to command history
      setCommandHistory((prev) => [
        ...prev.slice(-9),
        {
          command: normalizedCommand,
          timestamp: new Date(),
          executed: false,
        },
      ])

      let commandExecuted = false

      // Check navigation commands
      for (const [pattern, action] of Object.entries(commands.navigation)) {
        if (normalizedCommand.includes(pattern)) {
          action()
          commandExecuted = true
          break
        }
      }

      // Check action commands
      if (!commandExecuted) {
        for (const [pattern, action] of Object.entries(commands.actions)) {
          if (normalizedCommand.includes(pattern)) {
            action()
            commandExecuted = true
            break
          }
        }
      }

      // Check search commands
      if (!commandExecuted && normalizedCommand.startsWith("search for ")) {
        const query = normalizedCommand.replace("search for ", "")
        commands.search["search for"](query)
        commandExecuted = true
      }

      // Check system commands
      if (!commandExecuted) {
        for (const [pattern, action] of Object.entries(commands.system)) {
          if (normalizedCommand.includes(pattern)) {
            action()
            commandExecuted = true
            break
          }
        }
      }

      // Update command history with execution status
      setCommandHistory((prev) => {
        const updated = [...prev]
        if (updated.length > 0) {
          updated[updated.length - 1].executed = commandExecuted
        }
        return updated
      })

      // Provide audio feedback
      if ("speechSynthesis" in window) {
        const utterance = new SpeechSynthesisUtterance(commandExecuted ? "Command executed" : "Command not recognized")
        utterance.rate = 1.2
        utterance.pitch = commandExecuted ? 1.2 : 0.8
        utterance.volume = 0.5
        speechSynthesis.speak(utterance)
      }

      setIsProcessing(false)
      resetTranscript()
    },
    [router, resetTranscript, stopListening],
  )

  // Process transcript when it changes
  useEffect(() => {
    if (transcript && !isListening && !isProcessing) {
      processCommand(transcript)
    }
  }, [transcript, isListening, isProcessing, processCommand])

  const startVoiceCommand = useCallback(() => {
    if (isSupported) {
      startListening()

      // Provide audio feedback
      if ("speechSynthesis" in window) {
        const utterance = new SpeechSynthesisUtterance("Listening for command")
        utterance.rate = 1.2
        utterance.pitch = 1.2
        utterance.volume = 0.5
        speechSynthesis.speak(utterance)
      }
    }
  }, [isSupported, startListening])

  return {
    isListening,
    isProcessing,
    lastCommand,
    commandHistory,
    isSupported,
    startVoiceCommand,
    stopListening,
    showHelp,
  }
}
